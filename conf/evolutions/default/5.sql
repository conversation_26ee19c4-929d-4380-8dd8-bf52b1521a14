# --- !Ups

CREATE INDEX IF NOT EXISTS wb_email_accounts_account_id ON email_accounts (account_id);
CREATE INDEX IF NOT EXISTS wb_email_accounts_email ON email_accounts (email);
CREATE INDEX IF NOT EXISTS wb_email_accounts_email_domain ON email_accounts (email_domain);
CREATE INDEX IF NOT EXISTS wb_email_accounts_service_provider ON email_accounts (service_provider);
CREATE INDEX IF NOT EXISTS wb_email_accounts_in_queue_for_scheduling ON email_accounts (in_queue_for_scheduling);
CREATE INDEX IF NOT EXISTS wb_email_accounts_paused_till ON email_accounts (paused_till);

CREATE INDEX IF NOT EXISTS wb_emails_scheduled_warmup_email_account_id ON emails_scheduled (warmup_email_account_id);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_sender_email_account_id ON emails_scheduled (sender_email_account_id);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_receiver_email_account_id ON emails_scheduled (receiver_email_account_id);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_account_id ON emails_scheduled (account_id);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_template_id ON emails_scheduled (template_id);

CREATE INDEX IF NOT EXISTS wb_emails_scheduled_message_id ON emails_scheduled (message_id);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_email_thread_id ON emails_scheduled (email_thread_id);

CREATE INDEX IF NOT EXISTS wb_emails_scheduled_sent ON emails_scheduled (sent);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_sent_at ON emails_scheduled (sent_at);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_replied ON emails_scheduled (replied);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_scheduled_at ON emails_scheduled (scheduled_at);

CREATE INDEX IF NOT EXISTS wb_emails_scheduled_is_reply ON emails_scheduled (is_reply);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_is_scheduled_for_sending ON emails_scheduled (is_scheduled_for_sending);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_is_scheduled_for_landing_check ON emails_scheduled (is_scheduled_for_landing_check);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_is_scheduled_for_reply ON emails_scheduled (is_scheduled_for_reply);
CREATE INDEX IF NOT EXISTS wb_emails_scheduled_landed_folder_type ON emails_scheduled (landed_folder_type);

# --- !Downs

DROP INDEX IF EXISTS wb_email_accounts_account_id;
DROP INDEX IF EXISTS wb_email_accounts_email;
DROP INDEX IF EXISTS wb_email_accounts_email_domain;
DROP INDEX IF EXISTS wb_email_accounts_service_provider;
DROP INDEX IF EXISTS wb_email_accounts_in_queue_for_scheduling;
DROP INDEX IF EXISTS wb_email_accounts_paused_till;

DROP INDEX IF EXISTS wb_emails_scheduled_warmup_email_account_id;
DROP INDEX IF EXISTS wb_emails_scheduled_sender_email_account_id;
DROP INDEX IF EXISTS wb_emails_scheduled_receiver_email_account_id;
DROP INDEX IF EXISTS wb_emails_scheduled_account_id;
DROP INDEX IF EXISTS wb_emails_scheduled_template_id;

DROP INDEX IF EXISTS wb_emails_scheduled_message_id;
DROP INDEX IF EXISTS wb_emails_scheduled_email_thread_id;

DROP INDEX IF EXISTS wb_emails_scheduled_sent;
DROP INDEX IF EXISTS wb_emails_scheduled_sent_at;
DROP INDEX IF EXISTS wb_emails_scheduled_replied;
DROP INDEX IF EXISTS wb_emails_scheduled_scheduled_at;

DROP INDEX IF EXISTS wb_emails_scheduled_is_reply;
DROP INDEX IF EXISTS wb_emails_scheduled_is_scheduled_for_sending;
DROP INDEX IF EXISTS wb_emails_scheduled_is_scheduled_for_landing_check;
DROP INDEX IF EXISTS wb_emails_scheduled_is_scheduled_for_reply;
DROP INDEX IF EXISTS wb_emails_scheduled_landed_folder_type;
