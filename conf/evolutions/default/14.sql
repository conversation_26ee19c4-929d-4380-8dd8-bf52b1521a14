# --- !Ups

ALTER TABLE email_accounts
	ADD COLUMN IF NOT EXISTS oauth2_token_type TEXT,
	ADD COLUMN IF NOT EXISTS oauth2_access_token_expires_in integer,
	ADD COLUMN IF NOT EXISTS oauth2_access_token_expires_at TIMESTAMPTZ,
	ADD COLUMN IF NOT EXISTS oauth2_access_token_updated_at TIMESTAMPTZ,
	ADD COLUMN IF NOT EXISTS oauth2_access_token_enc text,
	ADD COLUMN IF NOT EXISTS oauth2_refresh_token_enc text;


# --- !Downs

ALTER TABLE email_accounts
   DROP COLUMN IF EXISTS oauth2_token_type,
   DROP COLUMN IF EXISTS oauth2_access_token_expires_in,
   DROP COLUMN IF EXISTS oauth2_access_token_expires_at,
   DROP COLUMN IF EXISTS oauth2_access_token_updated_at,
   DROP COLUMN IF EXISTS oauth2_access_token_enc,
   DROP COLUMN IF EXISTS oauth2_refresh_token_enc;
