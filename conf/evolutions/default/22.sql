# --- !Ups

ALTER TABLE email_template_body
	ADD COLUMN IF NOT EXISTS is_reply BOOLEAN,
	ADD COLUMN IF NOT EXISTS prompt_token_count INTEGER,
	ADD COLUMN IF NOT EXISTS generation_token_count INTEGER,
	ADD COLUMN IF NOT EXISTS subject_template_id BIGINT R<PERSON>ERENCES email_templates (id) ON DELETE SET NULL,
	ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT now();


# --- !Downs

ALTER TABLE email_template_body
	DROP COLUMN IF EXISTS created_at,
	DROP COLUMN IF EXISTS subject_template_id,
	DROP COLUMN IF EXISTS generation_token_count,
	DROP COLUMN IF EXISTS prompt_token_count,
	DROP COLUMN IF EXISTS is_reply;
