# --- !Ups


CREATE TABLE IF NOT EXISTS email_accounts (
    id BIGSERIAL PRIMARY KEY,
    email text NOT NULL,
    service_provider text NOT NULL,
    smtp_username text,
    smtp_password text,
    smtp_host text,
    smtp_port integer,
    smtp_enable_ssl boolean,
    imap_username text,
    imap_password text,
    imap_host text,
    imap_port integer,
    imap_enable_ssl boolean,
    sender_name text NOT NULL,
    error text,
    error_reported_at timestamp with time zone,
    active boolean NOT NULL DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    in_queue_for_scheduling boolean DEFAULT false,
    pushed_to_queue_for_scheduling_at timestamp with time zone,
    latest_email_scheduled_at timestamp with time zone,
    auth_failed_count integer DEFAULT 0,
    paused_till timestamp with time zone,
    account_id bigint REFERENCES accounts(id),
    warm_up_emails_per_day integer DEFAULT 30,
    warm_up_emails_increase_per_day integer DEFAULT 2,
    signature text NOT NULL DEFAULT ''::text,
    first_name text NOT NULL DEFAULT ''::text,
    last_name text NOT NULL DEFAULT ''::text,
    email_domain text NOT NULL,
    message_id_suffix text NOT NULL
);


CREATE UNIQUE INDEX IF NOT EXISTS email_accounts_account_id_email_key ON email_accounts (account_id, email, service_provider);

# --- !Downs

DROP INDEX IF EXISTS email_accounts_account_id_email_key;

DROP TABLE IF EXISTS email_accounts;
