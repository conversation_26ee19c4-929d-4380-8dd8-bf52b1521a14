# --- !Ups

ALTER TABLE email_accounts
	ADD COLUMN IF NOT EXISTS last_used_max_sends_per_day integer NOT NULL DEFAULT 0,
	ADD COLUMN IF NOT EXISTS warm_up_emails_increase_per_day_updated_at timestamptz NOT NULL DEFAULT now();

UPDATE
	email_accounts
SET
	warm_up_emails_increase_per_day_updated_at = created_at;


# --- !Downs

ALTER TABLE email_accounts
	DROP COLUMN IF EXISTS last_used_max_sends_per_day,
	DROP COLUMN IF EXISTS warm_up_emails_increase_per_day_updated_at;
