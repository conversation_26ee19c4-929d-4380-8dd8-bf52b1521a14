# --- !Ups

CREATE TABLE IF NOT EXISTS email_templates (
    id SERIAL PRIMARY KEY,
    subject text,
    message_body text,
    reply_body text
);

CREATE TABLE IF NOT EXISTS email_template_salutations (
    id BIGSERIAL PRIMARY KEY,
    salutation text NOT NULL
);

CREATE TABLE IF NOT EXISTS email_template_ending_remarks (
    id BIGSERIAL PRIMARY KEY,
    ending_remark text NOT NULL
);

# --- !Downs

DROP TABLE IF EXISTS email_templates;
DROP TABLE IF EXISTS email_template_salutations;
DROP TABLE IF EXISTS email_template_ending_remarks;
