# --- !Ups


CREATE TABLE IF NOT EXISTS emails_scheduled (
    id SERIAL PRIMARY KEY,
    body text,
    subject text,
    sender_email_account_id bigint REFERENCES email_accounts(id) ON DELETE SET NULL,
    receiver_email_account_id bigint REFERENCES email_accounts(id) ON DELETE SET NULL,
    message_id text,
    template_id bigint REFERENCES email_templates(id) ON DELETE SET NULL,
    sent boolean DEFAULT false,
    sent_at timestamp with time zone,
    is_reply boolean DEFAULT false,
    is_scheduled_for_reply boolean DEFAULT false,
    is_scheduled_for_reply_at timestamp with time zone,
    replied boolean DEFAULT false,
    replied_at timestamp with time zone,
    original_landed_folder text,
    landed_folder text,
    account_id bigint REFERENCES accounts(id),
    is_scheduled_for_landing_check boolean DEFAULT false,
    is_scheduled_for_landing_check_at timestamp with time zone,
    is_scheduled_for_sending boolean DEFAULT false,
    is_scheduled_for_sending_at timestamp with time zone,
    text_body text,
    scheduled_at timestamp with time zone NOT NULL DEFAULT now(),
    added_at timestamp with time zone NOT NULL DEFAULT now(),
    from_email text NOT NULL,
    from_name text,
    to_email text NOT NULL,
    to_name text,
    landed_folder_type folder_type_enum,
    email_thread_id text NOT NULL,
    in_reply_to_header text,
    references_header text,
    warmup_email_account_id bigint REFERENCES email_accounts(id) ON DELETE SET NULL REFERENCES email_accounts(id) ON DELETE SET NULL
);

# --- !Downs

DROP TABLE IF EXISTS emails_scheduled;
