# --- !Ups

ALTER TABLE emails_scheduled
	ADD COLUMN IF NOT EXISTS has_email_bounced boolean NOT NULL DEFAULT FALSE,
	ADD COLUMN IF NOT EXISTS bounce_type text,
	ADD COLUMN IF NOT EXISTS landing_check_performed_at timestamptz,
	ADD COLUMN IF NOT EXISTS landing_check_tried_count integer NOT NULL DEFAULT 0;

-- We won't be able to drop the values added to the enum type

-- ALTER TYPE folder_type_enum
-- 	ADD VALUE IF NOT EXISTS 'bounced';

-- ALTER TYPE folder_type_enum
-- 	ADD VALUE IF NOT EXISTS 'landing_check_failed';

# --- !Downs

ALTER TABLE emails_scheduled
	DROP COLUMN IF EXISTS has_email_bounced,
	DROP COLUMN IF EXISTS bounce_type,
	DROP COLUMN IF EXISTS landing_check_performed_at,
	DROP COLUMN IF EXISTS landing_check_tried_count;
