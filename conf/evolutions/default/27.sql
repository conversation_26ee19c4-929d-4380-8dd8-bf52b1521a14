# --- !Ups

CREATE TABLE IF NOT EXISTS email_health_check_records (
	id bigserial PRIMARY KEY,

	account_id bigint REFERENCES accounts (id) ON DELETE CASCADE NOT NULL,
	email_account_id bigint REFERENCES email_accounts (id) ON DELETE CASCADE NOT NULL,

	spf_auth_status text NOT NULL,
	dkim_auth_status text NOT NULL,
	dmarc_auth_status text NOT NULL,

	dkim_selector text,

	spf_record text,
	dkim_record text,
	dmarc_record text,

	status text NOT NULL,
	retry_count bigint NOT NULL DEFAULT 0,
	
	updated_at timestamptz NOT NULL DEFAULT now(),
	created_at timestamptz NOT NULL DEFAULT now()
);

CREATE UNIQUE INDEX IF NOT EXISTS 
    email_health_check_account_id_email_account_id_unique 
    ON email_health_check_records (account_id, email_account_id);

# --- !Downs

DROP INDEX IF EXISTS email_health_check_team_id_email_setting_id;

DROP TABLE IF EXISTS email_health_check_records;
