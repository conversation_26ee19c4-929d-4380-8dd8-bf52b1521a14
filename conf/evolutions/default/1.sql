# --- !Ups

CREATE TABLE IF NOT EXISTS accounts
(
    id BIGSE<PERSON>AL PRIMARY KEY,
    email text NOT NULL UNIQUE,
    password text NOT NULL,
    first_name text,
    last_name text,
    sr_api_key text,
    updated_at TIMESTAMPTZ DEFAULT now(),
    created_at TIMESTAMPTZ DEFAULT now(),
    active boolean DEFAULT true,
    email_verified boolean DEFAULT false,
    email_verification_code text,
    email_verification_code_created_at TIMESTAMPTZ
);

# --- !Downs

DROP TABLE accounts;