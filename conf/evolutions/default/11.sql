# --- !Ups

CREATE INDEX IF NOT EXISTS wb_emails_scheduled_find_receiver_1 ON emails_scheduled(sender_email_account_id, is_reply, scheduled_at);

CREATE INDEX IF NOT EXISTS wb_emails_scheduled_find_receiver_2 ON emails_scheduled(sender_email_account_id, is_reply, sent_at);


CREATE INDEX IF NOT EXISTS
    wb_emails_scheduled_ready_for_reply_1
    ON emails_scheduled(
        is_reply,
        sent,
        replied,
        is_scheduled_for_sending,
        is_scheduled_for_landing_check,
        is_scheduled_for_landing_check_at,
        is_scheduled_for_reply,
        is_scheduled_for_reply_at,
        original_landed_folder,
        landed_folder
        )
    WHERE
        sent = TRUE AND
        replied = FALSE AND
        landed_folder IS NOT NULL AND
        original_landed_folder IS NOT NULL AND
        is_reply = FALSE
;

# --- !Downs

DROP INDEX IF EXISTS wb_emails_scheduled_ready_for_reply_1;

DROP INDEX IF EXISTS wb_emails_scheduled_find_receiver_2;

DROP INDEX IF EXISTS wb_emails_scheduled_find_receiver_1;