<!-- https://www.playframework.com/documentation/latest/SettingsLogger -->
<configuration>

    <conversionRule conversionWord="coloredLevel" converterClass="play.api.libs.logback.ColoredLevel"/>

<!--    <appender name="loggly" class="ch.qos.logback.ext.loggly.LogglyAppender">-->
<!--        <endpointUrl>https://logs-01.loggly.com/inputs/************************************/tag/smartreach_production-->
<!--        </endpointUrl>-->
<!--        <pattern>%m%n</pattern>-->
<!--    </appender>-->

    <!--<appender name="loggly2" class="ch.qos.logback.ext.loggly.LogglyAppender">-->
        <!--<endpointUrl>https://logs-01.loggly.com/inputs/************************************/tag/logback</endpointUrl>-->
        <!--&lt;!&ndash;<pattern>%d{"ISO8601", UTC}  %p %t %c{0}.%M - %m%n</pattern>&ndash;&gt;-->
        <!--<pattern>%m%n</pattern>-->
    <!--</appender>-->


    <!-- REF: https://www.baeldung.com/java-logging-rolling-file-appenders#4-rolling-based-on-size-and-time-1-->
    <!--    <appender name="roll-by-time-and-size"-->
    <!--              class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        <file>/home/<USER>/srlogs/sr_app.log</file>-->
    <!--        <rollingPolicy-->
    <!--                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
    <!--            <fileNamePattern>-->
    <!--                /home/<USER>/srlogs/sr_app.%d{yyyy-MM-dd}.%i.gz-->
    <!--            </fileNamePattern>-->
    <!--            <maxFileSize>10MB</maxFileSize>-->
    <!--            <maxHistory>30</maxHistory>-->
    <!--            <totalSizeCap>500MB</totalSizeCap>-->
    <!--        </rollingPolicy>-->
    <!--        <encoder>-->
    <!--            <pattern>%d{yyyy-MM-dd HH:mm:ss} %p %m%n</pattern>-->
    <!--        </encoder>-->
    <!--    </appender>-->

    <!-- REF: https://cloud.google.com/logging/docs/setup/java-->
    <appender name="gcloud" class="com.google.cloud.logging.logback.LoggingAppender">
        <!-- Optional : filter logs at or above a level -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <!--        <log>application.log</log> &lt;!&ndash; Optional : default java.log &ndash;&gt;-->
        <!--        <resourceType>gae_app</resourceType> &lt;!&ndash; Optional : default: auto-detected, fallback: global &ndash;&gt;-->
        <!--        <enhancer>com.example.logging.logback.enhancers.ExampleEnhancer</enhancer> &lt;!&ndash; Optional &ndash;&gt;-->
        <flushLevel>WARN</flushLevel> <!-- Optional : default ERROR -->
    </appender>

    <logger name="play" level="INFO"/>
    <logger name="application" level="DEBUG"/>

    <!-- Off these ones as they are annoying, and anyway we manage configuration ourselves -->
    <logger name="com.avaje.ebean.config.PropertyMapLoader" level="OFF"/>
    <logger name="com.avaje.ebeaninternal.server.core.XmlConfigLoader" level="OFF"/>
    <logger name="com.avaje.ebeaninternal.server.lib.BackgroundThread" level="OFF"/>
    <logger name="com.gargoylesoftware.htmlunit.javascript" level="OFF"/>

    <root level="INFO">
        <appender-ref ref="gcloud"/>

        <!--        <appender-ref ref="loggly"/>-->
<!--        <appender-ref ref="roll-by-time-and-size"/>-->
        <!--<appender-ref ref="loggly2"/>-->
    </root>

</configuration>
