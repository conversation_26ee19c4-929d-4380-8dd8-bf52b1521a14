# This is the production configuration file for the application.

include "application"

# play.crypto.secret = ${?APPLICATION_SECRET}
play.http.secret.key = ${APPLICATION_SECRET}
application {
  domain = ${APP_DOMAIN}
  apiDomain: ${API_DOMAIN}
  isprod = true
}


## JDBC Datasource
db {
  # postgresql url
  default.url = ${JDBC_URL}
  default.username = ${JDBC_USER}
  default.password = ${JDBC_PASS}

  # You can turn on SQL logging for any datasource
  # https://www.playframework.com/documentation/latest/Highlights25#Logging-SQL-statements
  #default.logSql=true
}

db.default.poolInitialSize = 5
#db.default.poolInitialSize = ${?JDBC_DEFAULT_POOL_INIT_SIZE}

db.default.poolMaxSize = 50
#db.default.poolMaxSize = ${?JDBC_DEFAULT_POOL_MAX_SIZE}


#db.srread1.poolInitialSize = 5
#db.srread1.poolInitialSize = ${?JDBC_SRREAD1_POOL_INIT_SIZE}

#db.srread1.poolMaxSize = 50
#db.srread1.poolMaxSize = ${?JDBC_SRREAD1_POOL_MAX_SIZE}


# enable autoApply only in production, disabled in application.conf
play.evolutions.autoApply = true

# auto apply downs always disable in prod mode
play.evolutions.autoApplyDowns = false
play.evolutions.db.default.autoApplyDowns = false


scalikejdbc.global.loggingSQLAndTime.enabled = false
scalikejdbc.global.loggingSQLAndTime.warningEnabled = true
scalikejdbc.global.loggingSQLAndTime.warningThresholdMillis = 10000


rabbitmq.host = ${RABBITMQ_HOST}
rabbitmq.username = ${RABBITMQ_USERNAME}
rabbitmq.password = ${RABBITMQ_PASSWORD}
rabbitmq.virtualHost = ${RABBITMQ_VIRTUALHOST}

rabbitmq.prefix = ${RABBITMQ_PREFIX}

play.http {

  session {
    # Sets the cookie to be sent only over HTTPS.
    secure = true

    # Sets the cookie to be accessed only by the server.
    httpOnly = true

    # Sets the max-age field of the cookie to 5 minutes.
    # NOTE: this only sets when the browser will discard the cookie. Play will consider any
    # cookie value with a valid signature to be a valid session forever. To implement a server side session timeout,
    # you need to put a timestamp in the session and check it at regular intervals to possibly expire it.
    maxAge = 604800000

    # Sets the domain on the session cookie.
    # domain = ${?PLAY_SESSION_DOMAIN}

    # The value of the SameSite attribute of the cookie. Set to null for no SameSite attribute.
    sameSite = Secure //Setting null bcz of extension


  }

}

  play.cache.redis {
    prefix: ${REDIS_KEY_PREFIX}
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
  }

  mailgun {
    host = "smtp.eu.mailgun.org"
    login = "<EMAIL>"
    password = "**************************************************"
    adminEmail = "<EMAIL>"
    adminName = "WarmupHero"
    domain ="mg.warmuphero.com"
    apiKey = "************************************"
  }

# SmartReach
smartreach {
    apiKey = ${SMARTREACH_API_KEY}
}

 # Outlook API
 microsoft {
   clientID = ${MS_CLIENT_ID}
   clientSecret = ${MS_CLIENT_SECRET}

   clientIDV2 = ${MS_CLIENT_ID_V2}
   clientSecretV2 = ${MS_CLIENT_SECRET_V2}
 }
