# Routes
# This file defines all application routes (Higher priority routes first)
# ~~~~

# Auth routes

POST    /api/v1/auth/access_token                   api.controllers.AuthController.createAccessToken()

POST    /api/v1/auth/access_token/login             api.controllers.AuthController.loginViaAccessToken()

PUT    /api/v1/auth/reset_credits                   api.controllers.AuthController.resetCredits()

POST    /api/v1/auth/login                          api.controllers.AuthController.authenticate()

POST    /api/v1/auth/signup                         api.controllers.AuthController.signUp()

POST    /api/v1/auth/logout                         api.controllers.AuthController.signOut()

GET     /api/v1/auth/me                             api.controllers.AuthController.me()

POST    /api/v1/auth/profile                        api.controllers.AuthController.updateProfile()

POST    /api/v1/auth/update_sr_api_key              api.controllers.AuthController.updateSRSettings()

GET    /api/v1/auth/sr_warmupbox_credits           api.controllers.AuthController.getCreditsFromSRApiKey()

POST    /api/v1/auth/forgot_password                api.controllers.AuthController.forgotPassword()

POST    /api/v1/auth/forgot_password/update         api.controllers.AuthController.updatePasswordFromForgotPasswordFlow

POST    /api/v1/auth/change_password                api.controllers.AuthController.changePasswordLoggedinFlow()

POST     /api/v1/auth/verify_email/verify            api.controllers.AuthController.verifyEmail

POST    /api/v1/auth/verify_email/resend            api.controllers.AuthController.resendVerificationEmail


GET    /api/v1/auth/metadata                        api.controllers.AuthController.getAccountMetadata()


GET    /api/v1/email_accounts/get_oauth_url        api.controllers.OAuthController.getOAuthUrl(service_provider: String,email_address: Option[String])

GET    /api/v1/email_accounts/zapmail/get_oauth_url        api.controllers.OAuthController.getOAuthUrlZapmail(service_provider: String, email_address: String)

GET    /api/v1/email_accounts/oauth/code        api.controllers.OAuthController.getOAuthCode(code:String)

GET    /api/v1/email_accounts/oauth/kylpfqzpgxkmnrtzoftbxsrthphqfy/code        api.controllers.OAuthController.getOAuthCodeZapmail(code: String)

GET /api/v1/sr/email_accounts                                          api.controllers.EmailAccountController.findAllFromSR(older_than: Option[Long], newer_than: Option[Long])
GET /api/v1/sr/email_accounts/:email_setting_uuid                      api.controllers.EmailAccountController.connectSrEmailAccount(email_setting_uuid: String)

POST /api/v1/email_accounts/status                                      api.controllers.EmailAccountController.findEmailAccountWarmupStatus()


GET /api/v1/email_accounts                         api.controllers.EmailAccountController.findAll()

POST /api/v1/email_accounts                         api.controllers.EmailAccountController.create()

PUT /api/v1/email_accounts/frequency           api.controllers.EmailAccountController.updateWarmupFrequencySettings()

PUT /api/v1/email_accounts/activate         api.controllers.EmailAccountController.activate()

PUT /api/v1/email_accounts/deactivate       api.controllers.EmailAccountController.deactivate()

POST /api/v1/email_accounts/test_settings          api.controllers.EmailAccountController.testSettings()

GET /api/v1/email_accounts/:id                     api.controllers.EmailAccountController.find(id: Long)

PUT /api/v1/email_accounts/:id                     api.controllers.EmailAccountController.update(id: Int)

    PUT /api/v1/email_accounts/:id/basic_details       api.controllers.EmailAccountController.updateBasicInfo(id: Int)

PUT /api/v1/sr/email_accounts/:email_setting_uuid/activate                 api.controllers.EmailAccountController.startWarmupFromSr(email_setting_uuid: String)

DELETE /api/v1/email_accounts/:id                 api.controllers.EmailAccountController.delete(id: Int)

PUT /api/v1/email_accounts/:id/health_check       api.controllers.EmailAccountController.checkEmailHealth(id: Int)

PUT /api/v1/email_accounts/:id/signature           api.controllers.EmailAccountController.updateSignature(id: Int)

GET /api/v1/email_accounts/:id/stats/overall       api.controllers.ReportController.getEmailAccountOverallStats(id: Long)

GET /api/v1/email_accounts/:id/stats/timewise       api.controllers.ReportController.getEmailAccountTimewiseStats(id: Long)

# GET /api/v1/email_accounts/:id/stats/daily/primary   api.controllers.ReportController.getEmailAccountDailyStatsForPrimary(id: Long)

# GET /api/v1/email_accounts/:id/stats/daily/replies     api.controllers.ReportController.getEmailAccountDailyStatsForReplies(id: Long)

GET /api/v1/email_accounts/:id/stats/daily     api.controllers.ReportController.getEmailAccountDailyStats(id: Long)

GET /                                                  api.controllers.IndexController.returnIndexHtml



