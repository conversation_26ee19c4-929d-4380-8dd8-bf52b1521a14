#APPLICATION
export APPLICATION_SECRET=`gcloud secrets versions access 1 --secret=WH_APPLICATION_SECRET`
export APP_DOMAIN=`gcloud secrets versions access 1 --secret=WH_APP_DOMAIN`
export API_DOMAIN=`gcloud secrets versions access 1 --secret=WH_APP_DOMAIN`


#DB
export JDBC_URL=`gcloud secrets versions access 1 --secret=WH_JDBC_URL`
export JDBC_USER=`gcloud secrets versions access 1 --secret=WH_JDBC_USER`
export JDBC_PASS=`gcloud secrets versions access 1 --secret=WH_JDBC_PASS`

#Redis
export REDIS_HOST=`gcloud secrets versions access 1 --secret=WH_REDIS_HOST`
export REDIS_PORT=`gcloud secrets versions access 1 --secret=WH_REDIS_PORT`
export REDIS_KEY_PREFIX=`gcloud secrets versions access 1 --secret=WH_REDIS_KEY_PREFIX`

#Play
export PLAY_SESSION_DOMAIN=`gcloud secrets versions access 1 --secret=WH_PLAY_SESSION_DOMAIN`
export PLAY_SESSION_MAX_AGE=`gcloud secrets versions access 1 --secret=WH_PLAY_SESSION_MAX_AGE`
export PLAY_SESSION_SECURE=`gcloud secrets versions access 1 --secret=WH_PLAY_SESSION_SECURE`

#RABBITMQ
export RABBITMQ_HOST=`gcloud secrets versions access 1 --secret=WH_RABBITMQ_HOST`
export RABBITMQ_USERNAME=`gcloud secrets versions access 1 --secret=WH_RABBITMQ_USERNAME`
export RABBITMQ_PASSWORD=`gcloud secrets versions access 1 --secret=WH_RABBITMQ_PASSWORD`
export RABBITMQ_VIRTUALHOST=`gcloud secrets versions access 1 --secret=WH_RABBITMQ_VIRTUALHOST`
export RABBITMQ_PREFIX=`gcloud secrets versions access 1 --secret=WH_RABBITMQ_PREFIX`


export GOOGLE_APPLICATION_CREDENTIALS=`gcloud secrets versions access 1 --secret=WH_GOOGLE_APPLICATION_CREDENTIALS`

#SMARTREACH
export SMARTREACH_API_KEY=`gcloud secrets versions access latest --secret=WH_SMARTREACH_API_KEY`

#MICROSOFT
export MS_CLIENT_ID=`gcloud secrets versions access latest --secret=WH_MS_CLIENT_ID`
export MS_CLIENT_SECRET=`gcloud secrets versions access latest --secret=WH_MS_CLIENT_SECRET`

export MS_CLIENT_ID_V2=`gcloud secrets versions access latest --secret=WH_MS_CLIENT_ID_V2`
export MS_CLIENT_SECRET_V2=`gcloud secrets versions access latest --secret=WH_MS_CLIENT_SECRET_V2`

#AWS
export AWS_ACCESS_KEY_ID=`gcloud secrets versions access latest --secret=WH_AWS_ACCESS_KEY_ID`
export AWS_SECRET_ACCESS_KEY=`gcloud secrets versions access latest --secret=WH_AWS_SECRET_ACCESS_KEY`





