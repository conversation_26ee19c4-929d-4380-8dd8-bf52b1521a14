include "application"

# Override configuration for test environment
# REF: http://stackoverflow.com/a/********


//play.evolutions.db.default.enabled = false

db.default.url = "***********************************************"
db.default.username = "postgres"
db.default.password = "postgres"
db.default.logSql = true


# REF: http://scalikejdbc.org/documentation/playframework-support.html
db.default.fixtures.test = ["accounts.sql"]

//play.modules.enabled += "scalikejdbc.PlayFixtureModule"

loggly.tag = "coldemail-test"

application.domain = "http://localhost:1111"

rabbitmq.prefix = "coldemailTesting"
