#Detailed Documentaion
# https://almanac.io/docs/warmuphero-IRsV7ZzCj3BE0p7pESTIibWnn7mUXPen
variables:
  ARTIFACT_BUILD_FOLDER: artifact-build

stages:
  - custom-docker
  - style-check
  - build
  - production
  - smoke test

#This template is common to stages which uses the google cloud sdk this is used for authentication purpose
#GCP_SERVICE_ACCOUNT , GCP_PROJECT_ID are present in variables section of Gitlab
.job_template: &template
  image: google/cloud-sdk:latest
  before_script:
    - echo $GCP_SERVICE_ACCOUNT > gcloud-service-key.json
    - gcloud auth activate-service-account --key-file gcloud-service-key.json
    - gcloud config set project $GCP_PROJECT_ID

#Custom-Docker
#The custom Docker stage is responsible for building a docker image consisting of sbt,
#google cloud SDK, and zip. This is job is set to run every morning 9am .
custom-docker:
  stage: custom-docker
  image: docker
  services:
    - docker:dind
  only:
    - schedules
  script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER $CI_REGISTRY --password-stdin
    - docker build -t registry.gitlab.com/smartreach/code_warmuphero_core/emailwarmup_backend .
    - docker push registry.gitlab.com/smartreach/code_warmuphero_core/emailwarmup_backend

#style-check:
#  stage: style-check
#  image: registry.gitlab.com/smartreach/code_warmuphero_core/emailwarmup_backend
#  only:
#    - merge_requests
#  cache:
#    key: $CI_JOB_NAME
#    paths:
#      - sbt-cache/.ivy2/cache
#  variables:
#    REPORT_FORMAT: xml
#  script:
#    - sbt scalastyleGenerateConfig
#    - sbt scalastyle -Dsbt.ivy.home=sbt-cache/.ivy2
#  artifacts:
#    paths:
#      - target/gl-code-quality-report.json
#    reports:
#      codequality: target/scalastyle-result.xml


#Build
#The build stage is used to build artifacts stored in artifact storage GitLab.The build stage also uses
#sbt ivy2/cache so that it does not have to download all the sbt dependencies again.

#The artifact stores a zip file that consist of jar file and conf folder
#These 2 things are combined into a deploy.zip file and stored inside the GitLab artifact storage
#path: artifact-build/deploy.zip
build:
  stage: build
  before_script:
    - echo $GCP_SERVICE_ACCOUNT_ARTIFACT_REGISTRY_PROD > gcs-resolver-google-account.json
  image: registry.gitlab.com/smartreach/code_warmuphero_core/emailwarmup_backend
  cache:
    key: $CI_JOB_NAME
    paths:
      - sbt-cache/.ivy2/cache
  only:
    - merge_requests
    - master
  script:
    - export GOOGLE_APPLICATION_CREDENTIALS=gcs-resolver-google-account.json
    - echo $GOOGLE_APPLICATION_CREDENTIALS
    - sbt clean assembly -Dsbt.ivy.home=sbt-cache/.ivy2
    - rm -rf $ARTIFACT_BUILD_FOLDER
    - mkdir -p  $ARTIFACT_BUILD_FOLDER/
    - cp -r target/scala-2.13/srwarmup.jar $ARTIFACT_BUILD_FOLDER
    - cp -r conf/ $ARTIFACT_BUILD_FOLDER/
    - cd $ARTIFACT_BUILD_FOLDER
    - zip -r deploy.zip *
  artifacts:
    paths:
      - $ARTIFACT_BUILD_FOLDER/deploy.zip
  except:
    - schedules
#Production
#The production stage does 2 things
#  1)Upload deploy.zip to cloud storage 
#  2)ssh into VM to run the deploy script  -  restart_api_worker_cron.sh
#The deploy script downloads the deploy.zip file from cloud storage, unzip it and executes the
# jar file - srwarmup.jar
production:
  <<: *template
  stage: production
  when: manual
  only:
    - master
  script:
    - FOLDER_NAME=`/bin/date +%s`
    - echo $FOLDER_NAME
    - gsutil cp -r $ARTIFACT_BUILD_FOLDER/deploy.zip gs://warmuphero_jars/$FOLDER_NAME/
    - gcloud compute ssh ubuntu@warmuptool --zone "us-central1-a" --command "cd warmuphero && source restart_api_worker_cron.sh"
  except:
    - schedules
#Smoke-Test
# After successful completion of production stage a smoke test is performed on production url
# https://api.warmuphero.com/ to check whether the website is live
smoke test:
  stage: smoke test
  needs: ["production"]
  only:
    - master
  script:
    - sleep 10
    - "[[ $(curl -I https://api.warmuphero.com/ 2>/dev/null | head -n 1 | cut -d$' ' -f2) == *200* ]] || exit 1"
  except:
    - schedules