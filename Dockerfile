FROM ubuntu

RUN apt-get update &&\
apt-get install -y software-properties-common &&\
apt-get install wget &&\
rm -rf /var/lib/apt/lists/*


RUN wget -O - https://apt.corretto.aws/corretto.key |  gpg --dearmor -o /usr/share/keyrings/corretto-keyring.gpg &&\
echo "deb [signed-by=/usr/share/keyrings/corretto-keyring.gpg] https://apt.corretto.aws stable main" | tee /etc/apt/sources.list.d/corretto.list &&\
apt-get update; apt-get install -y java-21-amazon-corretto-jdk

#installing sbt
RUN apt-get update -yqq &&\
apt-get install apt-transport-https -yqq &&\
echo "deb https://repo.scala-sbt.org/scalasbt/debian /" | tee -a /etc/apt/sources.list.d/sbt.list &&\
mkdir -p /root/.gnupg &&\
gpg --recv-keys --no-default-keyring --keyring gnupg-ring:/etc/apt/trusted.gpg.d/scalasbt-release.gpg --keyserver hkp://keyserver.ubuntu.com:80 2EE0EA64E40A89B84B2DF73499E82A75642AC823 &&\
chmod 644 /etc/apt/trusted.gpg.d/scalasbt-release.gpg &&\
apt-get update -yqq &&\
apt-get install sbt -yqq

RUN apt-get install zip -y
RUN apt-get install wget -y
RUN apt-get install curl -y