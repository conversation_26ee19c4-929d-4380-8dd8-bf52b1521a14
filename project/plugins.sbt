logLevel := Level.Warn

resolvers += "Typesafe Simple Repository" at "https://repo.typesafe.com/typesafe/simple/maven-releases/"

addSbtPlugin("com.typesafe.play" % "sbt-plugin" % "2.9.1")
// addSbtPlugin("org.scalastyle" %% "scalastyle-sbt-plugin" % "1.0.0")

addSbtPlugin("org.latestbit" % "sbt-gcs-plugin" % "1.8.0")

// addSbtPlugin("io.get-coursier" % "sbt-coursier" % "1.0.1")

// REF: http://www.scalatest.org/install
//resolvers += "Artima Maven Repository" at "http://repo.artima.com/releases"

//addSbtPlugin("com.artima.supersafe" % "sbtplugin" % "1.1.7")

