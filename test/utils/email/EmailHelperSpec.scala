package utils.email

import io.smartreach.esp.utils.email.EmailHelperCommon
import org.scalatest.funspec.AnyFunSpec

class EmailHelperSpec extends AnyFunSpec {

  describe("Test makeHTMLBody") {

    it("should replace newlines with HTML <br /> tags") {

      val body =
        """
          |We are excited to introduce our latest innovation in the world of photography - a 35mm SLR camera that is set to revolutionize the way you capture life's precious moments. With its sleek design, advanced features, and unparalleled image quality, this camera is a game-changer for both professionals and enthusiasts alike.
          |
          |Our new camera boasts a range of innovative features, including a high-precision shutter, interchangeable lenses, and a built-in light meter. Whether you're a seasoned photographer or just starting out, this camera is designed to help you take your photography to the next level. We believe that this camera has the potential to make a significant impact on the photography industry, and we would love the opportunity to discuss how it can benefit your business.
          |
          |If you're interested in learning more, please don't hesitate to contact us.
          |
          |We look forward to hearing from you soon.
          |
          |""".stripMargin

      val htmlBody = EmailHelper.makeHTMLBody(
        baseBody = body,
        salutation = "Hi Nik<PERSON>, <br />",
        signature = "Regards,<br />Johnny"
      ).stripMargin


      val expectedOutput =
        """<html>
          | <head></head>
          | <body>
          |  Hi <PERSON>las, <br><br>
          |  We are excited to introduce our latest innovation in the world of photography - a 35mm SLR camera that is set to revolutionize the way you capture life's precious moments. With its sleek design, advanced features, and unparalleled image quality, this camera is a game-changer for both professionals and enthusiasts alike.<br><br>
          |  Our new camera boasts a range of innovative features, including a high-precision shutter, interchangeable lenses, and a built-in light meter. Whether you're a seasoned photographer or just starting out, this camera is designed to help you take your photography to the next level. We believe that this camera has the potential to make a significant impact on the photography industry, and we would love the opportunity to discuss how it can benefit your business.<br><br>
          |  If you're interested in learning more, please don't hesitate to contact us.<br><br>
          |  We look forward to hearing from you soon.<br><br>
          |  Regards,<br>
          |  Johnny
          | </body>
          |</html>""".stripMargin


      assert(expectedOutput == htmlBody)

    }

    it("should replace <br> with newline.") {

      val body =
        """
          |We are excited to introduce our latest innovation in the world of photography - a 35mm SLR camera that is set to revolutionize the way you capture life's precious moments. With its sleek design, advanced features, and unparalleled image quality, this camera is a game-changer for both professionals and enthusiasts alike.
          |
          |Our new camera boasts a range of innovative features, including a high-precision shutter, interchangeable lenses, and a built-in light meter. Whether you're a seasoned photographer or just starting out, this camera is designed to help you take your photography to the next level. We believe that this camera has the potential to make a significant impact on the photography industry, and we would love the opportunity to discuss how it can benefit your business.
          |
          |If you're interested in learning more, please don't hesitate to contact us.
          |
          |We look forward to hearing from you soon.
          |
          |""".stripMargin


      val htmlBody = EmailHelper.makeHTMLBody(
        baseBody = body,
        salutation = "Hi Niklas,<br />",
        signature = "Regards,<br />Johnny"
      ).stripMargin

      val expectedOutput =
        """Hi Niklas,
          |
          |
          |  We are excited to introduce our latest innovation in the world of photography - a 35mm SLR camera that is set to revolutionize the way you capture life's precious moments. With its sleek design, advanced features, and unparalleled image quality, this camera is a game-changer for both professionals and enthusiasts alike.
          |
          |
          |  Our new camera boasts a range of innovative features, including a high-precision shutter, interchangeable lenses, and a built-in light meter. Whether you're a seasoned photographer or just starting out, this camera is designed to help you take your photography to the next level. We believe that this camera has the potential to make a significant impact on the photography industry, and we would love the opportunity to discuss how it can benefit your business.
          |
          |
          |  If you're interested in learning more, please don't hesitate to contact us.
          |
          |
          |  We look forward to hearing from you soon.
          |
          |
          |  Regards,
          |
          |  Johnny""".stripMargin


      val textBody = EmailHelperCommon.getTextBodyFromHtmlBody(bodyHtml = htmlBody)


      assert(textBody == expectedOutput)

    }

  }

}
