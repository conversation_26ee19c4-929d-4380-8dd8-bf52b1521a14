package api.services

import api.models.EmailAccount

import java.time._
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers

class EmailAccountStatsServiceSpec extends AnyFunSpec with Matchers {

  describe("Test startAndEndOfToday") {

    it("should return the correct start and end epoch seconds for today") {

      val result = EmailAccount.startAndEndOfToday

      val expectedFrom = LocalDate.now().atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli / 1000
      val expectedTill = LocalDate.now().atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli / 1000

      result.from shouldEqual expectedFrom
      result.till shouldEqual expectedTill
    }

    it("should always start at midnight and end at end of day") {

      val datesToCheck = Seq(
        LocalDate.now().minusDays(1).atTime(8, 30),
        LocalDate.now().minusDays(14).atTime(0, 0),
        LocalDate.now().minusDays(30).atTime(13, 0),
        LocalDate.now().atTime(0, 0)
      )

      val truncatedMax = LocalTime.of(23, 59, 59) // precision compatible with epoch seconds

      datesToCheck.foreach { createdAt =>

        val result = EmailAccountStatsService.getStatsTimeRangeFromCreatedAt(createdAt)

        val fromTime = Instant.ofEpochSecond(result.from).atZone(ZoneOffset.UTC).toLocalTime
        val tillTime = Instant.ofEpochSecond(result.till).atZone(ZoneOffset.UTC).toLocalTime

        fromTime shouldEqual LocalTime.MIDNIGHT
        tillTime shouldEqual truncatedMax

      }

      val todayRange = EmailAccount.startAndEndOfToday
      Instant.ofEpochSecond(todayRange.from).atZone(ZoneOffset.UTC).toLocalTime shouldEqual LocalTime.MIDNIGHT
      Instant.ofEpochSecond(todayRange.till).atZone(ZoneOffset.UTC).toLocalTime shouldEqual truncatedMax

    }

  }

  describe("Test getStatsTimeRangeFromCreatedAt") {

    it("should compute range from created_at to created_at + 14 days if account is < 14 days old") {

      val createdAt = LocalDate.now().minusDays(5).atTime(10, 0)

      val result = EmailAccountStatsService.getStatsTimeRangeFromCreatedAt(createdAt)

      val expectedFrom = createdAt.toLocalDate.atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli / 1000
      val expectedTill = createdAt.toLocalDate.atTime(LocalTime.MAX).plusDays(14).toInstant(ZoneOffset.UTC).toEpochMilli / 1000

      result.from shouldEqual expectedFrom
      result.till shouldEqual expectedTill

    }

    it("should compute range from now -14 days to now if account is > 14 days old") {

      val createdAt = LocalDate.now().minusDays(30).atTime(10, 0)

      val result = EmailAccountStatsService.getStatsTimeRangeFromCreatedAt(createdAt)

      val expectedFrom = LocalDate.now().minusDays(14).atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli / 1000
      val expectedTill = LocalDate.now().atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli / 1000

      result.from shouldEqual expectedFrom
      result.till shouldEqual expectedTill

    }

    it("should compute range from now -14 days if account is exactly 14 days old") {

      val createdAt = LocalDate.now().minusDays(14).atTime(9, 0)

      val result = EmailAccountStatsService.getStatsTimeRangeFromCreatedAt(createdAt)

      val expectedFrom = LocalDate.now().minusDays(14).atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli / 1000
      val expectedTill = LocalDate.now().atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli / 1000

      result.from shouldEqual expectedFrom
      result.till shouldEqual expectedTill

    }

    it("should compute correct range if account was created today") {

      val createdAt = LocalDate.now().atTime(0, 0)

      val result = EmailAccountStatsService.getStatsTimeRangeFromCreatedAt(createdAt)

      val expectedFrom = LocalDate.now().atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli / 1000
      val expectedTill = LocalDate.now().atTime(LocalTime.MAX).plusDays(14).toInstant(ZoneOffset.UTC).toEpochMilli / 1000

      result.from shouldEqual expectedFrom
      result.till shouldEqual expectedTill

    }

  }

}
