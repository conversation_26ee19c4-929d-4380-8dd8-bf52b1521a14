# Claude Instructions for EmailWarmup Backend

## Code Style
- Always use named parameters in function calls
- Move domain models to separate files under `app/api/models/`
- Use service layer pattern for business logic
- Keep controllers thin, move logic to services
- Always add a newline at the end of files

## Git Workflow
- Always create feature branches with `vg/` prefix
- Use selective staging (`git add -p`) for mixed-change files
- Include detailed commit messages with context
- Always ask before committing configuration changes with secrets
- **NEVER include Claude branding, "Generated with Claude Code", or "Co-Authored-By: Claude" in commit messages**

## Database
- Use `IF NOT EXISTS` / `IF EXISTS` in migrations
- Always include both UP and DOWN migrations
- Default values should represent legacy/existing behavior

## Architecture Patterns
- Put domain models in separate files under `app/api/models/`
- Use service layer pattern for business logic
- Keep controllers thin, move logic to services
- Prefer type-safe enums over string constants
- Use helper methods in services to determine configuration per entity

## Configuration
- Ask before modifying configuration files with sensitive data
- Support multiple versions/environments through configuration
- Use pattern matching for configuration selection based on entity state
