package utils.dbutils

import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef

object SQLUtils {

  def getMultipleRowsValuePlaceholderSql(
    rows: Seq[SQLSyntax]
  ): SQLSyntax = {
    rows
      .foldLeft(sqls"") { case (previousRows, nextRow) =>
        if (previousRows.trim.isEmpty) {
          sqls"$nextRow"
        } else {
          sqls"$previousRows, $nextRow"
        }
      }
  }

  /*
  REF: https://dba.stackexchange.com/questions/91247/optimizing-a-postgres-query-with-a-large-in
  used in places where we need a postgres "IN" construct

  REF: https://postgres.cz/wiki/PostgreSQL_SQL_Tricks_I#Predicate_IN_optimalization

  Example outputs:
  if arr.length < 25, we are sticking to common format:
  IN (1,2,3)

  if arr.length >= 25, we are using this format:
  IN (VALUES (1), (2), (3))
  */
  def generateSQLValuesClause[T](
    arr: Seq[T]
  ): SQLSyntax = {

    if (arr.length < 25) {

      sqls" ($arr) "

    } else {

      val foldedValues = arr
        .zipWithIndex
        .foldLeft(sqls"") { case (acc, (nextVal, index)) =>

          if (index == 0) {
            sqls"$acc ($nextVal)"
          } else {
            sqls"$acc, ($nextVal)"
          }

        }

      sqls"(VALUES $foldedValues)"
    }
  }

}
