package api.accounts

import org.mindrot.jbcrypt.BCrypt


object PasswordHasher {

  /**
    * Implementation of the password hasher based on BCrypt.
    *
    * The log2 of the number of rounds of hashing to apply.
    * @see [[http://www.mindrot.org/files/jBCrypt/jBCrypt-0.2-doc/BCrypt.html#gensalt(int) gensalt]]
    */
  val logRounds = 12

  /**
    * Hashes a password.
    *
    * This implementation does not return the salt separately because it is embedded in the hashed password.
    * Other implementations might need to return it so it gets saved in the backing store.
    *
    * @param plainPassword The password to hash.
    * @return the hashed password.
    */
  def hash(plainPassword: String) = BCrypt.hashpw(plainPassword, BCrypt.gensalt(logRounds))

  /**
    * Checks if a password matches the hashed version.
    *
    * @param password The password retrieved from the backing store.
    * @param suppliedPassword The password supplied by the user trying to log in.
    * @return True if the password matches, false otherwise.
    */
  def matches(password: String, suppliedPassword: String) = BCrypt.checkpw(suppliedPassword, password)

}
