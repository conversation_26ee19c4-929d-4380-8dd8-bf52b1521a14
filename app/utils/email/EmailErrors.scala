package utils.email

import java.time.LocalDateTime

import org.joda.time.DateTime

object EmailErrors {

  val HUNDRED_YEARS: Int = 100 * 365 * 24 * 60 // 100 years

  sealed abstract class EmailAccountError {
    val defaultMsg: String
    val pauseForMinutes: Int

    def pauseTill: LocalDateTime = LocalDateTime.now().plusMinutes(pauseForMinutes)
//    def pauseTill: DateTime = DateTime.now().plusMinutes(pauseForMinutes)

  }

  case object AuthenticationFailedError extends EmailAccountError {
    val defaultMsg = "WarmupHero failed to connect to this email account. Please update account credentials in Settings -> Email Accounts."
    val pauseForMinutes = HUNDRED_YEARS
  }

  case object GmailSMTPFailedError extends EmailAccountError {
    val defaultMsg = "Gmail send error, will retry after 10 minutes"
    val pauseForMinutes = 10
  }

  case object SmtpSendFailedWithSpamORRejectedWarning extends EmailAccountError {
    val defaultMsg = s"Your email domain / host is being blocked by the recipient(s) for sending spam (Please contact support if you have any questions)"
    val pauseForMinutes = HUNDRED_YEARS
  }


  case object GmailDailyUserSendingQuotaExceededError extends EmailAccountError {
    val defaultMsg = "Gmail is throttling your email sending with the 'Daily user sending quota exceeded' message. Sending will be restarted in 24 hours."
    val pauseForMinutes = 24 * 60 // 24 Hours
  }

  case object UnknownSendError extends EmailAccountError {
    val defaultMsg = "Error while sending email, will retry after 10 minutes"
    val pauseForMinutes = 10
  }

  case object ConnectionTimeoutError extends EmailAccountError {
    val defaultMsg = "We are facing a problem (timeout) connecting with this email account. Connection will be retried in 10 minutes."
    val pauseForMinutes = 10
  }

  case object ReplyTrackingMessagingExceptionError extends EmailAccountError {
    val defaultMsg = "We are getting an error while tracking reply in your inbox; will retry in 10 minutes"
    val pauseForMinutes = 10
  }

  case object UnknownReplyTrackingError extends EmailAccountError {
    val defaultMsg = "There is an error while tracking reply in your inbox; will retry in 10 minutes"
    val pauseForMinutes = 10
  }

  case object LandingCheckFailedExceededLimitError extends EmailAccountError {
    val defaultMsg =
      """
        |Warmup has been paused for this email inbox as WarmupHero is not able to check warmup emails in this inbox.
        |Please verify and remove any filters in the inbox that might be filtering our warmup emails,
        |and then start email warmup again.
        |""".stripMargin

    val pauseForMinutes = HUNDRED_YEARS
  }

  case object EmailHealthCheckError extends EmailAccountError {

    val defaultMsg = "Email authentication failed due to missing SPF, DKIM, or DMARC records. Please contact support."

    val pauseForMinutes: Int = HUNDRED_YEARS

  }

  case object EmailHardBouncedError extends EmailAccountError {

    // TODO: default message

    val defaultMsg =
      """
        |Warmup has been paused for this email inbox as an email sent to this inbox has hard bounced.
        |""".stripMargin

    val pauseForMinutes = HUNDRED_YEARS
  }

}
