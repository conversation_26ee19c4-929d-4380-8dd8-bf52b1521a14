package utils.email

import java.util.Properties

import api.models._
import com.sun.mail.gimap.GmailStore
import javax.mail._
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

object GmailService extends TEmailService {

  override val INBOX_FOLDER_NAME: String = IMAPStanderdFolderTypes.INBOX.toString
  override val SPAM_FOLDER_NAME: String = IMAPStanderdFolderTypes.GMAIL_SPAM.toString
  override val IMPORTANT_FOLDER_NAME: String = IMAPStanderdFolderTypes.IMPORTANT.toString
  override val CATEGORIES: Seq[String] = EmailCategories.gmail


  def receiveEmail(receiverEmailAccount: EmailAccount, emailScheduled: EmailScheduled, Logger: SRLogger)
                  (implicit wsClient: WSClient, ec: ExecutionContext): Future[Option[EmailMessageTrackedFullOjb]] = {

    Logger.info(s"[GmailService] receiveEmail: Finding email")

    val filteredMessagesFut: Future[Option[EmailMessageTrackedFullOjb]] = for {
      imapEmailSetting: EmailSettings.ImapEmailAccount <- Future.fromTry(EmailAccount.getImapEmailAccount(receiverEmailAccount))

      filteredMessages: Option[EmailMessageTrackedFullOjb] <- getFilteredMessagesFromAllFolders(
        receiveCredentials = imapEmailSetting,
        emailScheduled = emailScheduled,
        Logger = Logger
      )

    } yield {

      Logger.info(s"[GmailService] receiveEmail: Finding email: filteredMessages: $filteredMessages")

      filteredMessages
    }


    filteredMessagesFut
      .map { message =>

        Logger.info(s"[GmailService] receiveEmail: DONE")

        message
      }
      .recover {
        case e =>
          Logger.error(s"[SmtpImapService] receiveEmail: DONE with error", err = e)
          throw e
      }

  }

  // We have to override getFilteredMessagesFromAllFolders here because GmailService has its own
  // implementation for openImapConnection, which return GmailStore instead of Store.
  override def getFilteredMessagesFromAllFolders(
                                                  receiveCredentials: EmailSettings.ImapEmailAccount,
                                                  emailScheduled: EmailScheduled,
                                                  Logger: SRLogger
                                                )(
                                                  implicit wsClient: WSClient,
                                                  ec: ExecutionContext,
                                                ): Future[Option[EmailMessageTrackedFullOjb]] = {

    val tryOfFilteredMessages: Try[Option[EmailMessageTrackedFullOjb]] = for {

      store: GmailStore <- openImapConnection(
        emailSetting = receiveCredentials,
        Logger = Logger
      )

      filteredMessages: Option[EmailMessageTrackedFullOjb] <- getFilteredMessagesFromAllFoldersInternal(
        store = store,
        emailScheduled = emailScheduled,
        Logger = Logger
      )

      // return type is Unit. So, not adding explicit return type.
      _ <- closeImapConnection(store)

    } yield {

      filteredMessages
    }

    Future.fromTry(tryOfFilteredMessages)
  }


  // We have to override fetchMessagesWithBodySearchTerm here because GmailService has its own
  // implementation for openImapConnection, which return GmailStore instead of Store.
  override def fetchMessagesWithBodySearchTerm(
                                                bodySearchTerm: String,
                                                emailScheduled: EmailScheduled,
                                                receiveCredentials: EmailSettings.ImapEmailAccount,
                                                Logger: SRLogger
                                              )(
                                                implicit wsClient: WSClient,
                                                ec: ExecutionContext,
                                              ): Future[Vector[BounceCheckDetails]] = {

    val tryOfFilteredMessages: Try[Vector[BounceCheckDetails]] = for {

      store: GmailStore <- openImapConnection(
        emailSetting = receiveCredentials,
        Logger = Logger
      )

      filteredMessages: Vector[BounceCheckDetails] <- fetchMessagesWithBodySearchTermInternal(
        store = store,
        emailScheduled = emailScheduled,
        bodySearchTerm = bodySearchTerm,
        Logger = Logger
      )

      // return type is Unit. So, not adding explicit return type.
      _ <- closeImapConnection(store)

    } yield {

      filteredMessages
    }

    Future.fromTry(tryOfFilteredMessages)
  }

  override def openImapConnection(emailSetting: EmailSettings.ImapEmailAccount, Logger: SRLogger): Try[GmailStore] = Try {

    val props: Properties = new Properties()

    props.put("mail.debug", "true")
    props.setProperty("mail.store.protocol", "gimap")
    // props.put("mail.gimap.sasl.enable", "true")

    val session: Session = Session.getDefaultInstance(props, null)

    val store = session.getStore("gimap").asInstanceOf[GmailStore]

    store.connect(
      "imap.gmail.com",
      emailSetting.imap_username,
      emailSetting.imap_password,
    )

    store
  }

}
