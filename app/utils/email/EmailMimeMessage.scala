package utils.email

import javax.mail._
import javax.mail.internet._

import play.api.{Logging}

class EmailMimeMessage(session: Session, messageId: String) extends MimeMessage(session)  with  Logging {

  @throws(classOf[MessagingException])
  override def updateMessageID(): Unit = {
    logger.info(s"[EmailMimeMessage] before sending add message id: $messageId")
    setHeader("Message-ID", messageId)
  }

}
