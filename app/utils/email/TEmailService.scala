package utils.email

import akka.actor.ActorSystem

import java.security.Security
import java.time.ZoneId
import java.util.{Date, Properties}
import javax.mail.internet._
import javax.mail.search._
import javax.mail._
import api.emails._
import api.models._
import com.sun.mail.gimap.{GmailFolder, GmailMessage, GmailRawSearchTerm}
import play.api.libs.ws.WSClient
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext, Future, blocking}
import scala.util.{Failure, Success, Try}
import javax.mail.FetchProfile
import com.sun.mail.imap.{IMAPFolder, IMAPMessage}
import com.sun.mail.util.MailSSLSocketFactory
import io.smartreach.esp.api.emails.IEmailAddress
import io.smartreach.esp.utils.email.services.EmailReplyTrackingCommonService
import org.joda.time.{DateTime, DateTimeZone}
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>}


case class SRSmtpSendFailedWithSpamORRejectedWarning(message: String = "", cause: Throwable = None.orNull) extends Exception(message, cause)

case class TrackedEmailMessageWithCategory(
                                            message: Message,
                                            category: Option[String]
                                          )

trait TEmailService
  extends EmailSendService[EmailSettings.SmtpEmailAccount, Unit]
    with EmailReceiveService[EmailSettings.ImapEmailAccount, Message] {


  val INBOX_FOLDER_NAME: String
  val SPAM_FOLDER_NAME: String
  val IMPORTANT_FOLDER_NAME: String
  val CATEGORIES: Seq[String]


  private def openIMAPFolder(store: Store, folderName: String): Try[IMAPFolder] = blocking {
    Try {

      val folder: IMAPFolder = store.getFolder(folderName).asInstanceOf[IMAPFolder]
      folder.open(Folder.READ_WRITE)
      folder

    }
  }

  final def closeInbox(inbox: Folder): Try[Unit] = blocking {
    Try {
      inbox.close(false)
    }
  }

  private final def closeFolder(folder: Folder): Try[Unit] = blocking {
    Try {
      folder.close(false)
    }
  }


  def openImapConnection(emailSetting: EmailSettings.ImapEmailAccount, Logger: SRLogger): Try[Store] = blocking {

    val enableSSL = emailSetting.imap_port == 993

    if (enableSSL) {

      openImapConnectionInternal(
        emailSetting = emailSetting,
        Logger = Logger,

        enableSSL = true,
        enableSTARTTLS = false // in case of enableSSL, startTLS doesnt matter
      )

    } else {

      val tryWithoutSTARTTLS = openImapConnectionInternal(
        emailSetting = emailSetting,
        Logger = Logger,
        enableSSL = false,

        enableSTARTTLS = false
      )

      if (tryWithoutSTARTTLS.isFailure) {

        val tryWithSTARTTLS = openImapConnectionInternal(
          emailSetting = emailSetting,
          Logger = Logger,
          enableSSL = false,

          enableSTARTTLS = true
        )

        if (tryWithSTARTTLS.isSuccess) {
          Logger.info(s"AMAZING tryWithoutSTARTTLS failed but tryWithSTARTTLS worked: ${emailSetting.imap_username} : ${emailSetting.imap_host} : ${emailSetting.imap_port}")
        }

        tryWithSTARTTLS

      } else {

        tryWithoutSTARTTLS

      }

    }

  }


  protected final def closeImapConnection(store: Store): Try[Unit] = blocking {
    Try {
      store.close()
    }
  }


  // close store after using this function
  private def openImapConnectionInternal(
                                          emailSetting: EmailSettings.ImapEmailAccount,
                                          Logger: SRLogger,

                                          enableSSL: Boolean,
                                          enableSTARTTLS: Boolean
                                        ): Try[Store] = blocking {
    Try {

      val sf = new MailSSLSocketFactory
      sf.setTrustedHosts(emailSetting.imap_host)

      val props: Properties = new Properties

      props.put("mail.debug", "true")

      if (enableSSL) {


        props.setProperty("mail.store.protocol", "imaps")
        props.setProperty("mail.imaps.host", emailSetting.imap_host.trim)
        props.setProperty("mail.imaps.port", emailSetting.imap_port.toString)
        props.setProperty("mail.imaps.connectiontimeout", "150000")
        props.setProperty("mail.imaps.timeout", "150000")

        props.put("mail.imaps.ssl.socketFactory", sf)

      } else {

        props.setProperty("mail.store.protocol", "imap")
        props.setProperty("mail.imap.host", emailSetting.imap_host.trim)
        props.setProperty("mail.imap.port", emailSetting.imap_port.toString)
        props.setProperty("mail.imap.connectiontimeout", "150000")
        props.setProperty("mail.imap.timeout", "150000")


        if (enableSTARTTLS) {

          props.setProperty("mail.imap.starttls.enable", "true")

          props.put("mail.imap.ssl.socketFactory", sf)
        }

      }

      val session: Session = Session.getInstance(props, null)

      val store: Store = if (enableSSL) session.getStore("imaps") else session.getStore("imap")

      Logger.info(
        s"""[READEMAIL] IMAP: try connect:   ${emailSetting.imap_host},
      ${emailSetting.imap_port},
      ${emailSetting.imap_username},
      $props
      """)

      store.connect(
        emailSetting.imap_host.trim,
        emailSetting.imap_port,
        emailSetting.imap_username.trim,
        emailSetting.imap_password.trim
      )
      store



    }
  }



  final def sendEmail(
                        sendCredentials: EmailSettings.SmtpEmailAccount,
                        email: EmailToBeSent
                      )(
                        implicit wsClient: WSClient,
                        ec: ExecutionContext,
                        system: ActorSystem,
                        Logger: SRLogger
                      ): Future[Unit] = blocking {

    val tryOfUnit: Try[Unit] = Try {

      Logger.info(s"Sending email : ${email.to_emails}, ${sendCredentials.smtp_username}")

      val props = new Properties

      val isSMTPS = sendCredentials.smtp_port == 465
      val isSTARTTLS = sendCredentials.smtp_port == 587


      val smtpHost = sendCredentials.smtp_host.trim

      if (isSMTPS) {
        // Logger.info(s"Sending email : ${email.to_email}, ${smtpEmailSetting.smtp_username}, inreplyto: ${email.in_reply_to_id} :: inside 465")

        props.put("mail.transport.protocol", "smtps")
        props.put("mail.smtps.auth", "true")
        props.put("mail.smtps.host", smtpHost)
        props.put("mail.smtps.port", sendCredentials.smtp_port.toString)
        props.put("mail.smtps.timeout", "60000")


        props.put("mail.smtps.socketFactory.port", "465")
        props.put("mail.smtps.socketFactory.class", "javax.net.ssl.SSLSocketFactory")

      } else {
        // Logger.info(s"Sending email : ${email.to_email}, ${smtpEmailSetting.smtp_username}, inreplyto: ${email.in_reply_to_id} :: inside smtp")

        props.put("mail.transport.protocol", "smtp")
        props.put("mail.smtp.auth", "true")
        props.put("mail.smtp.host", smtpHost)
        props.put("mail.smtp.port", sendCredentials.smtp_port.toString)
        props.put("mail.smtp.timeout", "60000")


        // starttls works over 587, keeping this open for all email accounts blocks 465 ssl emails
        if (isSTARTTLS) {
          // Logger.info(s"Sending email : ${email.to_email}, ${smtpEmailSetting.smtp_username}, inreplyto: ${email.in_reply_to_id} :: inside 587")

          props.put("mail.smtp.starttls.enable", "true")

          // REF: https://stackoverflow.com/questions/********/javamail-could-not-convert-socket-to-tls-gmail
          props.put("mail.smtp.ssl.trust", smtpHost)

          /**
            * REF: https://stackoverflow.com/a/********
            *
            * CaptionData issue: https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/all/conversations/**************
            *   javax.net.ssl.SSLHandshakeException: The server selected protocol version TLS10 is not accepted by client preferences [TLS12]
            *
            * In April 2021, jdk stopped supporting TLSv1 and TLSv1.1 by default, so this issue might have been because of that.
            *
            * Below we are explicitly supporting TLSv1 and TLSv1.1 for smtp requests. This change was made on 4th May 2021.
            */
          if (smtpHost.contains("captiondata.com")) {
            val disabledAlgorithms1 = Security.getProperty("jdk.tls.disabledAlgorithms")
            Logger.info(s"disabled properties 1: $disabledAlgorithms1")

            val protocolsAllowed1 = props.getProperty("mail.smtp.ssl.protocols")
            Logger.info(s"protocolsAllowed 1: $protocolsAllowed1")


            Logger.info("enabling mail.smtp.ssl.protocols : TLSv1 TLSv1.1 TLSv1.2 TLSv1.3")
            props.setProperty("mail.smtp.ssl.protocols", "TLSv1 TLSv1.1 TLSv1.2 TLSv1.3")


            val protocolsAllowed2 = props.getProperty("mail.smtp.ssl.protocols")
            Logger.info(s"protocolsAllowed 2: $protocolsAllowed2")

            val disabledAlgorithms2 = Security.getProperty("jdk.tls.disabledAlgorithms")
            Logger.info(s"disabled properties 2: $disabledAlgorithms2")

          }

        }


      }



      // REF: https://stackoverflow.com/a/27404861
      props.put("mail.mime.charset", "utf-8")

      // props.setProperty("mail.debug", "true")
      /// Logger.info(s"Sending email : ${email.to_email}, ${smtpEmailSetting.smtp_username}, inreplyto: ${email.in_reply_to_id} :: $props")

      val session = Session.getInstance(
        props,
        new Authenticator() {
          override def getPasswordAuthentication = new
              PasswordAuthentication(sendCredentials.smtp_username.trim, sendCredentials.smtp_password.trim)
        }
      )


      // session.setDebug(true)

      val message = if (email.message_id.isDefined) new EmailMimeMessage(session, email.message_id.get) else new MimeMessage(session)

      // this was a disaster
      //    val message = new SMTPMessage(session)
      //    if (email.message_id.isDefined) {
      //      message.setHeader("Message-ID", email.message_id.get)
      //    }


      if (email.in_reply_to_header.isDefined && email.references_header.isDefined) {
        message.setHeader("In-Reply-To", email.in_reply_to_header.get)
        message.setHeader("References", email.references_header.get)
      }


      // set return path
      // REF: https://stackoverflow.com/a/19030774
      //    val (replyToEmailName, replyToEmailDomain) = EmailValidator.getLowercasedNameAndDomainFromEmail(email.reply_to_email)
      //    val bounceAddress = s"$replyToEmailName+bounced@$replyToEmailDomain"
      //    message.setEnvelopeFrom(bounceAddress)


      // REF: https://stackoverflow.com/a/14924502
      // start: set content

      if(email.is_reply) {
        message.reply(true)
      }

      val multiPart = new MimeMultipart("alternative")

      val textPart = new MimeBodyPart()
      textPart.setText(email.text_body, "utf-8")
      textPart.setHeader("Content-Transfer-Encoding", "8bit")


      val htmlPart = new MimeBodyPart()
      htmlPart.setContent(email.body, "text/html; charset=utf-8")
      // htmlPart.setHeader("Content-Transfer-Encoding", "8bit")


      multiPart.addBodyPart(textPart)
      multiPart.addBodyPart(htmlPart)

      message.setContent(multiPart)

      // end: set content

      message.setSubject(email.subject, "utf-8")
      //      message.reply(true)

      message.setFrom(new InternetAddress(email.from_email, email.from_name))

      // set reply to email header only if reply-to email address is different from from-email address
      //      if (email.reply_to_email.isDefined && email.reply_to_email.get.trim.nonEmpty && email.from_email.trim.toLowerCase != email.reply_to_email.get.trim.toLowerCase) {
      //        message.setReplyTo(Array(new InternetAddress(email.reply_to_email.get.trim, email.reply_to_name.get)))
      //      }

      /*
      if (email.to_name.isEmpty) {
        message.addRecipients(Message.RecipientType.TO, email.to_email)
      } else {
        message.addRecipients(Message.RecipientType.TO, Array(new InternetAddress(email.to_email, email.to_name.get).asInstanceOf[Address]))
      }
      */

      val toEmails: Array[Address] = IEmailAddress.toJavaAddressArray(ems = email.to_emails)

      message.addRecipients(Message.RecipientType.TO, toEmails)


      //
      //    if (email.in_reply_to_id.isDefined) {
      //      message.setHeader("In-Reply-To", email.in_reply_to_id.get)
      //    }



      val transport = if (isSMTPS) session.getTransport("smtps") else session.getTransport("smtp")
      transport.connect()


      transport.sendMessage(message, message.getAllRecipients)

      transport.close()
    }

    Future.fromTry(tryOfUnit)
  }

  def fetchRecentMessagesForTestingImap(
                                         emailAfterDate: DateTime,
                                         receiveCredentials: EmailSettings.ImapEmailAccount,
                                         Logger: SRLogger
                                       )(
                                         implicit wsClient: WSClient,
                                         ec: ExecutionContext,
                                       ): Future[Vector[Message]] = {

    fetchRecentMessages(
      emailAfterDate = emailAfterDate,
      receiveCredentials = receiveCredentials,
      Logger = Logger,
    )

  }

  def fetchRecentMessages(
    emailAfterDate: DateTime,
    receiveCredentials: EmailSettings.ImapEmailAccount,
    Logger: SRLogger,
    senderEmail: Option[String] = None
  )(
    implicit wsClient: WSClient,
    ec: ExecutionContext,
  ): Future[Vector[Message]] = {

    val tryOfMessages: Try[Vector[Message]] = for {

      store: Store <- openImapConnection(
        emailSetting = receiveCredentials,
        Logger = Logger
      )

      inbox: IMAPFolder <- openIMAPFolder(store, folderName = IMAPStanderdFolderTypes.INBOX.toString)

      messages: Vector[Message] <- getRecentImapMessagesForTestSettings(inbox, emailAfterDate, isTestingSettings = true, Logger = Logger, senderEmail = senderEmail)

      // return type is Unit. So, not adding explicit return type.
      closeFolder <- closeFolder(inbox)

      // return type is Unit. So, not adding explicit return type.
      closeConn <- closeImapConnection(store)

    } yield {
      messages
    }

    Future.fromTry(tryOfMessages)
  }


  /**
    * searchMessageBySenderAndSubject
    * will search message in all the folders one by one until it found
    * first it will check using messageIdtTerm if not goes to fall back check fromAndSubjectAndReceivedAtTerm
    * after some subject parse issues in search term, implemented search using message_id
    * */
  protected def searchMessageBySenderAndSubject(
                                                 folder: Folder,
                                                 emailScheduled: EmailScheduled,
                                                 isTestingSettings: Boolean = false,
                                                 Logger: SRLogger
                                 ): Try[Vector[TrackedEmailMessageWithCategory]] = blocking {
    Try {

      val emailAfterDate = if (emailScheduled.sent_at.isDefined) {
        emailScheduled.sent_at.get.minusDays(1)
      } else emailScheduled.scheduled_at.get.minusDays(1)


      /*message_id always exists but checking isDefined confident that in the fail case it will fallback to subjectTerm search*/
      val messageIdtTerm = if(emailScheduled.message_id.isDefined) new MessageIDTerm(emailScheduled.message_id.get) else new MessageIDTerm("")

      val convertedEmailAfterDate = Date.from(emailAfterDate.toLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant())
      val newerThanReceivedDate: SearchTerm = new ReceivedDateTerm(ComparisonTerm.GE, convertedEmailAfterDate)

      val formattedSubjectToSearch = EmailHelper.getSubjectToSearch(emailScheduled.subject)
      val subject = new SubjectTerm(formattedSubjectToSearch)

      val fromAddress = new InternetAddress(emailScheduled.from_email)
      val fromTerm = new FromTerm(fromAddress)

      val fromAndSubject = new AndTerm(subject, fromTerm)

      val fromAndSubjectAndReceivedAtTerm = new AndTerm(fromAndSubject, newerThanReceivedDate)

      var categoryMessages: Seq[TrackedEmailMessageWithCategory] = Seq()

      if(EmailHelper.isInbox(folder = folder)) {

        val categories = CATEGORIES

        categories.foreach(c => {
          if(categoryMessages.isEmpty) {
            val categoryTerm = new GmailRawSearchTerm(s"category:$c")
            val queryWithMsgIdTerm = new AndTerm(categoryTerm, messageIdtTerm)
            val queryWithFromEmailAndSubjectTerm = new AndTerm(categoryTerm, fromAndSubjectAndReceivedAtTerm)
            var messages: Array[Message] = folder.search(queryWithMsgIdTerm)
            messages = if (messages.nonEmpty) {
              messages
            } else {
              Logger.warn(s"Fallback to queryWithFromEmailAndSubjectTerm: ${queryWithFromEmailAndSubjectTerm} :: emailScheduledId: ${emailScheduled.id}")
              folder.search(queryWithFromEmailAndSubjectTerm)
            }
            val messagesWithProfile = addFetchProfilePropsAndFetch(messages = messages, inbox = folder)
            if (messagesWithProfile.nonEmpty) {
              categoryMessages = categoryMessages ++ Seq((TrackedEmailMessageWithCategory(messagesWithProfile.head, Some(c))))
            }
          }
        })

      }

      if (categoryMessages.nonEmpty) {

        categoryMessages.toVector

      } else {


        var messages: Array[Message] = folder.search(messageIdtTerm)
        messages = if (messages.nonEmpty) {
          messages
        } else {

          Logger.warn(s"Fallback to fromAndSubjectAndReceivedAtTerm: ${fromAndSubjectAndReceivedAtTerm} :: emailScheduledId: ${emailScheduled.id}")
          folder.search(fromAndSubjectAndReceivedAtTerm)

        }

        val messagesWithProfile = addFetchProfilePropsAndFetch(messages = messages, inbox = folder)

        val emailsTracked = messagesWithProfile.map(m => {
          TrackedEmailMessageWithCategory(m, None)
        })

        emailsTracked

      }

    }
  }


  final def addFetchProfilePropsAndFetch(
                                          messages: Array[Message],
                                          inbox: Folder
                                        ): Vector[Message] = blocking {

    val fetchProfile = new FetchProfile
    fetchProfile.add(FetchProfile.Item.ENVELOPE)


    // Removed fetchprofile info because on some outlook emails, the getContent was empty when using this, dafaq javamail
    // REF: https://stackoverflow.com/questions/33164207/javamail-fetchprofile-item-content-info-breaks-getcontent
    //        fetchProfile.add(FetchProfile.Item.CONTENT_INFO)
    fetchProfile.add("Content-Type")


    fetchProfile.add(FetchProfile.Item.FLAGS)


    // NOTE: commented out FetchProfileItem.MESSAGE: it was choking the server
    // when there were messages with huge attachments etc.
    // REF: https://javaee.github.io/javamail/docs/api/com/sun/mail/imap/IMAPFolder.FetchProfileItem.html
    //fetchProfile.add(IMAPFolder.FetchProfileItem.MESSAGE)
    fetchProfile.add(IMAPFolder.FetchProfileItem.HEADERS)


    fetchProfile.add("References")
    fetchProfile.add("Message-ID")
    fetchProfile.add("In-Reply-To")

    inbox match {
      case folder: GmailFolder =>
        fetchProfile.add(GmailFolder.FetchProfileItem.MSGID)
        fetchProfile.add(GmailFolder.FetchProfileItem.THRID)
        fetchProfile.add(GmailFolder.FetchProfileItem.LABELS)

        folder.fetch(messages, fetchProfile)
        messages.toVector.asInstanceOf[Vector[GmailMessage]]

      case _ =>

        inbox.fetch(messages, fetchProfile)
        messages.toVector

    }

  }


  private final def _getAllNonEmptyFolders(
                                    inboxEmailSettingId: Long,
                                    store: Store,
                                    Logger: SRLogger
                                  ): Try[Seq[IMAPFolder]] = blocking {
    Try {

      val folders = store.getDefaultFolder.list("*").toSeq.asInstanceOf[Seq[IMAPFolder]]

      val updatedFolders = folders.filter(f => {
        (f.getType & javax.mail.Folder.HOLDS_MESSAGES) != 0
      })

      Logger.info(s"\n\n\n\n updatedFolders $updatedFolders")
      val nonEmptyFolders: Seq[IMAPFolder] = updatedFolders.filter(f => {

        val folderName: Option[String] = Try {
          f.getFullName
        } match {
          case Failure(e) =>
            Logger.error(s"[SmtpImapService] getAllNonEmptyFolders ($inboxEmailSettingId) getFullName", err = e)
            None

          case Success(c) => Option(c) // c could be "null" as well (JAVA).
        }

        val messageCount: Int = Try {
          f.getMessageCount
        } match {
          case Failure(e) =>

            Logger.error(s"[SmtpImapService] getAllNonEmptyFolders ($inboxEmailSettingId :: $folderName) getMessageCount", err = e)

            0

          case Success(c) => c
        }

         Logger.info(s"\n\n\n\n folder: ${f.getFullName} :: ${f.getName} :: $messageCount :: ${f.getAttributes.toList.map(_.toString)}")

        messageCount > 0

      })

      nonEmptyFolders

    }
  }

  final def getRecentImapMessagesForTestSettings(
                                   inbox: Folder,
                                   emailAfterDate: DateTime,
                                   isTestingSettings: Boolean = false,
                                   Logger: SRLogger,
                                   senderEmail: Option[String] = None
                                 ): Try[Vector[Message]] = blocking {
    Try {

      val convertedEmailAfterDate = emailAfterDate.withTimeAtStartOfDay().withZone(DateTimeZone.getDefault).toDate

      Logger.info(s"[TEmailService] getRecentImapMessagesForTestSettings recent messages: ${inbox.getFullName} :: $emailAfterDate :: actual: ${convertedEmailAfterDate}")

      //    val olderThan: SearchTerm = new ReceivedDateTerm(ComparisonTerm.LT, DateTime.now.toDate)

      // NOTE: The IMAP protocol only provides resolution to the day, no time.
      // REF: http://stackoverflow.com/questions/15967234/javamail-fetching-messages-for-the-last-hour
      val newerThanSentDate: SearchTerm = new SentDateTerm(ComparisonTerm.GE, convertedEmailAfterDate)
      val newerThanReceivedDate: SearchTerm = new ReceivedDateTerm(ComparisonTerm.GE, convertedEmailAfterDate)

      val newerThan = new OrTerm(newerThanSentDate, newerThanReceivedDate)

      //    val andTerm: SearchTerm = new AndTerm(olderThan, newerThan)

      // Add a search term for the sender's email if provided
      val searchTerms = senderEmail match {

        case Some(email) =>

          val fromTerm: SearchTerm = new FromTerm(new InternetAddress(email))

          new AndTerm(newerThan, fromTerm)

        case None =>

          newerThan

      }

      var messages: Array[Message] = inbox.search(searchTerms)

      if (isTestingSettings && messages.length > 500) {
        Logger.info(s"[TEmailService] getRecentMessages isTestingSettings: $isTestingSettings :: recent messages: ${inbox.getFullName} :: $emailAfterDate :: actual: ${emailAfterDate} :: total: ${messages.size} taking only 500")

        messages = messages.take(500)
      }

      addFetchProfilePropsAndFetch(messages = messages, inbox = inbox)


    }
  }


  private def searchMessageByBodyTerm(
                                       folder: Folder,
                                       emailScheduled: EmailScheduled,
                                       bodySearchTerm: String,
                                       Logger: SRLogger
                                     ): Try[Vector[TrackedEmailMessageWithCategory]] = blocking {

    Try {

      val emailAfterDate = if (emailScheduled.sent_at.isDefined) {
        emailScheduled.sent_at.get.minusDays(1)
      } else emailScheduled.scheduled_at.get.minusDays(1)

      val convertedEmailAfterDate = Date.from(emailAfterDate.toLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant())

      val newerThanReceivedDate: SearchTerm = new ReceivedDateTerm(ComparisonTerm.GE, convertedEmailAfterDate)

      // val formattedSubjectToSearch = EmailHelper.getSubjectToSearch(emailScheduled.subject)

      val body = new BodyTerm(bodySearchTerm)

      val fromAndSubjectAndReceivedAtTerm = new AndTerm(body, newerThanReceivedDate)

      val messages = folder.search(fromAndSubjectAndReceivedAtTerm)

      val messagesWithProfile = addFetchProfilePropsAndFetch(messages = messages, inbox = folder)

      val emailsTracked = messagesWithProfile.map(m => {
        TrackedEmailMessageWithCategory(m, None)
      })

      emailsTracked
    }
  }

  private def bounceCheckEmailDetails(
                                       messageWithCategory: TrackedEmailMessageWithCategory,
                                       Logger: SRLogger
                                     ): Option[BounceCheckDetails] = {

    val tryOfHeadersMap: Try[Map[String, String]] = Try {

      var headersMap = Map[String, String]()

      val headersEnum = messageWithCategory.message.getAllHeaders

      while (headersEnum.hasMoreElements) {

        val header = headersEnum.nextElement()

        val headerName = header.getName

        val headerValue = header.getValue

        headersMap = headersMap + (headerName -> headerValue)

      }

      headersMap

    }

    val headers: Map[String, String] = tryOfHeadersMap match {

      case Failure(exception) =>

        Logger.error(
          msg = s"Failed to headers - message: ${messageWithCategory.message}",
          err = exception
        )

        Map[String, String]()

      case Success(headers) =>

        headers

    }

    val full_headers: JsValue = Json.toJson(headers)

    val message = messageWithCategory.message

    val fromEmailOpt: Option[IEmailAddress] = IEmailAddress.getFromEmailFromJavaMessage(
      message = message,
      Logger = Logger
    )

    val subject: String = Try {
      message.getSubject
    } match {
      case Failure(e) =>
        Logger.error(s"[TEmailService] getSubject error :: fromEmail: $fromEmailOpt", err = e)

        ""

      case Success(value) => if (value == null) "" else value
    }

    val (_, htmlBodyRaw) = EmailReplyTrackingCommonService.getTextFromMessage(
      message = message,
      Logger = Logger
    )

    val receivedDate: DateTime = Try {

      val rd = message.getReceivedDate

      if (rd == null) {

        Logger.error(s"[TEmailService] getReceivedDate :: fromEmail: $fromEmailOpt :: getReceivedDate: $rd: is null")

        DateTime.now()

      } else {
        new DateTime(rd)
      }

    } match {
      case Failure(e) =>
        Logger.error(s"[TEmailService] getReceivedDate :: fromEmail: $fromEmailOpt :: getReceivedDate", err = e)
        DateTime.now()

      case Success(date) => date
    }

    val references: Array[String] = Try {

      message.getHeader("References")

    } match {

      case Failure(exception) =>

        Logger.error(
          s"[TEmailService] getHeader - References error :: fromEmail: $fromEmailOpt",
          err = exception
        )

        Array()

      case Success(references) =>

        if (references == null) {

          Array()

        } else {

          references

        }
    }

    fromEmailOpt match {
      case None =>

        Logger.error(s"bounceCheckEmailDetails - fromEmailOpt: None :: messageWithCategory: $messageWithCategory")

        None

      case Some(fromEmail) =>
        Some(
          BounceCheckDetails(
            emailHtmlBody = htmlBodyRaw,
            subject = subject,
            fromEmail = fromEmail.email,
            fromName = fromEmail.name.getOrElse(""),
            received_at = receivedDate,
            references_header = references,
            fullHeaders = full_headers,
          )
        )
    }
  }


  def fetchMessagesWithBodySearchTerm(
                                       bodySearchTerm: String,
                                       emailScheduled: EmailScheduled,
                                       receiveCredentials: EmailSettings.ImapEmailAccount,
                                       Logger: SRLogger
                                     )(
                                       implicit wsClient: WSClient,
                                       ec: ExecutionContext,
                                     ): Future[Vector[BounceCheckDetails]] = {

    val tryOfFilteredMessages: Try[Vector[BounceCheckDetails]] = for {

      store: Store <- openImapConnection(
        emailSetting = receiveCredentials,
        Logger = Logger
      )

      filteredMessages: Vector[BounceCheckDetails] <- fetchMessagesWithBodySearchTermInternal(
        store = store,
        emailScheduled = emailScheduled,
        bodySearchTerm = bodySearchTerm,
        Logger = Logger
      )

      // return type is Unit. So, not adding explicit return type.
      _ <- closeImapConnection(store)

    } yield {

      filteredMessages
    }

    Future.fromTry(tryOfFilteredMessages)
  }

  protected def fetchMessagesWithBodySearchTermInternal(
                                                         store: Store,
                                                         emailScheduled: EmailScheduled,
                                                         bodySearchTerm: String,
                                                         Logger: SRLogger
                                                       ): Try[Vector[BounceCheckDetails]] = blocking {
    Try {

      // Have to use `emailScheduled.sender_email_account_id` as we want to check sender inbox.
      _getAllNonEmptyFolders(inboxEmailSettingId = emailScheduled.sender_email_account_id, store = store,
        Logger = Logger
      ) match {

        case Failure(e) =>

          Logger.fatal(s"[SmtpImapService] getFilteredMessagesFromAllFolders", err = e)

          throw e

        case Success(folders) =>

          var vectorMessages: Vector[BounceCheckDetails] = Vector()

          folders.map { f =>

            Logger.info(s"next folder ${f.getFullName}}")

            f.open(Folder.READ_WRITE)

            searchMessageByBodyTerm(folder = f, emailScheduled = emailScheduled, bodySearchTerm = bodySearchTerm, Logger = Logger) match {

              case Failure(e) =>

                Logger.fatal(s"searchMessageBySenderAndSubject error: ${Helpers.getStackTraceAsString(e)}")

                val errMsg = s"Error from searchMessageBySenderAndSubject. (TraceId: ${Logger.logRequestId})"

                Future.failed(new Exception(errMsg))

              case Success(imapMessages) =>

                // Note: Don't not invoke any methods on the messaging object (`Message`)
                // after the folder has been closed.

                val bounceCheckDetailsList: Vector[BounceCheckDetails] = imapMessages
                  .map { message =>
                    bounceCheckEmailDetails(
                      messageWithCategory = message,
                      Logger = Logger
                    )
                  }
                  .filter(bounceCheckDetailsOpt => bounceCheckDetailsOpt.nonEmpty)
                  .map(bounceCheckDetailsOpt => bounceCheckDetailsOpt.get) // Note: This .get is only safe because of the .nonEmpty check in the above filter

                vectorMessages = vectorMessages ++ bounceCheckDetailsList

                closeFolder(folder = f)
            }
          }

          vectorMessages
      }
    }
  }

  def getFilteredMessagesFromAllFolders(
                                         receiveCredentials: EmailSettings.ImapEmailAccount,
                                         emailScheduled: EmailScheduled,
                                         Logger: SRLogger
                                       )(
                                         implicit wsClient: WSClient,
                                         ec: ExecutionContext,
                                       ): Future[Option[EmailMessageTrackedFullOjb]] = {

    val tryOfFilteredMessages: Try[Option[EmailMessageTrackedFullOjb]] = for {

      store: Store <- openImapConnection(
        emailSetting = receiveCredentials,
        Logger = Logger
      )

      filteredMessages: Option[EmailMessageTrackedFullOjb] <- getFilteredMessagesFromAllFoldersInternal(
        store = store,
        emailScheduled = emailScheduled,
        Logger = Logger
      )

      // return type is Unit. So, not adding explicit return type.
      _ <- closeImapConnection(store)

    } yield {

      filteredMessages
    }

    Future.fromTry(tryOfFilteredMessages)
  }

  protected def getFilteredMessagesFromAllFoldersInternal(
                                                         store: Store,
                                                         emailScheduled: EmailScheduled,
                                                         Logger: SRLogger
                                                       ): Try[Option[EmailMessageTrackedFullOjb]] = blocking {
    Try {

      _getAllNonEmptyFolders(inboxEmailSettingId = emailScheduled.receiver_email_account_id, store = store,
        Logger = Logger
      ) match {

        case Failure(e) =>

          Logger.fatal(s"[SmtpImapService] getFilteredMessagesFromAllFolders", err = e)

          throw e

        case Success(folders) =>

          var vectorMessages: Vector[EmailMessageTrackedFullOjb] = Vector()
          folders
//            .filter(f => {
//              val name = Option(f.getFullName).getOrElse("").trim.toLowerCase()
//
//
//              if (
//                name == "junk" ||
//                  name.contains("sent") ||
//                  name.contains("trash") ||
//                  name.contains("calendar") ||
//                  name.contains("deleted") ||
//                  name.contains("draft")
//
//              ) {
//
//                false
//
//              } else {
//
//                true
//              }
//
//
//            })
            .map(f => {

              if (vectorMessages.isEmpty) {

                Logger.info(s"next folder ${f.getFullName}}")

                f.open(Folder.READ_WRITE)

                searchMessageBySenderAndSubject(folder = f, emailScheduled = emailScheduled, Logger = Logger) match {

                  case Failure(e) =>

                    Logger.fatal(s"searchMessageBySenderAndSubject error: ${Helpers.getStackTraceAsString(e)}")

                    val errMsg = s"Error from searchMessageBySenderAndSubject. (TraceId: ${Logger.logRequestId})"

                    Future.failed(new Exception(errMsg))

                  case Success(imapMessages) =>

                    Logger.info(s"imapMessages $imapMessages :: folder ${f.getFullName}")

                    if (imapMessages.nonEmpty) {

                      val folderName = Option(f.getFullName).getOrElse("").trim.toLowerCase()

                      if (
                        folderName == "junk" ||
                          folderName.contains("sent") ||
                          folderName.contains("trash") ||
                          folderName.contains("calendar") ||
                          folderName.contains("deleted") ||
                          folderName.contains("draft")
                      ) {

                        // If the message is found in any of the filtered folders,
                        // not taking any action, just logging it.

                        Logger.error(
                          s"[EmailLandingService] Message found in filtered folder. folderName: $folderName :: from_email: ${emailScheduled.from_email} :: sender_email_account_id: ${emailScheduled.sender_email_account_id} :: to_email: ${emailScheduled.to_email} :: receiver_email_account_id: ${emailScheduled.receiver_email_account_id} :: subject: ${emailScheduled.subject}"
                        )

                        closeFolder(folder = f)

                      } else {

                        val messageTracked = imapMessages.head

                        val msg = messageTracked.message.asInstanceOf[IMAPMessage]

                        val originalLandedFolder = msg.getFolder.getFullName

                        var landedFolder = msg.getFolder.getFullName

                        val landedCategory = messageTracked.category

                        val important = IMPORTANT_FOLDER_NAME

                        val inbox = INBOX_FOLDER_NAME

                        val spam = SPAM_FOLDER_NAME

                        var landedFolderType = FolderType.INBOX

                        val markImpOpenMoveToInboxTry = Try {

                          markAsOpen(store = store, message = msg)

                          if (landedFolder == spam) {
                            landedFolder = inbox
                            landedFolderType = FolderType.SPAM
                            moveMessageSpamToInbox(store = store, message = msg, inbox = inbox, spam = spam)
                          }

                          markAsImportant(store = store, message = msg, landedFolder = landedFolder, important = important)

                          if (landedCategory.isDefined) {
                            landedFolder = landedCategory.get
                            landedFolderType = FolderType.CATEGORY
                          }

                          closeFolder(folder = f)

                        }


                        markImpOpenMoveToInboxTry match {

                          case Failure(e) =>

                            Logger.fatal(s"markImpOpenMoveToInboxTry error: ${Helpers.getStackTraceAsString(e)}")

                            val errMsg = s"Error from markImpOpenMoveToInboxTry. (TraceId: ${Logger.logRequestId})"
                            Future.failed(new Exception(errMsg))

                          case Success(_) =>

                            val msgs = EmailMessageTrackedFullOjb(
                              // message = messageTracked.message,
                              folder = landedFolder,
                              originalLandedFolder = originalLandedFolder,
                              landedFolderType = landedFolderType,
                              messageIdOpt = None
                            )

                            vectorMessages = vectorMessages ++ Seq(msgs)

                        }
                      }
                    } else {

                      closeFolder(folder = f)

                    }
                }
              }
            })

          if (vectorMessages.nonEmpty) {
            Some(vectorMessages.head)
          }
          else {
            Logger.error(
              s"[EmailLandingService] getFilteredMessagesFromAllFoldersInternal failed - vectorMessages Empty. email_scheduled_id: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: sender_email_account_id: ${emailScheduled.sender_email_account_id} :: to_email: ${emailScheduled.to_email} :: receiver_email_account_id: ${emailScheduled.receiver_email_account_id} :: subject: ${emailScheduled.subject}"
            )
            None
          }
      }


    }
  }


  private final def moveMessageSpamToInbox(store: Store, message: IMAPMessage, inbox: String, spam: String): Try[IMAPMessage] = {

    for {

      inbox: IMAPFolder <- openIMAPFolder(store = store, folderName = inbox)

      spam: IMAPFolder <- openIMAPFolder(store = store, folderName = spam)

      // return type is Unit. So, not adding explicit return type.
      move <- Try {
        spam.moveMessages(Array(message), inbox)
      }

      // return type is Unit. So, not adding explicit return type.
      inboxClose <- closeFolder(folder = inbox)

      // return type is Unit. So, not adding explicit return type.
      spamClose <- closeFolder(folder = spam)

    } yield {
      message
    }

  }


  private final def markAsImportant(store: Store, message: IMAPMessage, landedFolder: String, important: String): Try[IMAPMessage] = {

    for {

      folder: IMAPFolder <- openIMAPFolder(store = store, folderName = landedFolder)

      important: IMAPFolder <- openIMAPFolder(store = store, folderName = important)

      // return type is Unit. So, not adding explicit return type.
      copy <- Try {
        folder.copyMessages(Array(message), important)
      }

      // return type is Unit. So, not adding explicit return type.
      inboxClose <- closeFolder(folder = folder)

      // return type is Unit. So, not adding explicit return type.
      spamClose <- closeFolder(folder = important)

    } yield {
      message
    }

  }

  private final def markAsOpen(store: Store, message: IMAPMessage) =  Try {

    message.setFlag(Flags.Flag.SEEN, true);

  }
}
