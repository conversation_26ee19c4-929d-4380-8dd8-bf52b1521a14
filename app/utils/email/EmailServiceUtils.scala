package utils.email

import play.api.libs.json.Format
import utils.enum.EnumUtils


object EmailCategories {
  val gmail: Seq[String] = Seq("promotions", "social", "updates", "forums")
  val outlook: Seq[String] = Seq()
  val other: Seq[String] = Seq()
}

object FolderType extends Enumeration {

  type PlanType = Value
  val INBOX = Value("inbox")
  val SPAM = Value("spam")
  val CATEGORY = Value("category")
  val BOUNCED = Value("bounced")
  val LANDING_CHECK_FAILED = Value("landing_check_failed")

  implicit val format: Format[Value] = EnumUtils.enumFormat(FolderType)

}


object IMAPStanderdFolderTypes extends Enumeration {

   type PlanType = Value

   val INBOX = Value("INBOX")
   val JUNK = Value("Junk")
   val IMPORTANT = Value("Important")

   val GMAIL_SPAM = Value("[Gmail]/Spam")
   val GMAIL_IMPORTANT = Value("[Gmail]/Important")

   val OUTLOOK_JUNK = Value("Junk Email")

  implicit val format: Format[Value] = EnumUtils.enumFormat(IMAPStanderdFolderTypes)

}

object SubjectTags  {

  val RE = "RE:"

//  val SUFFIX = "warmupbox"
//  val SUFFIX = "wmbox"
//  val SUFFIX = "wmpbox"
  val SUFFIX = "wrmpbx"

}

