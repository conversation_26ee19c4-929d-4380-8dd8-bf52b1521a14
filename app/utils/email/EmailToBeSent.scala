package api.emails

import ai.x.play.json.Jsonx
import io.smartreach.esp.api.emails.IEmailAddress
import play.api.libs.json.OFormat


case class EmailToBeSent(

                          to_emails: Seq[IEmailAddress],
                          from_email: String,
                          from_name: String,
                          subject: String,
                          body: String,
                          text_body: String,

                          message_id: Option[String],

                          sender_email_settings_id: Long,
                          is_reply: Boolean,

                          in_reply_to_header: Option[String],
                          references_header: Option[String]
                        )
object EmailToBeSent {
  implicit lazy val format: OFormat[EmailToBeSent] = Jsonx.formatCaseClassUseDefaults[EmailToBeSent]
}
