package utils.email


import akka.actor.ActorSystem
import api.emails.EmailToBeSent
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}


trait EmailSendService[SendCredentials, SendResponse] {

  def sendEmail(
                 sendCredentials: SendCredentials,
                 email: EmailToBeSent
               )(
                 implicit wsClient: WSClient,
                 ec: ExecutionContext,
                 system: ActorSystem,
                 Logger: SRLogger,
               ): Future[SendResponse]
}
