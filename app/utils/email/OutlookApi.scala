package utils.email

import akka.actor.ActorSystem
import api.AppConfig
import api.emails.EmailToBeSent
import api.services.EmailAccountService
import api.models.{EmailAccount, EmailAccountId, EmailScheduled, EmailSettings}
import org.joda.time.DateTime
import io.smartreach.esp.api.emails.{AccessToken, EmailSettingId, InternetMessageId, MSSendEmailRes, OutlookMessageId, SREmailToBeSent}
import io.smartreach.esp.api.microsoftOAuth.{EmailSettingUpdateAccessToken, MicrosoftOAuthSettings}
import io.smartreach.esp.utils.email.{OutlookEmailSendDetails, OutlookJustLogException, OutlookReceiveEmailService, OutlookSendEmailService, OutlookUtilsApi, OutlookUtilsService}
import io.smartreach.esp.utils.email.models.EmailReply.OutlookReplyTrackedViaAPI
import io.smartreach.esp.utils.email.models.OutlookFolderTrackedViaAPI
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class SendEmailResponse(
                              messageId: InternetMessageId,
                            )


object OutlookApi
  extends EmailSendService[EmailSettings.OAuthTokens, SendEmailResponse]
    with EmailReceiveService[EmailSettings.OAuthTokens, OutlookReplyTrackedViaAPI]
    with OutlookReceiveEmailService
    with OutlookSendEmailService {

  override val INBOX_FOLDER_NAME: String = IMAPStanderdFolderTypes.INBOX.toString
  override val SPAM_FOLDER_NAME: String = IMAPStanderdFolderTypes.OUTLOOK_JUNK.toString

  // Not Used
  override val IMPORTANT_FOLDER_NAME: String = IMAPStanderdFolderTypes.IMPORTANT.toString

  // Not Used
  override val CATEGORIES: Seq[String] = EmailCategories.outlook

  private def refreshAccessTokenIfExpired(
                                           email: String,
                                           emailSettingId: EmailSettingId,
                                           credentials: EmailSettings.OAuthTokens,
                                           s: MicrosoftOAuthSettings
                                         )(
                                           implicit wsClient: WSClient,
                                           ec: ExecutionContext,
                                         ): Future[AccessToken] = {

    if (credentials.oauth2_access_token_expires_at.isBeforeNow) {

      refreshAccessToken(
        email = email,
        emailSettingId = emailSettingId,
        refreshToken = credentials.oauth2_refresh_token,
        s = s
      )
        .map(res => AccessToken(id = res.access_token))

    } else {

      Future.successful(
        AccessToken(id = credentials.oauth2_access_token)
      )
    }

  }

  override def sendEmail(
                          sendCredentials: EmailSettings.OAuthTokens,
                          email: EmailToBeSent
                        )(
                          implicit wsClient: WSClient,
                          ec: ExecutionContext,
                          system: ActorSystem,
                          Logger: SRLogger
                        ): Future[SendEmailResponse] = {

    val outlookMessageIdFutOpt: Future[Option[OutlookMessageId]] = if (email.is_reply) {

      for {

        accessToken: AccessToken <- refreshAccessTokenIfExpired(
          email = email.from_email,
          emailSettingId = EmailSettingId(emailSettingId = email.sender_email_settings_id),
          credentials = sendCredentials,

          // Should not matter if we pass isZapmailFlow = true/false,
          // but we are passing it as false as zapmail is only needed in the auth flow.
          // As redirect url is not getting used for `refreshAccessTokenIfExpired`
          s = EmailAccountService.getMicrosoftOAuthSettingsForAccount(
            emailAccountId = EmailAccountId(id = email.sender_email_settings_id),
            isZapmailFlow = false,
          )
        )

        outlookMessageIdOpt: Option[OutlookMessageId] <- if (email.in_reply_to_header.isDefined) {

          getMessageByInternetIDViaAPI(
            accessToken = accessToken,
            internetMessageId = InternetMessageId(id = email.in_reply_to_header.get),
            Logger = Logger
          )
            .map(m => Some(OutlookMessageId(id = m.outlook_msg_id)))
            .recoverWith {
              case e: OutlookJustLogException =>

                logger.error(
                  s"sendEmail - ${e.getMessage} :: internetMessageId: ${email.in_reply_to_header.get} :: from_email: ${email.from_email} :: sender_email_account_id: ${email.sender_email_settings_id} :: subject: ${email.subject}",
                  error = e
                )

                Future.failed(
                  MQDoNotNackException(message = e.getMessage, cause = e)
                )

              case e =>

                logger.error(
                  s"sendEmail - ${e.getMessage} :: internetMessageId: ${email.in_reply_to_header.get} :: from_email: ${email.from_email} :: sender_email_account_id: ${email.sender_email_settings_id} :: subject: ${email.subject}",
                  error = e
                )

                Future.failed(e)
            }

        } else {

          val errMsg = s"in_reply_to_header not defined for reply. from_email: ${email.from_email} :: to_emails: ${email.to_emails} :: sender_email_settings_id: ${email.sender_email_settings_id}"

          Logger.error(errMsg)

          Future.failed(new Exception(errMsg))
        }

      } yield {

        outlookMessageIdOpt
      }
    } else {

      Future.successful(None)
    }

    val srEmailToBeSent = SREmailToBeSent(
      to_emails = email.to_emails,
      from_email = email.from_email,
      cc_emails = Seq(),
      bcc_emails = Seq(),
      from_name = email.from_name,
      reply_to_email = None,
      reply_to_name = None,
      subject = email.subject,
      textBody = email.text_body,
      htmlBody = email.body,
      message_id = email.message_id,
      references_header = email.references_header,
      sender_email_settings_id = email.sender_email_settings_id
    )

    outlookMessageIdFutOpt.flatMap { outlookMessageIdOpt =>

      val outlookEmailSendDetails = OutlookEmailSendDetails(
        from_email = email.from_email,
        oauth2_refresh_token = Some(sendCredentials.oauth2_refresh_token),
        sender_email_setting_id = EmailSettingId(emailSettingId = email.sender_email_settings_id),
        in_reply_to_outlook_msg_id = outlookMessageIdOpt.map(_.id)
      )

      sendEmailViaOutlookApi(
        base_64_flow = true,
        send_plain_text_email = Some(false),
        outlookEmailSendDetails = outlookEmailSendDetails,
        emailToBeSent = srEmailToBeSent,

        // Should not matter if we pass isZapmailFlow = true/false,
        // but we are passing it as false as zapmail is only needed in the auth flow.
        // As redirect url is not getting used for `sendEmailViaOutlookApi`
        microsoftOAuthSettings = EmailAccountService.getMicrosoftOAuthSettingsForAccount(
          emailAccountId = EmailAccountId(id = email.sender_email_settings_id),
          isZapmailFlow = false,
        )
      )
        .flatMap { msSendEmailRes =>

          val messageIdOpt: Option[InternetMessageId] = msSendEmailRes.message_id

          if (messageIdOpt.isEmpty) {

            val errMsg = s"Failed to retrieve InternetMessageId: $messageIdOpt. from_email: ${email.from_email} :: sender_email_settings_id: ${email.sender_email_settings_id}"

            Logger.error(errMsg)

            Future.failed(new Exception(errMsg))

          } else {
            Future.successful(
              SendEmailResponse(
                messageId = messageIdOpt.get
              )
            )
          }
        }
        .recoverWith {
          case e: OutlookJustLogException =>
            Future.failed(
              MQDoNotNackException(message = e.getMessage, cause = e)
            )

          case e =>
            Future.failed(e)
        }
    }
  }


  override def fetchRecentMessagesForTestingImap(
                                                  emailAfterDate: DateTime,
                                                  receiveCredentials: EmailSettings.OAuthTokens,
                                                  Logger: SRLogger
                                                )(
                                                  implicit wsClient: WSClient,
                                                  ec: ExecutionContext,
                                                ): Future[Vector[OutlookReplyTrackedViaAPI]] = {

    // Note: As we are calling this function when creating an EmailAccount,
    // we should already have valid AccessToken.

    getRecentMessagesViaAPI(
      accessToken = receiveCredentials.oauth2_access_token,
      emailAfterDate = emailAfterDate,
      emailTillDate = None,
      Logger = Logger,
    )
      .map(_.toVector)
      .recoverWith {
        case e: OutlookJustLogException =>
          Future.failed(
            MQDoNotNackException(message = e.getMessage, cause = e)
          )

        case e =>
          Future.failed(e)
      }
  }

  def fetchMessagesWithBodySearchTerm(
                                       bodySearchTerm: String,
                                       emailScheduled: EmailScheduled,
                                       receiveCredentials: EmailSettings.OAuthTokens,
                                       Logger: SRLogger
                                     )(
                                       implicit wsClient: WSClient,
                                       ec: ExecutionContext,
                                     ): Future[Vector[BounceCheckDetails]] = {

    searchMessagesByBodyTermViaAPI(
      bodySearchTerm = bodySearchTerm,
      accessToken = AccessToken(id = receiveCredentials.oauth2_access_token),
      Logger = Logger,
    )
      .map { messages =>
        messages.map { message =>
          BounceCheckDetails(
            subject = message.commonPropsEmailMessage.subject,
            emailHtmlBody = message.commonPropsEmailMessage.base_body,
            fromEmail = message.commonPropsEmailMessage.from.email,
            fromName = message.commonPropsEmailMessage.from.name.getOrElse(""),
            received_at = message.commonPropsEmailMessage.received_at,
            references_header = message.commonPropsEmailMessage.references_header.toArray,
            fullHeaders = message.commonPropsEmailMessage.full_headers,
          )
        }.toVector
      }
      .recoverWith {
        case e: OutlookJustLogException =>
          Future.failed(
            MQDoNotNackException(message = e.getMessage, cause = e)
          )

        case e =>
          Future.failed(e)
      }
  }

  override def getFilteredMessagesFromAllFolders(
                                                  receiveCredentials: EmailSettings.OAuthTokens,
                                                  emailScheduled: EmailScheduled,
                                                  Logger: SRLogger
                                                )(
                                                  implicit wsClient: WSClient,
                                                  ec: ExecutionContext,
                                                ): Future[Option[EmailMessageTrackedFullOjb]] = {

    if (emailScheduled.message_id.isEmpty) {

      val errMsg = s"messageId is empty for getFilteredMessagesFromAllFolders. emailScheduledId: ${emailScheduled.id} :: sender_email_account_id: ${emailScheduled.sender_email_account_id} :: receiver_email_account_id: ${emailScheduled.receiver_email_account_id}"

      Logger.error(errMsg)

      Future.failed(new Exception(errMsg))
    } else {

      val messageId = InternetMessageId(id = emailScheduled.message_id.get)

      // TODO: Fixme DI
      val outlookUtilsService = new OutlookUtilsService

      for {

        accessToken: AccessToken <- refreshAccessTokenIfExpired(
          email = emailScheduled.to_email,
          emailSettingId = EmailSettingId(emailSettingId = emailScheduled.receiver_email_account_id),
          credentials = receiveCredentials,

          // Should not matter if we pass isZapmailFlow = true/false,
          // but we are passing it as false as zapmail is only needed in the auth flow.
          // As redirect url is not getting used for `refreshAccessTokenIfExpired`
          s = EmailAccountService.getMicrosoftOAuthSettingsForAccount(
            emailAccountId = EmailAccountId(id = emailScheduled.receiver_email_account_id),
            isZapmailFlow = false,
          )
        )

        folders: List[OutlookFolderTrackedViaAPI] <- outlookUtilsService.getAllFoldersViaAPI(
          accessToken = accessToken,
          Logger = Logger
        )
          .recoverWith {
            case e: OutlookJustLogException =>
              Future.failed(
                MQDoNotNackException(message = e.getMessage, cause = e)
              )

            case e =>
              Future.failed(e)
          }

        //        filteredFolders: List[OutlookFolderTrackedViaAPI] = folders.filter { f =>
        //
        //          if (
        //            f.folder_name == "junk" ||
        //              f.folder_name.contains("sent") ||
        //              f.folder_name.contains("trash") ||
        //              f.folder_name.contains("calendar") ||
        //              f.folder_name.contains("deleted") ||
        //              f.folder_name.contains("draft")
        //          ) {
        //
        //            false
        //
        //          } else {
        //
        //            true
        //          }
        //        }

        message: OutlookReplyTrackedViaAPI <- getMessageByInternetIDViaAPI(
          accessToken = accessToken,
          internetMessageId = messageId,
          Logger = Logger
        )
          .recoverWith {
            case e: OutlookJustLogException =>

              logger.error(
                s"getFilteredMessagesFromAllFolders - ${e.getMessage} :: internetMessageId: $messageId :: from_email: ${emailScheduled.from_email} :: sender_email_account_id: ${emailScheduled.sender_email_account_id} :: to_email: ${emailScheduled.to_email} :: receiver_email_account_id: ${emailScheduled.receiver_email_account_id} :: subject: ${emailScheduled.subject}",
                error = e
              )

              Future.failed(
                MQDoNotNackException(message = e.getMessage, cause = e)
              )

            case e =>

              logger.error(
                s"getFilteredMessagesFromAllFolders - ${e.getMessage} :: internetMessageId: $messageId :: from_email: ${emailScheduled.from_email} :: sender_email_account_id: ${emailScheduled.sender_email_account_id} :: to_email: ${emailScheduled.to_email} :: receiver_email_account_id: ${emailScheduled.receiver_email_account_id} :: subject: ${emailScheduled.subject}",
                error = e
              )

              Future.failed(e)
          }

        messageFolderOpt: Option[OutlookFolderTrackedViaAPI] = folders.find { f =>
          f.folder_id == message.outlook_parent_folder_id
        }

        messageFolder: OutlookFolderTrackedViaAPI <- if (messageFolderOpt.isEmpty) {

          val errMsg = s"messageFolderOpt.isEmpty, parentFolderId of the message didn't match with any folderId, messageParentFolderId: ${message.outlook_parent_folder_id} :: folders: $folders"

          Logger.error(errMsg)

          Future.failed(new Exception(errMsg))
        } else {
          Future.successful(messageFolderOpt.get)
        }

        msSendEmailRes: MSSendEmailRes <- if (messageFolder.folder_name.toLowerCase == SPAM_FOLDER_NAME.toLowerCase) {
          OutlookUtilsApi.moveToInbox(
            outlookMessageId = OutlookMessageId(id = message.outlook_msg_id),
            accessToken = accessToken,
            Logger = Logger
          )
            .recoverWith {
              case e: OutlookJustLogException =>
                Future.failed(
                  MQDoNotNackException(message = e.getMessage, cause = e)
                )

              case e =>
                Future.failed(e)
            }
        } else {

          // If message is not in spam folder, then we create a MSSendEmailRes from previous Response.
          Future {
            MSSendEmailRes(
              message_id = Some(messageId),
              outlook_msg_id = Some(OutlookMessageId(id = message.outlook_msg_id)),
              outlook_conversation_id = None,
              outlook_response_json = None
            )
          }
        }

        sendEmailResOutlookMessageId: OutlookMessageId <- if (msSendEmailRes.outlook_msg_id.isEmpty) {

          val errMsg = s"msSendEmailRes.outlook_msg_id.isEmpty. previousOutlookMessageId: ${message.outlook_msg_id} :: receiver_email_account_id: ${emailScheduled.receiver_email_account_id}"

          Logger.error(errMsg)

          Future.failed(new Exception(errMsg))
        } else {
          Future.successful(msSendEmailRes.outlook_msg_id.get)
        }

        // Note: If I add the type compiler says unreachable code.
        _ <- OutlookUtilsApi.markAsImportantAndOpen(
          outlookMessageId = sendEmailResOutlookMessageId,
          accessToken = accessToken,
          Logger = Logger
        )
          .recoverWith {
            case e: OutlookJustLogException =>
              Future.failed(
                MQDoNotNackException(message = e.getMessage, cause = e)
              )

            case e =>
              Future.failed(e)
          }

      } yield {

        // As Outlook does not have any special categories, if original landed folder is not SPAM or INBOX,
        // then we consider it in CATEGORY.

        val (landedFolderType: FolderType.Value, landedFolder: String) =
          if (messageFolder.folder_name.toLowerCase == SPAM_FOLDER_NAME.toLowerCase) {

            (FolderType.SPAM, INBOX_FOLDER_NAME)

          } else if (messageFolder.folder_name.toLowerCase == INBOX_FOLDER_NAME.toLowerCase) {

            (FolderType.INBOX, INBOX_FOLDER_NAME)

          } else {

            (FolderType.CATEGORY, messageFolder.folder_name)
          }

        if (msSendEmailRes.message_id.isEmpty) {

          val errMsg = s"msSendEmailRes.message_id.isEmpty. receiver_email_account_id: ${emailScheduled.receiver_email_account_id}"

          Logger.error(errMsg)

          throw new Exception(errMsg)
        } else {

          Some(
            EmailMessageTrackedFullOjb(
              folder = landedFolder,
              originalLandedFolder = messageFolder.folder_name,
              landedFolderType = landedFolderType,
              messageIdOpt = msSendEmailRes.message_id
            )
          )
        }
      }
    }
  }

  override def updateAccessTokenAndRefreshToken(
                                                 emailSettingId: EmailSettingId,
                                                 data: EmailSettingUpdateAccessToken
                                               ): Try[Option[EmailSettingId]] = {

    EmailAccount.updateAccessTokenAndRefreshToken(
      emailAccountId = emailSettingId.emailSettingId,
      data = data
    )
  }
}
