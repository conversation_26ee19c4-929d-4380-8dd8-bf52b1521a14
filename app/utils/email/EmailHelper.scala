package utils.email

import java.util.Date

import akka.http.scaladsl.model.headers.ByteRange.Suffix
import api.models.EmailTemplate
import org.jsoup.Jsoup
import  org.jsoup.safety.Safelist
import api.{AppConfig, CONSTANTS}
import javax.mail.Folder
import play.api.Logger
import play.api.libs.ws.{WSAuthScheme, WSClient}
import utils.{Helpers, SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import org.jsoup.nodes.Document.OutputSettings
import org.jsoup.parser.Parser
import utils.security.EncryptionService



object EmailHelper {

  private val mailgunDomain = AppConfig.mailgunDomain
  private val mailgunApiKey = AppConfig.mailgunApiKey
  private val fromName = AppConfig.adminName
  private val fromEmail = AppConfig.adminEmail

  val baseUrl = s"https://api.eu.mailgun.net/v3/$mailgunDomain"

  def sendMailFromAdmin(
    toName: Option[String],
    toEmail: String,
    subject: String,
    ccEmail: Option[String] = None,
    body: String,
    // textBody: Option[String],
    mailgunTag: Option[String] = None,
    Logger: SRLogger
  )(implicit wsClient: WSClient, ec: ExecutionContext): Future[Boolean] = {


    val toAddress = if (toName.isEmpty) Seq(toEmail) else Seq(s"${toName.get} <${toEmail}>")

    var postData: Map[String, Seq[String]] = Map(
      "from" -> Seq(s"""$fromName <$fromEmail>"""),
      "to" -> toAddress,
      "subject" -> Seq(subject),
      "html" -> Seq(body),
      // "text" -> Seq(textBody),

      //enabling mailgun open tracking
      "o:tracking-opens" -> Seq("yes")
    )


    //Passing tag to filter mails in mailgun dashboard
    if (mailgunTag.isDefined) {
      postData += ("o:tag" -> Seq(mailgunTag.get))
    }

    // add cc to emails
    if (ccEmail.isDefined && ccEmail.get.nonEmpty) {
      postData += ("cc" -> Seq(ccEmail.get))
    }

    Logger.info(s"[EmailHelper] sending: $toEmail :: $body")

    wsClient.url(s"$baseUrl/messages")
      .withAuth("api", mailgunApiKey, WSAuthScheme.BASIC)
      .post(postData)
      .map(response => {

        if (response.status != 200) {
          Logger.fatal(s"[EmailHelper] error:  $response ::: ${response.body}")

          throw new Exception(s"[EmailHelper] ${response.body}. Please check your API Key, Mailgun Email Domain and Mailgun Region")

        } else {

          Logger.info(s"[EmailHelper] success: $response ::: ${response.body}")

          true
        }
      })
  }


  def verifyEmail(
                   name: String,
                   code: String
                 ): String = {
    return """<!-- Email Body -->
    <span class="preheader"
          style="display: none !important; visibility: hidden; mso-hide: all; font-size: 1px; line-height: 1px; max-height: 0; max-width: 0; opacity: 0; overflow: hidden;">Thanks for trying out WarmupHero. To finish setting up your account, we just need to make sure this email address is yours.</span>
    <tr>
      <td class="email-body" width="670" cellpadding="0" cellspacing="0"
           style="word-break: break-word; margin: 0 auto; padding: 0; font-size: 16px; width: 100%; -premailer-width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;">
        <table class="email-body_inner" align="center" width="670" cellpadding="0" cellspacing="0"
               role="presentation"
               style="width: 670px; -premailer-width: 670px; -premailer-cellpadding: 0; -premailer-cellspacing: 0; background-color: #FFFFFF; margin: 0 auto; padding: 0;"
               bgcolor="#FFFFFF">
          <!-- Body content -->
          <tr>
            <td class="content-cell"
                style="word-break: break-word; font-family: 'Source Sans Pro'; font-size: 16px; padding: 10px;">
              <div class="f-fallback">
                <h1 style="margin-top: 0; color: #333333; font-size: 32px; font-weight: bold; text-align: left;font-weight: 700;"
                    align="left">Welcome, """+name+"""!</h1>
                <p style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                  Thanks for trying out WarmupHero. <br/>To finish setting up your account, we just need to make sure this email address is yours.</p>


                <table class="attributes" width="100%" cellpadding="0" cellspacing="0"
                       role="presentation" style="margin: 0 0 21px;">
                  <tr>
                    <td class="attributes_content"
                        style="word-break: break-word;font-size: 16px; background-color: #F4F4F7; padding: 16px;"
                        bgcolor="#F4F4F7">
                      <table width="100%" cellpadding="0" cellspacing="0"
                             role="presentation">

                        <tr>
                          <td class="attributes_item"
                              style="font-style: normal;font-weight: 700;font-size: 24px;line-height: 36px;text-align:center;padding: 8px;">
                                                                        <span class="f-fallback">
                                                                            <strong>Verification Code</strong>
                                                                        </span>
                          </td>
                        </tr>
                        <tr>
                          <td class="attributes_item"
                              style="color: #2C3644;letter-spacing: 0.16em;font-style: normal;font-weight: 400;font-size: 48px;line-height: 48px;text-align: center;padding: 8px;">
                                                                        <span class="f-fallback">
                                                                            <strong>"""+code+"""</strong>
                                                                        </span>
                          </td>
                        </tr>
                        <tr>
                          <td class="attributes_item"
                              style="color: #2C3644;;text-align: center;font-style: normal;font-weight: 400;font-size: 14px;line-height: 20px;padding: 8px;">
                                                                        <span class="f-fallback">
                                                                            Here is your OTP verification code.
                                                                        </span>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
                <p
                        style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                  If you have any questions, feel free to reply back to this email, or <a
                        href="mailto:<EMAIL>" style="color: #3869D4;">email
                  our
                  customer success team</a> (We're lightning quick at replying).
                <p
                        style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                  Thanks,
                  <br />WarmupHero Team</p>
                <p
                        style="font-size: 16px; line-height: 1.625; color: #51545E; margin: .4em 0 1.1875em;">
                  <strong>P.S.</strong> Check out our
                  <a href="https://warmuphero.com/resources/" target="_blank"
                     style="color: #3869D4;">help documentation</a> for queries related to onboarding or product features.</p>

              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    """
  }

  def resetPasswordEmail(
    name: String,
    code: String
  ): String = {

    s"Hi $name! <br/> " +
      s"You recently requested to reset your password for your ${CONSTANTS.APP_NAME} account. <br/> " +
      s"Use this One Time Password to reset the password: <b>$code</b> " +
      s"<br/><br/> "
  }


  def getMessageId(emailAccountId: Long, suffix: String): String = {

      val randomStr1 = StringUtils.genRandomAlphaNumericString30Chars
      val randomStr2 = StringUtils.genRandomAlphaNumericString30Chars
      s"<${new Date().getTime}.$emailAccountId.$randomStr1.$suffix>"

  }


  def genMessageIdSuffix() = {
    val msgIdSuffixId = EncryptionService.encrypt("emailliame", StringUtils.genRandomAlphaNumericString30Chars)
    s"mail@$msgIdSuffixId"
  }


  def getEmailThreadId(emailAccountId: Long): String = {

    val randomStr1 = StringUtils.genRandomAlphaNumericString30Chars
    val randomStr2 = StringUtils.genRandomAlphaNumericString30Chars
    s"WB.$emailAccountId.$randomStr1.$randomStr2"

  }

//  // https://stackoverflow.com/a/********
//  def getTextBodyFromHtmlBody(bodyHtml: String, Logger: SRLogger): String = Try {
//    val removedNbspHTML = bodyHtml.replace('\u00A0', ' ').replaceAll("<br>", "\n")
//
//    // get pretty printed html with preserved br and p and div tags
//    val prettyPrintedBodyFragment = Jsoup.clean(removedNbspHTML, "", Safelist.none.addTags("br", "p", "div"), new OutputSettings().prettyPrint(false))
//
//    // get plain text with preserved line breaks by disabled prettyPrint
//    val out = Jsoup.clean(prettyPrintedBodyFragment, "", Safelist.none, new OutputSettings().prettyPrint(false)).trim
//
//    // NOTE: https://stackoverflow.com/a/********
//    // should sort gabor's "&nbsp;" issue
//    val out2 = Parser.unescapeEntities(out, false)
//
//    out2
//    // replace &nbsp; with a space " "
//    // gabor issue
//    // this was added on 6th June 2018 by Prateek
//    // because sometimes the "&nbsp;" appears as it is in the textPart making it look ugly
//    // out.replaceAll("&nbsp;", " ")
//  } match {
//
//    case Failure(e) =>
//      Logger.error(s"[EmailService] [getBaseBodyAndTextBody.textBody] FATAL ERROR returing empty text:: html: $bodyHtml ::error: ${Helpers.getStackTraceAsString(e)}")
//
//      ""
//
//    case Success(txt) => txt
//
//  }

  def cleanAIGeneratedEmailSubject(subject: String): String = {

    val subjectTrimmed = subject.trim

    if (
      subjectTrimmed.headOption.contains('"') &&
        subjectTrimmed.lastOption.contains('"')
    ) {

      subjectTrimmed
        .drop(1)
        .dropRight(1)

    } else {

      subjectTrimmed

    }

  }

  def makeHTMLBody(
                        baseBody: String,
                        salutation: String,
                        signature: String
                      ): String = {

    /**
      * 13 Sep 2024
      *
      * When converting plain text to HTML,
      * Jsoup does not preserve the newline ("\n").
      * So replacing all the "\n" in the body with HTML `<br />` tags,
      * before parsing with Jsoup.
      */
    val doc = Jsoup.parse(baseBody.replaceAll("\n", "<br />"))

    if (salutation.trim.nonEmpty) {
      doc.body().prepend(salutation)
    }

    if (signature.trim.nonEmpty) {
      doc.body().append(signature)
    }

    val htmlBody = doc.html()

    htmlBody

  }



  def getEmailBody() = {

    val emailBody = EmailTemplate.findEmailBodyForEmailSchedule()

    if(emailBody.isDefined) emailBody.get else ""

  }

  def getSalutationHtml(firstName: String) = {
    val salutation = EmailTemplate.findSalutationForEmailSchedule()

    val salutationHtml = if(salutation.isDefined && salutation.get.trim.nonEmpty) s"""${salutation.get.trim} ${firstName},<br/><br/>""" else ""

    salutationHtml
  }

  def getSignatureHtml(signature: Option[String], firstName: String) = {

    val endingRemark = EmailTemplate.findEndingRemarkForEmailSchedule()

    if (signature.isEmpty || signature.get.trim.isEmpty) {

      if (endingRemark.isDefined && endingRemark.get.trim.nonEmpty)
        s"""<br/><br/>${endingRemark.get.trim}<br/><br/>${firstName}"""
      else ""

    } else {

      s"""<br/>${endingRemark.get.trim}<br/><br/>${signature.get.trim}"""

    }
  }

  def getSubjectToReplay(sub: String): String = {
    s"${SubjectTags.RE} $sub"
  }

  def getSubjectToSearch(sub: String): String = {
    val removedRESub = sub.replace(SubjectTags.RE, "").trim
        removedRESub.replaceAll("\u0000", "")
  }

  def isInbox(folder: Folder) = {

    folder.getFullName == IMAPStanderdFolderTypes.INBOX.toString

  }
}