package utils.email

import api.AppConfig

import java.time.LocalDateTime
import api.CONSTANTS.EMAIL_API_ERROR_KEYS
import api.models.{EmailAccount, EmailScheduled, EmailSettings}
import io.smartreach.esp.utils.email.{EmailReplyBounceType, EmailReplyStatusCommon}
import io.smartreach.esp.utils.email.models.EmailReply
import io.smartreach.esp.utils.email.services.GmailServiceCommon
import org.joda.time.{DateTime, LocalDateTime => JodaLocalDateTime}
import play.api.libs.json.{JsValue, Json, OFormat}

import scala.util.{Failure, Success}
import play.api.libs.ws.WSClient
import utils.{Helpers, SRLogger}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._

import scala.concurrent.{ExecutionContext, Future}

case class BounceData(
                       bounced_at: DateTime,
                       bounce_type: EmailReplyBounceType,
                       bounce_reason: String,
                       emailBaseBody: String,
                       subject: String,
                       fullHeaders: JsValue,
                       is_soft_bounced: Boolean
                     )

object BounceData {
  implicit val formats: OFormat[BounceData] = Json.format[BounceData]
}

case class BounceCheckDetails(
                               subject: String,
                               emailHtmlBody: String,
                               fromEmail: String,
                               fromName: String,
                               received_at: DateTime,
                               references_header: Array[String],
                               fullHeaders: JsValue,
                             )

object BounceCheckDetails {
  implicit val formats: OFormat[BounceCheckDetails] = Json.format[BounceCheckDetails]
}

object EmailLandingService {

  private def checkEmailBounce(
                                emailBaseBody: String,
                                subject: String,
                                fromEmail: String,
                                fromName: String,
                                fullHeaders: JsValue,
                                received_at: DateTime
                              )(implicit Logger: SRLogger): Option[BounceData] = {

    // admin emails are not considered as bounced
    // gmail sending limit reached emails are also not considered as bounced
    val isGmailSendingLimitReachedMessage: Boolean = GmailServiceCommon
      .isSendingLimitReachedMessage(
        fromEmail = fromEmail,
        emailBody = emailBaseBody
      )

    val lowerCasedBody = emailBaseBody.toLowerCase

    val bounceType = EmailReplyStatusCommon.checkMessageBounceType(
      emailBody = lowerCasedBody,
      subject = subject,
      fromEmail = fromEmail,
      fromName = fromName
    )

    val isSoftBounced: Boolean =
      isGmailSendingLimitReachedMessage ||
        (bounceType.isDefined && bounceType.get._1 != EmailReplyBounceType.EmailAddressNotFound)

    bounceType.map { case (bounce_type: EmailReplyBounceType, bounce_reason: String) =>
      BounceData(
        bounced_at = received_at,
        bounce_type = bounce_type,
        bounce_reason = bounce_reason,
        emailBaseBody = emailBaseBody,
        subject = subject,
        fullHeaders = fullHeaders,
        is_soft_bounced = isSoftBounced
      )
    }
  }

  private def handleBounceMessageNotFound(
                                           emailScheduled: EmailScheduled,
                                           receiverEmailAccount: EmailAccount,
                                           Logger: SRLogger
                                         )(
                                           implicit ec: ExecutionContext,
                                           wSClient: WSClient
                                         ): (EmailScheduled, Boolean) = {

    // Message not found in Landing Check

    Logger.info(s"Start handleBounceMessageNotFound. emailScheduledId: ${emailScheduled.id}:: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email}")

    EmailScheduled.incrementLandingCheckTriedCount(
      emailScheduledId = emailScheduled.id
    ) match {
      case Failure(exception) =>

        Logger.error(
          s"incrementLandingCheckTriedCount [Limit Unreached] - Failed. emailScheduledId: ${emailScheduled.id}:: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email}",
          err = exception
        )

        (emailScheduled, false)

      case Success(0) =>

        Logger.error(
          s"incrementLandingCheckTriedCount [Limit Unreached] - Update: 0. emailScheduledId: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email}"
        )

        (emailScheduled, false)

      case Success(_) =>

        EmailScheduled.getLandingCheckTriedCount(emailScheduledId = emailScheduled.id) match {

          case Failure(exception) =>

            Logger.error(
              s"getLandingCheckTriedCount - Failed. emailScheduledId: ${emailScheduled.id}",
              err = exception
            )

            (emailScheduled, false)

          case Success(None) =>

            Logger.error(
              s"getLandingCheckTriedCount - Update: 0. emailScheduledId: ${emailScheduled.id}"
            )

            (emailScheduled, false)

          case Success(Some(landing_check_tried_count)) =>

            if (landing_check_tried_count < AppConfig.stopLandingCheckAfterTries) {

              // This is the case where we have not yet hit the limit of `stopLandingCheckAfterTries`.

              // So, we just increment the count.

              // Update `landing_check_tried_count` and `landing_check_performed_at`, so that the
              // scheduler will only pick it up after 24 hrs

              Logger.info(
                s"incrementLandingCheckTriedCount [Limit Unreached] Success. emailScheduledId: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email}"
              )

              (emailScheduled, false)

            } else {

              // Here we have hit the limit of `stopLandingCheckAfterTries`.

              // Handle landing check error

              // And update `landing_check_tried_count` and `landing_check_performed_at`, so that the
              // scheduler will never pick it up again

              EmailAccountErrorHandlingService.handleEmailAccountError(
                emailAccountId = receiverEmailAccount.id,
                accountId = receiverEmailAccount.account_id,
                emailAddress = receiverEmailAccount.email,
                emailException = new Exception("Email Landing Check Failed."),
                Logger = Logger
              )(emailAccountError = EmailErrors.LandingCheckFailedExceededLimitError) match {
                case Failure(exception) =>

                  Logger.error(
                    s"handleBounceMessageNotFound - handleEmailNotFoundForLandingCheckError Failed :: emailAccountId: ${receiverEmailAccount.id} :: accountId: ${receiverEmailAccount.account_id} :: emailAddress: ${receiverEmailAccount.email}",
                    err = exception
                  )

                  (emailScheduled, false)

                case Success(None) =>

                  Logger.error(
                    s"handleBounceMessageNotFound - handleEmailNotFoundForLandingCheckError NONE :: emailAccountId: ${receiverEmailAccount.id} :: accountId: ${receiverEmailAccount.account_id} :: emailAddress: ${receiverEmailAccount.email}"
                  )

                  (emailScheduled, false)

                case Success(Some(_)) =>

                  Logger.info(
                    s"handleBounceMessageNotFound - handleEmailNotFoundForLandingCheckError Success :: emailAccountId: ${receiverEmailAccount.id} :: accountId: ${receiverEmailAccount.account_id} :: emailAddress: ${receiverEmailAccount.email}"
                  )

                  (emailScheduled, false)
              }
            }
        }
    }
  }

  private def handleEmailBounced(
                                  emailScheduled: EmailScheduled,
                                  receiverEmailAccount: EmailAccount,
                                  bouncedData: BounceData,
                                  Logger: SRLogger
                                )(
                                  implicit ec: ExecutionContext,
                                  wSClient: WSClient
                                ): (EmailScheduled, Boolean) = {

    // Email has bounced

    Logger.info(
      s"Start handleEmailBounced. emailScheduledId: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email}"
    )

    bouncedData.bounce_type match {
      case EmailReplyBounceType.None |
           EmailReplyBounceType.SenderRejected |
           EmailReplyBounceType.SpamComplaint |
           EmailReplyBounceType.SendingLimitReached |
           EmailReplyBounceType.MsgTooLarge =>

        EmailScheduled.updateBounceStatus(
          emailScheduledId = emailScheduled.id,
          bounceType = bouncedData.bounce_type,
          bouncedReplyBody = bouncedData.emailBaseBody,
          bouncedReplySubject = bouncedData.subject,
          bouncedReplyHeaders = bouncedData.fullHeaders,
        ) match {
          case Failure(exception) =>

            Logger.error(
              s"updateBounceStatus - Failed :: emailAccountId: ${receiverEmailAccount.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email} :: accountId: ${receiverEmailAccount.account_id} :: bounceType: ${bouncedData.bounce_type}",
              err = exception
            )

            (emailScheduled, false)

          case Success(0) =>

            Logger.error(
              s"updateBounceStatus - Update: 0 :: emailAccountId: ${receiverEmailAccount.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email} :: accountId: ${receiverEmailAccount.account_id} :: bounceType: ${bouncedData.bounce_type}"
            )

            (emailScheduled, false)

          case Success(_) =>

            Logger.info(
              s"updateBounceStatus Success [SoftBounce]. emailScheduledId: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email} :: bounce_type: ${bouncedData.bounce_type}"
            )

            (emailScheduled, false)

        }

      case EmailReplyBounceType.EmailAddressNotFound |
           EmailReplyBounceType.RecipientConnectionIssue =>

        EmailScheduled.updateBounceStatus(
          emailScheduledId = emailScheduled.id,
          bounceType = bouncedData.bounce_type,
          bouncedReplyBody = bouncedData.emailBaseBody,
          bouncedReplySubject = bouncedData.subject,
          bouncedReplyHeaders = bouncedData.fullHeaders,
        ) match {
          case Failure(exception) =>

            Logger.error(
              s"updateBounceStatus [hard_bounce] - Failed :: emailAccountId: ${receiverEmailAccount.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email} :: accountId: ${receiverEmailAccount.account_id} :: bounceType: ${bouncedData.bounce_type}",
              err = exception
            )

            (emailScheduled, false)

          case Success(0) =>

            Logger.error(
              s"updateBounceStatus [hard_bounce] - Update: 0 :: emailAccountId: ${receiverEmailAccount.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email} :: accountId: ${receiverEmailAccount.account_id} :: bounceType: ${bouncedData.bounce_type}"
            )

            (emailScheduled, false)

          case Success(_) =>

            // ALso send email notification

            EmailAccountErrorHandlingService.handleEmailAccountError(
              emailAccountId = receiverEmailAccount.id,
              accountId = receiverEmailAccount.account_id,
              emailAddress = receiverEmailAccount.email,
              emailException = new Exception("Failed Email Hard Bounced."),
              Logger = Logger
            )(emailAccountError = EmailErrors.EmailHardBouncedError) match {
              case Failure(exception) =>

                Logger.error(
                  s"handleEmailBounced - EmailHardBouncedError Failed :: emailAccountId: ${receiverEmailAccount.id} :: accountId: ${receiverEmailAccount.account_id} :: emailAddress: ${receiverEmailAccount.email}",
                  err = exception
                )

                (emailScheduled, false)

              case Success(None) =>

                Logger.error(
                  s"handleEmailBounced - EmailHardBouncedError NONE :: emailAccountId: ${receiverEmailAccount.id} :: accountId: ${receiverEmailAccount.account_id} :: emailAddress: ${receiverEmailAccount.email}"
                )

                (emailScheduled, false)

              case Success(Some(_)) =>

                Logger.info(
                  s"handleEmailBounced - EmailHardBouncedError Success. emailScheduledId: ${emailScheduled.id}:: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email} :: bounce_type: ${bouncedData.bounce_type}"
                )

                (emailScheduled, false)
            }
        }
    }
  }

  private def filterUnrelatedBounceCheckDetails(
    emailScheduled: EmailScheduled,
    bounceCheckDetailsList: Vector[BounceCheckDetails]
  )(
    implicit logger: SRLogger
  ): Vector[BounceCheckDetails] = {

    logger.info(
      msg = s"filterUnrelatedBounceCheckDetails - fromEmail: ${emailScheduled.from_email} :: toEmail: ${emailScheduled.to_email} :: subject: ${emailScheduled.subject} isReply: ${emailScheduled.is_reply} :: messageId: ${emailScheduled.message_id} :: scheduledAt: ${emailScheduled.scheduled_at} :: referencesHeader: ${emailScheduled.references_header} :: bounceMailSubject: ${bounceCheckDetailsList.map(_.subject)} :: bounceMailRefs: ${bounceCheckDetailsList.map(_.references_header)}"
    )

    /**
      * 04-Apr-2024
      *
      * For Outlook API in parseOutlookMessage,
      * we are hardcoding the values of the following fields:
      *
      * - full_headers = Json.obj(),
      * - in_reply_to_header = None,
      * - references_header = None,
      *
      * So, for Outlook references_header is always None,
      * so we also need the subject check.
      *
      * Outlook bounced emails have a similar subject to that of the original email,
      * so we are leveraging that to check bounce emails along with other checks.
      *
      * Example subject:
      *
      * Subject: Undeliverable: RE: What we don't know for sure is whether or not an
      * owl is a punctual kumquat?  | wrmpbx
      *
      * If Outlook breaks this, then we will need to opt for some other check.
      */

    val filteredDetails: Vector[BounceCheckDetails] = bounceCheckDetailsList
      .filter { bounceDetails =>

        val isMessageIdInReferences = emailScheduled.message_id.exists { msgId =>
          bounceDetails.references_header.contains(msgId)
        }

        val isSubjectMatching = bounceDetails.subject.contains(emailScheduled.subject)

        isMessageIdInReferences || isSubjectMatching

      }

    logger.info(msg = s"filterUnrelatedBounceCheckDetails - filteredDetails: $filteredDetails")

    filteredDetails

  }

  private def handleLandingCheckFailed(
                                        emailScheduled: EmailScheduled,
                                        receiverEmailAccount: EmailAccount,
                                        Logger: SRLogger
                                      )(
                                        implicit ec: ExecutionContext,
                                        wSClient: WSClient
                                      ): Future[(EmailScheduled, Boolean)] = {

    // check is same message in senders inbox, if it has bounced

    EmailAccount.find(id = emailScheduled.sender_email_account_id) match {
      case Failure(exception) =>

        Logger.error(
          s"handleLandingCheckFailed - find senderEmailAccount FAILED. sender_email_account_id: ${emailScheduled.sender_email_account_id}",
          err = exception
        )

        Future.failed(exception)

      case Success(senderEmailAccount: EmailAccount) =>

        getBouncedMessages(
          receiverEmailAccount = senderEmailAccount,
          emailScheduled = emailScheduled,
          Logger = Logger
        )
          .map { bounceCheckDetailsList: Vector[BounceCheckDetails] =>

            val bounceCheckDetailsListFiltered = filterUnrelatedBounceCheckDetails(
              emailScheduled = emailScheduled,
              bounceCheckDetailsList = bounceCheckDetailsList
            )(logger = Logger)

            val bouncedDataList = bounceCheckDetailsListFiltered
              .map { bounceCheckDetails: BounceCheckDetails =>
                checkEmailBounce(
                  emailBaseBody = bounceCheckDetails.emailHtmlBody,
                  subject = bounceCheckDetails.subject,
                  fromEmail = bounceCheckDetails.fromEmail,
                  fromName = bounceCheckDetails.fromName,
                  received_at = bounceCheckDetails.received_at,
                  fullHeaders = bounceCheckDetails.fullHeaders
                )(Logger = Logger)
              }
              .filter(bouncedDataOpt => bouncedDataOpt.nonEmpty)
              .map(bouncedDataOpt => bouncedDataOpt.get) // Note: This .get is only safe because of the .nonEmpty check in the above filter

            if (bouncedDataList.nonEmpty) {

              // Found the bounced email in sender email-inbox

              val bouncedData: BounceData = bouncedDataList.head

              handleEmailBounced(
                emailScheduled = emailScheduled,
                receiverEmailAccount = receiverEmailAccount,
                bouncedData = bouncedData,
                Logger = Logger
              )

            } else {

              // Not found the bounced email in sender email-inbox

              handleBounceMessageNotFound(
                emailScheduled = emailScheduled,
                receiverEmailAccount = receiverEmailAccount,
                Logger = Logger
              )

            }
          }
          .recoverWith {
            case e =>

              Logger.error(
                s"getBouncedMessages - FAILED. receiverEmailAccount: $receiverEmailAccount :: emailScheduled: $emailScheduled",
                err = e
              )

              Future.failed(e)
          }
    }
  }

  def checkLanding(
                    emailScheduledId: Long,
                    Logger: SRLogger
                  )(implicit ec: ExecutionContext, wSClient: WSClient): Future[(EmailScheduled, Boolean)] = {

    EmailScheduled.find(id = emailScheduledId, Logger = Logger) match {

      case Failure(e) =>
        Logger.error(s"[EmailLadingService] checkLanding EmailScheduled.find error emailScheduledId: $emailScheduledId :: ${Helpers.getStackTraceAsString(e)}")

        Future.failed(MQDoNotNackException(s"${Logger.logRequestId} email scheduled error"))

      case Success(None) =>
        Logger.fatal(s"[EmailLadingService] checkLanding EmailScheduled.find NONE found emailScheduledId: $emailScheduledId")

        Future.failed(MQDoNotNackException(s"${Logger.logRequestId} email scheduled not found"))

      case Success(Some(emailScheduled)) =>

        Logger.info(s"Found EmailScheduled:: $emailScheduled")

        val emailAccountId = emailScheduled.receiver_email_account_id

        EmailAccount.find(id = emailAccountId) match {

          case Failure(exception) =>

            Logger.error(s"[EmailLadingService] checkLanding EmailAccount not found emailAccountId: $emailAccountId", err = exception)
            Future.failed(MQDoNotNackException(s"${Logger.logRequestId} email account not found"))

          case Success(receiverEmailAccount) =>
            
            Logger.info(s"[EmailLadingService] checkLanding EmailAccount found: ${receiverEmailAccount.email}")

            if (receiverEmailAccount.paused_till.isDefined && receiverEmailAccount.paused_till.get.isAfter(LocalDateTime.now())) {

              Logger.error(s"[EmailLadingService] checkLanding email account is paused:: ${receiverEmailAccount.error}")

              Future.failed(MQDoNotNackException(s"${Logger.logRequestId} email account is paused:: ${receiverEmailAccount.paused_till.get}"))

            } else if (receiverEmailAccount.error.isDefined && receiverEmailAccount.paused_till.isEmpty) {

              Future.failed(MQDoNotNackException(s"${Logger.logRequestId} email account has error only happen when email account under manual review :: receiverEmailAccount: $receiverEmailAccount"))

            } else {

              receiveEmail(receiverEmailAccount = receiverEmailAccount, emailScheduled = emailScheduled, Logger = Logger)
                .flatMap { emailTracked =>

                  if (emailTracked.isEmpty) {

                    handleLandingCheckFailed(
                      emailScheduled = emailScheduled,
                      receiverEmailAccount = receiverEmailAccount,
                      Logger = Logger
                    )

                  } else {

                    val originalLandedFolder = emailTracked.get.originalLandedFolder
                    val folder = emailTracked.get.folder
                    val landedFolderType = emailTracked.get.landedFolderType

                    val messageIdOpt = emailTracked.get.messageIdOpt

                    EmailScheduled.updateLandingFolderAndMessageIds(
                      emailScheduledId = emailScheduledId,
                      originalLandedFolder = originalLandedFolder,
                      landedFolder = folder,
                      landedFolderType = landedFolderType,
                      messageIdOpt = messageIdOpt,
                      Logger = Logger
                    ) match {

                      case Failure(e) =>

                        Logger.error(s"[EmailLadingService] checkLanding EmailScheduled.updateLandingFolder FATAL error: ${Helpers.getStackTraceAsString(e)}")

                        Future.successful((emailScheduled, false))

                      case Success(_) =>

                        /**
                          * only the place other than update and activate email account where clearError is happening*/
                        EmailAccount.clearError(
                          emailAccountIds = List(receiverEmailAccount.id),
                          accountId = receiverEmailAccount.account_id
                        ).map(_.find(eaId => eaId == receiverEmailAccount.id)) match {

                          case Failure(e) =>
                            Logger.error(s"[EmailLadingService] checkLanding EmailScheduled.clearError receiverEmailAccount: ${receiverEmailAccount.id} :: FATAL error: ${Helpers.getStackTraceAsString(e)}")

                            Future.successful((emailScheduled, false))

                          case Success(None) =>

                            Logger.error(s"[EmailLadingService] checkLanding EmailScheduled.clearError NONE receiverEmailAccount: ${receiverEmailAccount.id} :: FATAL error}")

                            Future.successful((emailScheduled, false))

                          case Success(Some(_)) =>

                            Logger.info(s"[EmailLadingService] checkLanding EmailScheduled.clearError success receiverEmailAccountId: ${receiverEmailAccount.id}")

                            Future.successful((emailScheduled, true))
                        }

                    }


                  }

                }
                .recoverWith { case exception => {

                  /*partial function ends with _*/
                  val handleError = EmailAccountErrorHandlingService.handleEmailAccountError(
                    accountId = receiverEmailAccount.account_id,
                    emailAccountId = receiverEmailAccount.id,
                    emailAddress = receiverEmailAccount.email,
                    emailException = exception,
                    Logger = Logger
                  ) _

                  Logger.info(s"\n\n\n\n\n Lading check exception ${exception.getMessage} :: emailScheduledId: $emailScheduledId \n\n\n\n")

                  exception match {

                    case e: javax.mail.AuthenticationFailedException =>

                      handleError(EmailErrors.AuthenticationFailedError) match {

                        case Failure(err) =>
                          Logger.fatal("FATAL AuthenticationFailedException handleError_Result", err = err)

                          Future.failed(err)

                        case Success(_) =>

                          Logger.fatal(
                            msg = s"Authentication failed. accountId: ${receiverEmailAccount.account_id} :: emailAccountId: ${receiverEmailAccount.account_id} :: email: ${receiverEmailAccount.email}",
                            err = e
                          )

                          // do not nack
                          Future.failed(MQDoNotNackException(s"${Logger.logRequestId} [EmailService] error (emailAccountId: $emailAccountId) email is now paused so deleted from queue"))
                      }

                    case e: javax.mail.MessagingException =>

                      handleError(EmailErrors.ReplyTrackingMessagingExceptionError) match {
                        case Failure(err) =>
                          Logger.fatal("FATAL MessagingException handleError_Result", err = err)
                          Future.failed(err)

                        case Success(_) =>
                          Logger.error("MessagingException", err = e)
                          // do not nack
                          Future.failed(MQDoNotNackException(s"${Logger.logRequestId} [EmailService] error (emailAccountId: $emailAccountId) email is now paused so deleted from queue"))
                      }

                    case e =>
                      Logger.error(s"error other message ${receiverEmailAccount.id}", err = e)

                      if (e.getMessage != null) {

                        if (
                          e.getMessage.toLowerCase.contains("timed out") ||
                            e.getMessage.toLowerCase.contains("timeout") ||
                            e.getMessage.contains(EMAIL_API_ERROR_KEYS.GATEWAY_TIMEOUT)

                        ) {

                          handleError(EmailErrors.ConnectionTimeoutError) match {
                            case Failure(err) =>
                              Logger.fatal("FATAL ConnectionTimeoutError handleError_Result", err = err)
                              Future.failed(err)

                            case Success(_) =>
                              Logger.error("ConnectionTimeoutError", err = e)
                              // do not nack
                              Future.failed(MQDoNotNackException(s"${Logger.logRequestId} [EmailService] error (emailAccountId: $emailAccountId) email is now paused so deleted from queue"))
                          }

                        } else {

                          Logger.info(s"recover landing check landing - handleLandingCheckFailed. emailScheduledId: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email}")

                          handleLandingCheckFailed(
                            emailScheduled = emailScheduled,
                            receiverEmailAccount = receiverEmailAccount,
                            Logger = Logger
                          )
                        }

                      } else {

                        Logger.info(s"recover landing check landing - handleLandingCheckFailed e.getMessage == null. emailScheduledId: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: to_email: ${emailScheduled.to_email}")

                        handleLandingCheckFailed(
                          emailScheduled = emailScheduled,
                          receiverEmailAccount = receiverEmailAccount,
                          Logger = Logger)

                      }
                  }
                }
                }

            }


        }
    }
  }

  private def getBouncedMessages(receiverEmailAccount: EmailAccount, emailScheduled: EmailScheduled, Logger: SRLogger)
                                (implicit wsClient: WSClient, ec: ExecutionContext): Future[Vector[BounceCheckDetails]] = {

    Logger.info(s"[EmailLadingService] receiveEmail: Finding email")

    receiverEmailAccount.settings match {

      case s: EmailSettings.SmtpImapSettings =>

        val emailServiceOpt: Option[TEmailService] = receiverEmailAccount.service_provider match {

          case api.models.EmailServiceProvider.GOOGLE_WORKSPACE =>
            Some(GmailService)

          case api.models.EmailServiceProvider.MICROSOFT_365 =>
            Some(OutlookService)

          case api.models.EmailServiceProvider.OTHER =>
            Some(GenericEmailService)

          case api.models.EmailServiceProvider.MICROSOFT_365_API =>
            None
        }

        emailServiceOpt match {
          case Some(emailService) =>
            for {

              filteredMessages: Vector[BounceCheckDetails] <- emailService.fetchMessagesWithBodySearchTerm(
                receiveCredentials = s.imap_settings,
                emailScheduled = emailScheduled,
                bodySearchTerm = emailScheduled.to_email,
                Logger = Logger
              )

            } yield {

              Logger.info(s"[EmailLadingService] receiveEmail: Finding email: filteredMessages: $filteredMessages")

              filteredMessages

            }

          case None =>
            Future.failed(new Exception("Invalid EmailServiceProvider for SMTP/IMAP."))
        }

      case oauthTokens: EmailSettings.OAuthTokens =>

        val emailServiceOpt: Option[EmailReceiveService[EmailSettings.OAuthTokens, EmailReply.OutlookReplyTrackedViaAPI]] = receiverEmailAccount.service_provider match {
          case api.models.EmailServiceProvider.MICROSOFT_365_API =>
            Some(OutlookApi)

          case api.models.EmailServiceProvider.GOOGLE_WORKSPACE |
               api.models.EmailServiceProvider.MICROSOFT_365 |
               api.models.EmailServiceProvider.OTHER =>
            None
        }

        emailServiceOpt match {

          case Some(emailService) =>

            emailService.fetchMessagesWithBodySearchTerm(
              bodySearchTerm = emailScheduled.to_email,
              receiveCredentials = oauthTokens,
              emailScheduled = emailScheduled,
              Logger = Logger
            ).map { messages: Vector[BounceCheckDetails] =>

              messages
                .filter { message =>
                  message.received_at
                    .isAfter(
                      new JodaLocalDateTime(
                        emailScheduled.scheduled_at.get.getYear,
                        emailScheduled.scheduled_at.get.getMonthValue,
                        emailScheduled.scheduled_at.get.getDayOfMonth,
                        emailScheduled.scheduled_at.get.getHour,
                        emailScheduled.scheduled_at.get.getMinute,
                        emailScheduled.scheduled_at.get.getSecond,
                      ).toDateTime()
                    )
                }
            }

          case None =>

            Future.failed(new Exception("Invalid EmailServiceProvider for OAuth."))
        }
    }
  }

  def receiveEmail(receiverEmailAccount: EmailAccount, emailScheduled: EmailScheduled, Logger: SRLogger)
                  (implicit wsClient: WSClient, ec: ExecutionContext): Future[Option[EmailMessageTrackedFullOjb]] = {

    Logger.info(s"[EmailLadingService] receiveEmail: Finding email")

    val filteredMessagesFut: Future[Option[EmailMessageTrackedFullOjb]] = receiverEmailAccount.settings match {

      case s: EmailSettings.SmtpImapSettings =>

        val emailServiceOpt: Option[TEmailService] = receiverEmailAccount.service_provider match {

          case api.models.EmailServiceProvider.GOOGLE_WORKSPACE =>
            Some(GmailService)

          case api.models.EmailServiceProvider.MICROSOFT_365 =>
            Some(OutlookService)

          case api.models.EmailServiceProvider.OTHER =>
            Some(GenericEmailService)

          case api.models.EmailServiceProvider.MICROSOFT_365_API =>
            None
        }

        emailServiceOpt match {
          case Some(emailService) =>
            for {

              filteredMessages: Option[EmailMessageTrackedFullOjb] <- emailService.getFilteredMessagesFromAllFolders(
                receiveCredentials = s.imap_settings,
                emailScheduled = emailScheduled,
                Logger = Logger
              )

            } yield {

              Logger.info(s"[EmailLadingService] receiveEmail: Finding email: filteredMessages: $filteredMessages")

              filteredMessages
            }

          case None =>
            Future.failed(new Exception("Invalid EmailServiceProvider for SMTP/IMAP."))
        }

      case oauthTokens: EmailSettings.OAuthTokens =>

        val emailServiceOpt: Option[EmailReceiveService[EmailSettings.OAuthTokens, EmailReply.OutlookReplyTrackedViaAPI]] = receiverEmailAccount.service_provider match {
          case api.models.EmailServiceProvider.MICROSOFT_365_API =>
            Some(OutlookApi)

          case api.models.EmailServiceProvider.GOOGLE_WORKSPACE |
               api.models.EmailServiceProvider.MICROSOFT_365 |
               api.models.EmailServiceProvider.OTHER =>
            None
        }

        emailServiceOpt match {

          case Some(emailService) =>

            emailService.getFilteredMessagesFromAllFolders(
              receiveCredentials = oauthTokens,
              emailScheduled = emailScheduled,
              Logger = Logger
            )

          case None =>

            Future.failed(new Exception("Invalid EmailServiceProvider for OAuth."))
        }
    }

    filteredMessagesFut
      .map { message =>

        Logger.info(s"[EmailLadingService] receiveEmail: DONE")

        message
      }
      .recover {
        case e =>
          Logger.error(
            s"[EmailLandingService] receiveEmail failed. email_scheduled_id: ${emailScheduled.id} :: from_email: ${emailScheduled.from_email} :: sender_email_account_id: ${emailScheduled.sender_email_account_id} :: to_email: ${emailScheduled.to_email} :: receiver_email_account_id: ${emailScheduled.receiver_email_account_id} :: subject: ${emailScheduled.subject}",
            err = e
          )
          throw e
      }

  }

}
