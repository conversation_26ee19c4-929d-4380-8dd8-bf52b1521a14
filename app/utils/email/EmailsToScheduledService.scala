package utils.email

import api.emails._
import play.api.libs.ws.WSClient
import utils.{<PERSON><PERSON>, SRLogger}
import utils.mq.email.MQEmailMessage

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import api.models._

//case class MQDoNotNackException(message: String = "", cause: Throwable = None.orNull)
//  extends Exception(message, cause)

object EmailsToScheduledService {


  def savedEmailScheduled(
                           senderEmailAccountId: Long,
                           from_email: String,
                           from_name: String,
                           receiverEmailAccountId: Long,
                           to_email: String,
                           to_name: String,
                           account_id: Long,
                           isReply: Boolean,
                           warmup_email_account_id: Long,
                           in_reply_to_header: Option[String],
                           references_header: Option[String],
                           template_id: Long,
                           subject: Option[String]

                         ) = {


//    val email_body = EmailHelper.getEmailBody()
//
//    val salutationHtml = EmailHelper.getSalutationHtml(firstName = receiverEmailAccount.first_name)
//
//    val signatureHtml = EmailHelper.getSignatureHtml(signature = senderEmailAccount.signature, firstName = senderEmailAccount.first_name)
//
//    val htmlBody = EmailHelper.makeHTMLBody(
//      baseBody = email_body,
//      salutation = salutationHtml,
//      signature = signatureHtml
//    )
//
//    val textBody = EmailHelper.getTextBodyFromHtmlBody(htmlBody)
//
//    val emailsToBeScheduled = EmailToBeScheduled(
//      sender_email_account_id = senderEmailAccountId,
//      from_email = from_email,
//      from_name = from_name,
//      receiver_email_account_id = receiverEmailAccountId,
//      to_email = to_email,
//      to_name = to_name,
//      account_id = account_id,
//      htmlBody = htmlBody,
//      textBody = textBody,
//      subject = subject, //s"${template.subject} | ${SubjectTags.SUFFIX}",
//      template_id = template_id,
//      is_reply = isReply,
//      warmup_email_account_id = warmup_email_account_id,
//      email_thread_id = EmailHelper.getEmailThreadId(emailAccountId = senderEmailAccountId),
//      in_reply_to_header = in_reply_to_header,
//      references_header = references_header
//    )
//
//    val savedEmailScheduled = EmailScheduled.saveEmailsToBeScheduled(
//      emailsToBeScheduled = emailsToBeScheduled,
//      Logger = Logger
//    ).get

  }

}
