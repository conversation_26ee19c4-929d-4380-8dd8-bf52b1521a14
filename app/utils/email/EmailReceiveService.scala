package utils.email

import api.models.EmailScheduled
import io.smartreach.esp.api.emails.{InternetMessageId, OutlookMessageId}
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}


case class EmailMessageTrackedFullOjb(
                                       // message: Message,
                                       folder: String,
                                       originalLandedFolder: String,
                                       landedFolderType: FolderType.Value,
                                       messageIdOpt: Option[InternetMessageId]
                                     )


trait EmailReceiveService[ReceiveCredentials, EmailMessage] {


  val INBOX_FOLDER_NAME: String
  val SPAM_FOLDER_NAME: String
  val IMPORTANT_FOLDER_NAME: String
  val CATEGORIES: Seq[String]


  def fetchRecentMessagesForTestingImap(
                                         emailAfterDate: DateTime,
                                         receiveCredentials: ReceiveCredentials,
                                         Logger: SRLogger
                                       )(
                                         implicit wsClient: WSClient,
                                         ec: ExecutionContext,
                                       ): Future[Vector[EmailMessage]]


  def getFilteredMessagesFromAllFolders(
                                         receiveCredentials: ReceiveCredentials,
                                         emailScheduled: EmailScheduled,
                                         Logger: SRLogger
                                       )(
                                         implicit wsClient: WSClient,
                                         ec: ExecutionContext,
                                       ): Future[Option[EmailMessageTrackedFullOjb]]

  def fetchMessagesWithBodySearchTerm(
                                       bodySearchTerm: String,
                                       emailScheduled: EmailScheduled,
                                       receiveCredentials: ReceiveCredentials,
                                       Logger: SRLogger
                                     )(
                                       implicit wsClient: WSClient,
                                       ec: ExecutionContext,
                                     ): Future[Vector[BounceCheckDetails]]

}
