package utils.email

import api.AppConfig

import java.time.LocalDateTime
import api.models.{EmailAccount, EmailAccountId, EmailScheduled}
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

object EmailAccountErrorHandlingService {

  def handleEmailNotFoundForLandingCheckError(
                                               emailAccountId: Long,
                                               accountId: Long,
                                               emailAddress: String,
                                               Logger: SRLogger
                                             )(
                                               implicit wsClient: WSClient, ec: ExecutionContext
                                             ): Try[Option[EmailAccount]] = {

    // Fetch Landing Check Error Count

    EmailAccount.getLandingCheckFailedCount(emailAccountId = emailAccountId) match {

      case Failure(exception) =>

        Logger.error(s"getLandingCheckFailedCount Failed :: emailAccountId: $emailAccountId", err = exception)

        Failure(exception)

      case Success(None) =>

        val errMsg = s"getLandingCheckFailedCount returned NONE :: emailAccountId: $emailAccountId"

        Logger.error(errMsg)

        Failure(new Exception(errMsg))

      case Success(Some(count)) =>

        if (count > AppConfig.landingCheckErrorLimit) {

          // Pause this email account and delete all unsent emails.

          val errorMsg = EmailErrors.LandingCheckFailedExceededLimitError.defaultMsg

          for {

            emailAccount: Option[EmailAccount] <- _addErrorForEmailAccountDeleteUnsent(
              emailAccountId = emailAccountId,
              emailAccountError = EmailErrors.LandingCheckFailedExceededLimitError,
              rawErrorOpt = None,
              Logger = Logger
            )(errorMsg = errorMsg)

            // return type is Unit. So, not adding explicit return type.
            _ <- if (emailAccount.isDefined) {
              EmailNotificationService.sendEmailAccountPauseEmailLandingCheckFailed(
                emailAddress = Seq(emailAddress),
                accountId = accountId,
                Logger = Logger
              )
            } else {
              Success(())
            }
          } yield {
            emailAccount
          }

        } else {

          // Just increment the count.

          EmailAccount.incrementLandingCheckFailedCount(emailAccountId = emailAccountId)

        }
    }
  }

  private def handleEmailHardBouncedError(
                                           emailAccountId: Long,
                                           accountId: Long,
                                           emailAddress: String,
                                           Logger: SRLogger
                                         )(
                                           implicit wsClient: WSClient, ec: ExecutionContext
                                         ): Try[Option[EmailAccount]] = {

    // Pause this email account and delete all unsent emails.

    Logger.info(s"handleEmailHardBouncedError. emailAccountId: $emailAccountId :: accountId: $accountId :: emailAddress: $emailAddress")

    val errorMsg = EmailErrors.EmailHardBouncedError.defaultMsg

    for {

      emailAccount: Option[EmailAccount] <- _addErrorForEmailAccountDeleteUnsent(
        emailAccountId = emailAccountId,
        emailAccountError = EmailErrors.EmailHardBouncedError,
        rawErrorOpt = None,
        Logger = Logger
      )(errorMsg = errorMsg)

      // return type is Unit. So, not adding explicit return type.
      _ <- if (emailAccount.isDefined) {
        EmailNotificationService.sendEmailHardBouncedNotification(
          emailAddress = Seq(emailAddress),
          accountId = accountId,
          Logger = Logger
        )
      } else {
        Success(())
      }
    } yield {
      emailAccount
    }
  }

  def handleEmailHealthCheckError(
    emailAccountId: EmailAccountId,
  )(
    implicit Logger: SRLogger,
    wsClient: WSClient,
    ec: ExecutionContext,
  ): Try[Option[EmailAccount]] = {

    Logger.error(s"handleEmailHealthCheckError. emailAccountId: $emailAccountId")

    _addErrorForEmailAccountDeleteUnsent(
      emailAccountId = emailAccountId.id, // TODO: FIXME - VALUE CLASS
      emailAccountError = EmailErrors.EmailHealthCheckError,
      rawErrorOpt = None,
      Logger = Logger
    )(
      errorMsg = EmailErrors.EmailHealthCheckError.defaultMsg,
    )

  }

  // this can throw exceptions
  def handleEmailAccountError(
                               emailAccountId: Long,
                               accountId: Long,
                               emailAddress: String,
                               emailException: Throwable,
                               Logger: SRLogger
                             )(
                               emailAccountError: EmailErrors.EmailAccountError
                             )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Option[EmailAccount]] = {

    Logger.error(s"$emailAccountError handleAccountError", err = emailException)


    /*partial function ends with _*/
    val addErrorAndDeleteUnsent = _addErrorForEmailAccountDeleteUnsent(
      emailAccountId = emailAccountId,
      emailAccountError = emailAccountError,
      rawErrorOpt = Some(emailException.getMessage),
      Logger = Logger
    ) _


    emailAccountError match {

      case EmailErrors.LandingCheckFailedExceededLimitError =>

        handleEmailNotFoundForLandingCheckError(
          emailAccountId = emailAccountId,
          accountId = accountId,
          emailAddress = emailAddress,
          Logger = Logger
        )

      case EmailErrors.EmailHealthCheckError =>

        handleEmailHealthCheckError(
          emailAccountId = EmailAccountId(id = emailAccountId), // TODO: FIXME - VALUE CLASS
        )(
          Logger = Logger,
          wsClient = wsClient,
          ec = ec,
        )


      case EmailErrors.EmailHardBouncedError =>

        handleEmailHardBouncedError(
          emailAccountId = emailAccountId,
          accountId = accountId,
          emailAddress = emailAddress,
          Logger = Logger
        )

      case EmailErrors.AuthenticationFailedError =>

        EmailAccount.getEmailAccountAuthenticationFailedCount(
          emailAccountId = emailAccountId
        ) match {

          case Failure(exception) =>

            Logger.fatal(s"$emailAccountError getEmailAccountAuthenticationFailedCount failed", err = exception)

            Failure(exception)

          case Success(None) =>

            Logger.fatal(s"$emailAccountError getEmailAccountAuthenticationFailedCount Invalid email_account_id")

            Failure(new Exception(s"${Logger.logRequestId} [EmailAccountErrorHandlingService] (emailAccountId: $emailAccountId) $emailAccountError getEmailAccountAuthenticationFailedCount Invalid email_account_id"))

          case Success(Some(auth_failed_count)) =>

            if (auth_failed_count > 2) {
              //pausing emailAccount when two consecutive AuthenticationFails happen

              Logger.error(s"$emailAccountError error auth failed", err = emailException)

              for {
                emailAccount: Option[EmailAccount] <- addErrorAndDeleteUnsent(emailAccountError.defaultMsg)

                // return type is Unit. So, not adding explicit return type.
                _ <- sendAuthenticationErrorNotification(
                  emailAccountError = emailAccountError,
                  emailAccountId = emailAccountId,
                  emailAddress = emailAddress,
                  accountId = accountId,
                  Logger = Logger,
                  e = emailException
                )
              } yield {
                emailAccount
              }

            } else {

              Logger.error(s"$emailAccountError error auth failed:: auth_failed_count: $auth_failed_count", err = emailException)

              Failure(new Exception(s"${Logger.logRequestId} [EmailAccountErrorHandlingService] $emailAccountError error auth failed (emailAccountId: $emailAccountId) :: auth_failed_count: $auth_failed_count"))

            }

        }

      case EmailErrors.GmailSMTPFailedError =>

        addErrorAndDeleteUnsent(s"Gmail send error, will retry after 10 minutes. Error: ${emailException.getMessage}")

      case EmailErrors.SmtpSendFailedWithSpamORRejectedWarning =>

        addErrorAndDeleteUnsent(
          s"Your email domain / host is being blocked by the recipient(s) for sending spam (Please contact support if you have any questions): ${emailException.getMessage}"
        )

      case EmailErrors.GmailDailyUserSendingQuotaExceededError =>

        addErrorAndDeleteUnsent(s"Gmail is throttling your email sending with the 'Daily user sending quota exceeded' message. Sending will be restarted in 24 hours: ${emailException.getMessage}")

      case EmailErrors.UnknownSendError =>

        if (emailException.getMessage != null && emailException.getMessage.toLowerCase.contains("not found")) {
          Logger.fatal(s"handleEmailAccountError EmailErrors.UnknownSendError $emailAccountError FATAL NOT FOUND", err = emailException)
        } else {
          Logger.error(s"handleEmailAccountError EmailErrors.UnknownSendError $emailAccountError error", err = emailException)
        }

        addErrorAndDeleteUnsent(
          s"Error while sending email, will retry after 10 minutes. Error: ${emailException.getMessage}"
        )

      case EmailErrors.ConnectionTimeoutError |
           EmailErrors.ReplyTrackingMessagingExceptionError =>


        addErrorAndDeleteUnsent(emailAccountError.defaultMsg)

      case EmailErrors.UnknownReplyTrackingError =>

        Logger.fatal(s"EmailErrors.UnknownReplyTrackingError handleEmailAccountError", err = emailException)

        addErrorAndDeleteUnsent(emailAccountError.defaultMsg)

    }
  }


  private def _addErrorForEmailAccountDeleteUnsent(
                                        emailAccountId: Long,
                                        emailAccountError: EmailErrors.EmailAccountError,
                                        rawErrorOpt: Option[String],
                                        Logger: SRLogger
                                      )(errorMsg: String)(implicit wsClient: WSClient, ec: ExecutionContext): Try[Option[EmailAccount]] =  {

    Logger.error(s"_addErrorForEmailAccountDeleteUnsent :: $emailAccountError")

    EmailAccount.addError(
      emailAccountId = emailAccountId,
      error = errorMsg,
      rawErrorOpt = rawErrorOpt,
      errorReportedAt = LocalDateTime.now(),
      pausedTill = emailAccountError.pauseTill
    ).flatMap(updatedEmailAccount => {

      if (updatedEmailAccount.isEmpty) {

        Failure(new Exception(s"_addErrorForEmailAccountDeleteUnsent FATAL [EmailService] (emailAccountId: $emailAccountId) error EmailAccount not found while updating error ${Logger.logRequestId}"))

      } else {

        EmailScheduled.deleteUnsentByEmailAccountId(emailAccountId = emailAccountId) match {

          case Failure(e) =>

            Logger.fatal(s"_addErrorForEmailAccountDeleteUnsent EmailScheduled.deleteUnsentByEmailAccountId", err = e)

            Success(updatedEmailAccount)

          case Success(_) =>

            Success(updatedEmailAccount)
        }

      }
    })

  }


  private def sendAuthenticationErrorNotification(
                                                   emailAccountError: EmailErrors.EmailAccountError,
                                                   emailAccountId: Long,
                                                   emailAddress: String,
                                                   accountId: Long,
                                                   Logger: SRLogger,
                                                   e: Throwable
                                                 )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Unit] = {

    emailAccountError match {

      case EmailErrors.AuthenticationFailedError =>

        // send email notification
        EmailNotificationService.sendSmtpImapAuthErrorEmail(
          accountId = accountId,
          emailAccountId = emailAccountId,
          emailAddress = emailAddress,
          errorResponse = e.getMessage,
          Logger = Logger
        )

      case EmailErrors.SmtpSendFailedWithSpamORRejectedWarning |
           EmailErrors.GmailSMTPFailedError |
           EmailErrors.GmailDailyUserSendingQuotaExceededError |
           EmailErrors.UnknownSendError |
           EmailErrors.ConnectionTimeoutError |
           EmailErrors.ReplyTrackingMessagingExceptionError |
           EmailErrors.LandingCheckFailedExceededLimitError |
           EmailErrors.EmailHardBouncedError |
           EmailErrors.EmailHealthCheckError |
           EmailErrors.UnknownReplyTrackingError =>

        // no email notifications need to be sent here
        Success(())

    }
  }

}
