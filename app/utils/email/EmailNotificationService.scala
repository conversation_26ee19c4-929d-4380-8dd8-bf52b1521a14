package utils.email

import api.models.{AccountDB}
import play.api.libs.ws.WSClient
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext}
import scala.util.Try

object EmailNotificationService {

  def sendSmtpImapAuthErrorEmail(
                                  accountId: Long,
                                  emailAccountId: Long,
                                  emailAddress: String,
                                  errorResponse: String,
                                  Logger: SRLogger
                                )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Unit] = Try {


    val account = AccountDB.find(id = accountId).get
    val emailSettingUrl: String = Helpers.makeEmailAccountUrl(emailAccountId = emailAccountId)
    views.html.emails.smtpImapAuthError

    val body = views.html.emails.smtpImapAuthError(

      emailAccountSettingsUrl = emailSettingUrl,
      first_name = account.profile.first_name,
      email_address = emailAddress,
      error = errorResponse.trim

    ).toString()

    val subject = s"[ACTION REQUIRED] Update credentials for your email account: $emailAddress"


    /**
      * 25-May-2024
      *
      * TODO: REVERT this change in a week.
      *
      * We are pausing the email notifications sent to users due to authentication failed error,
      * instead sending those notifications to ourselves.
      */
    EmailHelper.sendMailFromAdmin(
      toEmail = "<EMAIL>",
      ccEmail = Some("<EMAIL>"),
      // toEmail = account.email,
      toName = Some(Helpers.getAccountName(profile = account.profile)),
      subject = subject,
      body = body,
      Logger = Logger

    ).map(res => {

      res

    })
  }



  def sendEmailAccountPauseEmail(
                                  accountId: Long,
                                  emailAddress: Seq[String],
                                  Logger: SRLogger
                                )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Unit] = Try {


    val account = AccountDB.find(id = accountId).get


    //TODO this body should be changed in final go
    val body =
      s"""
         <h3>Paused emails</h3>
         <br/>
         ${emailAddress.mkString("\n")}
         """.stripMargin

    val subject = s"[ACTION REQUIRED] Your email account(s) paused due to accounts exceeds warm up limit"

    EmailHelper.sendMailFromAdmin(

      toEmail = account.email,
      toName = Some(Helpers.getAccountName(profile = account.profile)),
      subject = subject,
      body = body,
      Logger = Logger

    ).map(res => {

      res

    })
  }


  def sendEmailAccountPauseEmailLandingCheckFailed(
                                  accountId: Long,
                                  emailAddress: Seq[String],
                                  Logger: SRLogger
                                )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Unit] = Try {


    val account = AccountDB.find(id = accountId).get

    val body =
      s"""
         <h3>Paused emails</h3>

         <br/>

         Warmup has been paused for this email inbox as WarmupHero is not able to check warmup emails in this inbox.

         </br>
         </br>

         Please verify and remove any filters in the inbox that might be filtering our warmup emails,
         and then start email warmup again.

         </br>
         </br>

         ${emailAddress.mkString("\n")}
         """.stripMargin

    val subject = s"[ACTION REQUIRED] Warmup has been paused of some of your email accounts"

    EmailHelper.sendMailFromAdmin(

      toEmail = account.email,
      toName = Some(Helpers.getAccountName(profile = account.profile)),
      subject = subject,
      body = body,
      Logger = Logger

    ).map(res => {

      res

    })
  }

  def sendEmailHardBouncedNotification(
                                        accountId: Long,
                                        emailAddress: Seq[String],
                                        Logger: SRLogger
                                      )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Unit] = Try {


    // TODO: Email Content

    val account = AccountDB.find(id = accountId).get

    val body =
      s"""
         <h3>Paused emails</h3>

         <br/>

         Warmup has been paused for this email inbox as an email sent was hard bounced

         </br>
         </br>

         ${emailAddress.mkString("\n")}
         """.stripMargin

    val subject = s"[ACTION REQUIRED] Warmup has been paused of some of your email accounts"

    /**
      * 06-Apr-2024
      *
      * TODO: REVERT this change in a week.
      *
      * We are pausing the email notifications sent to users when hard bounce occurs,
      * instead sending those notifications to ourselves.
      */
    EmailHelper.sendMailFromAdmin(
      toEmail = "<EMAIL>",
      ccEmail = Some("<EMAIL>"),
      toName = Some(Helpers.getAccountName(profile = account.profile)),
      subject = subject,
      body = body,
      Logger = Logger
    ).map(res => {

      res

    })
  }


  def sendEmailForDeactivatedAllEmailAccountsDueToInvalidAPIKey(
                                  accountId: Long,
                                  Logger: SRLogger
                                )(implicit wsClient: WSClient, ec: ExecutionContext): Try[Unit] = Try {


    val account = AccountDB.find(id = accountId).get

    val body = s"""
          <br/>All your email accounts are deactivated due to your SmartReach API key is invalid
          <br/>
          <br/>Update your api key by going to
          <br/>Settings -> SMARTREACH -> API Key -> Update API key
          <br/>
    """.stripMargin

    val subject = s"[ACTION REQUIRED] All your email account(s) are deactivated"

    EmailHelper.sendMailFromAdmin(

      toEmail = account.email,
      toName = Some(Helpers.getAccountName(profile = account.profile)),
      subject = subject,
      body = body,
      Logger = Logger

    ).map(res => {

      res

    })
  }

}

