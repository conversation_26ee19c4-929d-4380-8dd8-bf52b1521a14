package utils.email


import akka.actor.ActorSystem
import api.emails._
import play.api.libs.ws.WSClient
import utils.{Help<PERSON>, SRLogger}
import utils.mq.email.MQEmailMessage

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import api.models.{EmailAccount, EmailScheduled, EmailSendDetail, EmailServiceProvider, EmailSettings}
import io.smartreach.esp.api.emails.InternetMessageId

import java.time.LocalDateTime

case class MQDoNotNackException(message: String = "", cause: Throwable = None.orNull)
  extends Exception(message, cause)

object EmailSendingService {


  def processSendEmailRequest(
                               msg: MQEmailMessage
                             )(
                               implicit executionContext: ExecutionContext,
                               wSClient: WSClient,
                               system: ActorSystem,
                               SRLogger: SRLogger
                             ): Future[SendEmailResponse] = {

    val logRId1 = s"[EmailSendingService] processSendEmailRequest: email_scheduled_id_${msg.emailScheduledId}"

    SRLogger.info(s"$logRId1 start")

    EmailScheduled.getScheduleDetailsForSending(emailScheduledId = msg.emailScheduledId) match {
      case Failure(err1) =>
        SRLogger.fatal(s"$logRId1", err = err1)

        Future.failed(MQDoNotNackException(s"${SRLogger.logRequestId} [EmailSendingService] processSendEmailRequest: emailScheduledId: ${msg.emailScheduledId} Server Error"))

      case Success(None) =>
        SRLogger.fatal(s"$logRId1 email_scheduled_id NOT_FOUND")

        Future.failed(MQDoNotNackException(s"${SRLogger.logRequestId} [EmailSendingService] processSendEmailRequest: emailScheduledId: ${msg.emailScheduledId} error: Not Found"))

      case Success(Some(data)) =>

        val emailAccountId = data.sender_email_account_id

        if(data.sender_email_setting_paused_till.isDefined && data.sender_email_setting_paused_till.get.isAfter(LocalDateTime.now())) {

          SRLogger.fatal(s"already paused, dropping off queue sender_email_account_id: ${data.sender_email_account_id} :: es_id: ${data.id}")

          // drop the message from schedule / queue
          EmailScheduled.deleteUnsentByEmailAccountId(emailAccountId = emailAccountId) match {

            case Failure(e) => Future.failed(e)

            case Success(n) =>

              // do not nack
              Future.failed(MQDoNotNackException(s"${SRLogger.logRequestId} email is now paused so deleted from queue :: total: $n"))

          }


        } else {

          val Logger = SRLogger.appendLogRequestId(s"$logRId1 ea_$emailAccountId")

          canSendMoreEmails(
            senderEmailAccountId = data.sender_email_account_id,
            isSendingReply = data.is_reply,
            senderAccountId = data.account_id,
          ) match {
            case Failure(exception) =>

              Logger.error(
                msg = s"Failed to check canSendMoreEmails. senderEmailAccountId: ${data.sender_email_account_id} :: isReply: ${data.is_reply}",
                err = exception,
              )

              Future.failed(MQDoNotNackException(s"${SRLogger.logRequestId} [EmailSendingService] processSendEmailRequest: emailScheduledId: ${msg.emailScheduledId} Server Error - canSendMoreEmails"))

            case Success(canSendMoreEmails) =>


              if (canSendMoreEmails) {

                sendEmail(
                  data = data,
                  emailAccountId = emailAccountId,
                  Logger = Logger,
                )

              } else {

                Logger.error(
                  msg = s"Cannot send more emails today. emailScheduledId: ${msg.emailScheduledId} :: ${data.sender_email_account_id} :: isReply: ${data.is_reply}",
                )

                Future.failed(MQDoNotNackException(s"${SRLogger.logRequestId} [EmailSendingService] processSendEmailRequest: emailScheduledId: ${msg.emailScheduledId} Cannot send more emails today"))

              }

          }

        }

    }

  }

  private def canContinue(
    sentOrScheduledToday: Int,
    senderEmailAccountId: Long,
    senderAccountId: Long,
    isSendingOrSchedulingReply: Boolean,
  )(
    implicit Logger: SRLogger,
  ): Try[Boolean] = {

    for {

      dailyLimit: Int <- EmailAccount.getEmailAccountDailyLimit(
        emailAccountId = senderEmailAccountId,
        accountId = senderAccountId,
      ) match {

        case Failure(exception) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email account daily limit. emailAccountId: $senderEmailAccountId",
            err = Some(exception)
          )

          Failure(exception)

        case Success(None) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email account daily limit - None. emailAccountId: $senderEmailAccountId",
            err = None,
          )

          Failure(new Exception("Email account daily limit not found"))

        case Success(Some(limit)) =>

          Success(limit)

      }

      canSendOrScheduleMoreEmails: Boolean = {

        Logger.debug(
          msg = s"canContinue - isReply: $isSendingOrSchedulingReply :: senderAccountId: $senderAccountId :: senderEmailAccountId: $senderEmailAccountId :: sentToday: $sentOrScheduledToday :: dailyLimit: $dailyLimit"
        )

        // Lets send one less than the daily limit.
        // If users complain we will remove this.
        //
        // 23-Jun-2025:
        //  A customer complained that his email accounts
        //  were sending less than the daily limit.
        //  so we are removing the -1
        sentOrScheduledToday < dailyLimit

      }

    } yield {

      canSendOrScheduleMoreEmails

    }


  }

  private def canSendMoreEmails(
    senderEmailAccountId: Long,
    senderAccountId: Long,
    isSendingReply: Boolean,
  )(
    implicit Logger: SRLogger,
  ): Try[Boolean] = {

    for {

      sentToday: Int <- EmailScheduled.getTotalNewEmailsOrRepliesSentToday(
        emailAccountId = senderEmailAccountId,
        isReply = isSendingReply,
        accountId = senderAccountId,
      ) match {

        case Failure(exception) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email send today count. emailAccountId: $senderEmailAccountId :: isReply: $isSendingReply",
            err = Some(exception)
          )

          Failure(exception)

        case Success(None) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email send today count - None. emailAccountId: $senderEmailAccountId :: isReply: $isSendingReply",
            err = None,
          )

          // If not emails are sent for the day we will treat it as 0
          // There is a shouldNeverHappen above because count(NULL) should be equal to 0
          // So, this case should never be reached
          Success(0)

        case Success(Some(sentCount)) =>

          Success(sentCount)

      }

      canSendMoreEmails: Boolean <- canContinue(
        sentOrScheduledToday = sentToday,
        senderEmailAccountId = senderEmailAccountId,
        senderAccountId = senderAccountId,
        isSendingOrSchedulingReply = isSendingReply,
      )

    } yield {

      canSendMoreEmails

    }

  }

  def canScheduleMoreEmails(
    senderEmailAccountId: Long,
    senderAccountId: Long,
    isSchedulingReply: Boolean,
  )(
    implicit Logger: SRLogger,
  ): Try[Boolean] = {

    for {

      scheduledToday: Int <- EmailScheduled.getTotalNewEmailsOrRepliesScheduledToday(
        emailAccountId = senderEmailAccountId,
        isReply = isSchedulingReply,
        accountId = senderAccountId,
      ) match {

        case Failure(exception) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email scheduled today count. emailAccountId: $senderEmailAccountId :: isReply: $isSchedulingReply",
            err = Some(exception)
          )

          Failure(exception)

        case Success(None) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email scheduled today count - None. emailAccountId: $senderEmailAccountId :: isReply: $isSchedulingReply",
            err = None,
          )

          // If not emails are sent for the day we will treat it as 0
          // There is a shouldNeverHappen above because count(NULL) should be equal to 0
          // So, this case should never be reached
          Success(0)

        case Success(Some(sentCount)) =>

          Success(sentCount)

      }

      canScheduleMoreEmails: Boolean <- canContinue(
        sentOrScheduledToday = scheduledToday,
        senderEmailAccountId = senderEmailAccountId,
        senderAccountId = senderAccountId,
        isSendingOrSchedulingReply = isSchedulingReply,
      )

    } yield {

      canScheduleMoreEmails

    }

  }

  private def sendEmail(
    data: EmailSendDetail,
    emailAccountId: Long,
    Logger: SRLogger,
  )(
    implicit executionContext: ExecutionContext,
    wSClient: WSClient,
    system: ActorSystem,
  ): Future[SendEmailResponse] = {

    implicit val SRLogger: SRLogger = Logger

    sendEmailToProspect(
      data = data
    ).flatMap { emailThatWasSent =>

        Logger.success(
          msg = s"sendEmailToProspect DONE - isReply: ${data.is_reply} :: accountId: ${data.account_id} :: senderEmailAccountId: ${data.sender_email_account_id} :: senderEmail: ${data.from_email} :: receiverEmailAccountId: ${data.receiver_email_account_id} :: receiverEmail: ${data.to_emails}"
        )

        Future.fromTry(

          EmailScheduled.isSent(
            emailSentId = data.id,
            data = emailThatWasSent,
            Logger = Logger
          ) match {

            case Failure(e) =>

              throw e

            case Success(_) =>
              /*
                * NOTE: after sending reply to primary here we are updating primary email replied status
                * */
              if (data.is_reply) {
                EmailScheduled.updateRepliedStatus(
                  senderEmailAccountId = data.receiver_email_account_id,
                  receiverEmailAccountId = data.sender_email_account_id,
                  Logger = Logger) match {

                  case Failure(e) =>

                    throw e

                  case Success(_) =>

                    Try {emailThatWasSent}
                }
              } else {
                Try {
                  emailThatWasSent
                }
              }
          }


        )
      }
      .recover { case exception => {

        // partial function ends with _
        val handleError = EmailAccountErrorHandlingService.handleEmailAccountError(
          accountId = data.account_id,
          emailAccountId = emailAccountId,
          emailAddress = data.from_email,
          emailException = exception,
          Logger = Logger
        ) _

        val handleError_result = exception match {

          case e: javax.mail.AuthenticationFailedException =>

            SRLogger.fatal(
              msg = s"Authentication failed. accountId: ${data.account_id} :: emailAccountId: $emailAccountId :: email: ${data.from_email}",
              err = e
            )

            handleError(EmailErrors.AuthenticationFailedError)

          case e@(_: com.sun.mail.smtp.SMTPSendFailedException | _: javax.mail.MessagingException) =>

            if (e.getMessage != null &&
              e.getMessage.toLowerCase.contains("Daily user sending quota exceeded".toLowerCase())
            ) {

              handleError(EmailErrors.GmailDailyUserSendingQuotaExceededError)

            } else if (e.getMessage != null && e.getMessage.toLowerCase.contains("gsmtp")) {

              handleError(EmailErrors.GmailSMTPFailedError)

            } else {

              handleError(EmailErrors.UnknownSendError)

            }

          case e: SRSmtpSendFailedWithSpamORRejectedWarning => handleError(EmailErrors.SmtpSendFailedWithSpamORRejectedWarning)

          case e => handleError(EmailErrors.UnknownSendError)

        }

        handleError_result match {
          case Failure(err) =>
            Logger.fatal("FATAL handleError_Result", err = err)

            throw err

          case Success(_) =>
            // do not nack
            throw MQDoNotNackException(s"${Logger.logRequestId} [EmailSendingService] error (emailAccountId: $emailAccountId) email is now paused so deleted from queue")
        }

      }
      }


  }

  def sendEmailToProspect(
                           data: EmailSendDetail
                         )(
                           implicit executionContext: ExecutionContext,
                           wSClient: WSClient,
                           system: ActorSystem,
                           Logger: SRLogger
                         ): Future[SendEmailResponse] = {

    val isReply = data.is_reply

    val inReplyToHeader = if (isReply && data.in_reply_to_header.isDefined) data.in_reply_to_header else None
    val referencesHeader = if (isReply && data.references_header.isDefined) data.references_header else None

    val emailToBeSent = EmailToBeSent(
      sender_email_settings_id = data.sender_email_account_id,
      to_emails = data.to_emails,
      from_email = data.from_email.trim,
      from_name = data.from_name,
      subject = data.subject,
      body = data.body,
      text_body = data.text_body,
      message_id = Some(EmailHelper.getMessageId(data.sender_email_account_id, data.message_id_suffix)),
      is_reply = isReply,
      in_reply_to_header = inReplyToHeader,
      references_header = referencesHeader
    )

    val res: Future[SendEmailResponse] = data.send_settings match {

      case smtpEmailAccount: EmailSettings.SmtpEmailAccount =>

        val emailServiceOpt: Option[TEmailService] = data.service_provider match {

          case api.models.EmailServiceProvider.GOOGLE_WORKSPACE =>
            Some(GmailService)

          case api.models.EmailServiceProvider.MICROSOFT_365 =>
            Some(OutlookService)

          case api.models.EmailServiceProvider.OTHER =>
            Some(GenericEmailService)

          case api.models.EmailServiceProvider.MICROSOFT_365_API =>
            None
        }

        emailServiceOpt match {

          case Some(emailService) =>

            emailService.sendEmail(sendCredentials = smtpEmailAccount, email = emailToBeSent).flatMap { _ =>

              if (emailToBeSent.message_id.isEmpty) {

                val errMsg = s"messageId is empty for smtpEmailAccount. sender_email_settings_id: ${emailToBeSent.sender_email_settings_id}"

                Logger.error(errMsg)

                Future.failed(new Exception(errMsg))
              } else {

                Future.successful(
                  SendEmailResponse(
                    messageId = InternetMessageId(emailToBeSent.message_id.get)
                  )
                )
              }
            }

          case None =>
            Future.failed(new Exception("Invalid EmailServiceProvider for SMTP/IMAP."))
        }

      case oauthTokens: EmailSettings.OAuthTokens =>

        val emailServiceOpt: Option[EmailSendService[EmailSettings.OAuthTokens, SendEmailResponse]] = data.service_provider match {
          case api.models.EmailServiceProvider.MICROSOFT_365_API =>
            Some(OutlookApi)

          case api.models.EmailServiceProvider.GOOGLE_WORKSPACE |
               api.models.EmailServiceProvider.MICROSOFT_365 |
               api.models.EmailServiceProvider.OTHER =>
            None
        }

        emailServiceOpt match {

          case Some(emailService) =>

            emailService.sendEmail(sendCredentials = oauthTokens, email = emailToBeSent)

          case None =>

            Future.failed(new Exception("Invalid EmailServiceProvider for OAuth."))
        }
    }

    res
      .recover {
        case e =>

          val logErr = s"[EmailSendingService] error while sendEmail: ${data.id} from: ${data.from_email} to: ${data.to_emails} error: ${Helpers.getStackTraceAsString(e)}"

          Logger.error(s"$logErr")

          throw e
      }
  }
}
