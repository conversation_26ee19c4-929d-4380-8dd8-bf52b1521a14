package utils.cronjobs

import akka.actor.ActorSystem
import akka.stream.Materializer
import api.AppConfig
import api.models._
import api.services.AccountService
import io.smartreach.esp.utils.email.EmailHelperCommon
import io.sr.llm.bedrock.api.AwsBedrockLlama3Api
import io.sr.llm.bedrock.services.SrLLMService
import org.joda.time._
import play.api.Logger
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.email.{EmailHelper, EmailSendingService}
import utils.mq._
import utils.{Helpers, SRLogger}

import java.time.LocalDateTime
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


case class EmailBodyDetails(
  bodyWithoutSignatureAndSalutation: String,
)

object EmailReplyIngCronService extends CronTrait {

  override val cronName: String = "EmailSendingReplyIngCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronScheduleEmailIntervalInSeconds


  private lazy val awsBedrockLlama3Api = new AwsBedrockLlama3Api

  private lazy val srLLMService = new SrLLMService(awsBedrockLlama3Api = awsBedrockLlama3Api)

  def executeCron(
                   logger: SRLogger
                 )(
    implicit system: ActorSystem,
    ec: ExecutionContext,
    wSClient: AhcWSClient
  ) = {
    // NOTE: https://stackoverflow.com/questions/32255919/akka-scheduler-stops-on-exception-is-it-expected
    Try {

      logger.info(s"[EmailReplyIngCronService] execute intervalSecs: $cronIntervalInSeconds")

      scheduleAllEmailScheduledForReply(Logger = logger) match {

        case Failure(e) =>

          if (e.getMessage.contains("No active")) {
            logger.warn(s"[EmailReplyIngCronService] ${e.getMessage}")
          } else {
            logger.error(s"[EmailReplyIngCronService] error: ${Helpers.getStackTraceAsString(e)}")
          }

        case Success(_) =>
          logger.info(s"[EmailReplyIngCronService] success")

      }
    } match {

      case Failure(e) =>
        logger.error(s"[EmailReplyIngCronService] final FATAL error: ${Helpers.getStackTraceAsString(e)}")

      case Success(_) =>
        logger.info(s"[EmailReplyIngCronService] final success")

    }

  }


  def scheduleAllEmailScheduledForReply(Logger:SRLogger): Try[Boolean] = {


    for {

      foundEmailScheduledIdsForReply: List[Long] <- EmailScheduled.getAllEmailScheduledReadyForReply()

      updatedScheduledForReply: List[Long] <- EmailScheduled.updateScheduledForReply(emailScheduledIdsToBeUpdated = foundEmailScheduledIdsForReply)

    } yield {

      val filteredEmailScheduledIds = foundEmailScheduledIdsForReply
        .filter(es => {
          updatedScheduledForReply.contains(es)
        })

      filteredEmailScheduledIds.foreach(esId => {

            // publish emailAccountId for scheduling, regardless of warning
            Logger.info(s"[EmailReplyIngCronService] publishing to rabbimq MQEmailSchedulerForReply ${esId}")
            MQEmailSchedulerForReply.publish(MQEmailSchedulerMessageForReply(emailScheduledId = esId))

          })

          true

    }
  }

  private def generateEmailReplyWithAI(
    emailSubject: String,
    emailSubjectTemplateId: Long,
    recipientDepartment: String,
    recipientPosition: String,
  )(
    implicit ec: ExecutionContext,
    logger: SRLogger
  ): Future[EmailBodyDetails] = {

    val generatedReplyFut = srLLMService.generatePositiveReplyEmailBody(
      emailSubject = emailSubject,
    )

    val _: Future[Option[Long]] = generatedReplyFut.flatMap { generatedReply =>

      Future.fromTry {

        EmailTemplate.createEmailBodyTemplate(
          generatedEmail = generatedReply,
          isReply = true,
          emailSubjectTemplateId = emailSubjectTemplateId
        ) match {

          case Failure(exception) =>

            logger.logAndSendWarmupSmsAlert(
              msg = s"Failed to create reply email body template. generatedReply: $generatedReply :: emailSubjectTemplateId: $emailSubjectTemplateId",
              err = Some(exception),
            )

            Failure(exception)

          case Success(id) =>

            Success(id)

        }

      }

    }

    generatedReplyFut.map { generateReplyEmailBodyRes =>

      EmailBodyDetails(
        bodyWithoutSignatureAndSalutation = generateReplyEmailBodyRes.body,
      )

    }

  }

  private def generateReplyEmailBody(
    senderEmailAccount: EmailAccountForCreateEmailSchedule,
    receiverEmailAccount: EmailAccountForCreateEmailSchedule,
    emailSubject: String,
    emailSubjectTemplateId: Long,
  )(
    implicit ec: ExecutionContext,
    ws: WSClient,
    logger: SRLogger
  ): Future[EmailBodyDetails] = {

    // When sending reply email we need to check if receiver email account support AI warmup.
    val enableAIEmailGenerationForWarmupheroFut = AccountService.getAccountMetadataByAccountId(
      accountId = receiverEmailAccount.account_id
    )
      .map(_.enable_ai_email_generation_for_warmuphero)
      .recover { case e =>

        logger.error(
          msg = s"Failed to fetch account metadata. accountId: ${senderEmailAccount.account_id}",
          err = e
        )

        // fallback
        false

      }

    enableAIEmailGenerationForWarmupheroFut.flatMap { enable_ai_email_generation_for_warmuphero =>

      if (!enable_ai_email_generation_for_warmuphero) {

        val email_body = EmailHelper.getEmailBody()

        Future.successful(

          EmailBodyDetails(
            bodyWithoutSignatureAndSalutation = email_body,
          )

        )

      } else {

        EmailTemplate.findBodyForAIEmailReply(
          emailSubjectTemplateId = emailSubjectTemplateId,
          emailSubject = emailSubject,
        ) match {

          case Failure(exception) =>

            logger.logAndSendWarmupSmsAlert(
              msg = s"Failed to findBodyForAIEmailReply. emailSubjectTemplateId: $emailSubjectTemplateId :: emailSubject: $emailSubject",
              err = Some(exception),
            )

            generateEmailReplyWithAI(
              emailSubjectTemplateId = emailSubjectTemplateId,
              emailSubject = emailSubject,
              recipientDepartment = receiverEmailAccount.email_type.getEmailAccountOwnerDepartment,
              recipientPosition = receiverEmailAccount.email_type.getEmailAccountOwnerPosition,
            )

          case Success(None) =>

            generateEmailReplyWithAI(
              emailSubjectTemplateId = emailSubjectTemplateId,
              emailSubject = emailSubject,
              recipientDepartment = receiverEmailAccount.email_type.getEmailAccountOwnerDepartment,
              recipientPosition = receiverEmailAccount.email_type.getEmailAccountOwnerPosition,
            )

          case Success(Some(value)) =>

            Future.successful(value)

        }

      }

    }

  }

  private def checkAndMarkIfReceiverLimitReachedForReply(
    senderEmailAccountId: Long,
    senderAccountId: Long,
    emailScheduledId: Long,
  )(
    implicit Logger: SRLogger
  ): Try[Boolean] = {

    EmailSendingService.canScheduleMoreEmails(
      senderEmailAccountId = senderEmailAccountId,
      senderAccountId = senderAccountId,
      isSchedulingReply = true,
    ) match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(
          msg = s"Failed to check canSendMoreEmails. senderEmailAccountId: $senderEmailAccountId :: senderAccountId: $senderAccountId :: isReply: true",
          err = Some(exception),
        )

        Failure(exception)

      case Success(canSendMore) =>

        if (canSendMore) {

          Success(true)

        } else {

          EmailAccount.markReceiverLimitReachedForReply(
            senderEmailAccountId = senderEmailAccountId,
            emailScheduledId = emailScheduledId,
          ) match {

            case Failure(exception) =>

              Logger.shouldNeverHappen(
                msg = s"Failed to markReceiverLimitReachedForReply. senderEmailAccountId: $senderEmailAccountId :: senderAccountId: $senderAccountId",
                err = Some(exception),
              )

              Failure(exception)

            case Success(_) =>

              Success(false)

          }

        }

    }

  }


  def scheduleEmailAccountForReply(emailScheduledId: Long, Logger: SRLogger)(implicit ws: WSClient, ec: ExecutionContext, materializer: Materializer): Future[(Int, Option[DateTime])] = Future {

    Logger.info(s"scheduleEmailAccountForReply started emailScheduledId: $emailScheduledId")

    EmailScheduled.find(id = emailScheduledId, Logger = Logger) match {

      case Failure(e) =>
        Logger.error(s"scheduleEmailAccountForReply  EmailScheduled.getScheduleDetailsForSending error emailScheduledId: $emailScheduledId ::  ${Helpers.getStackTraceAsString(e)}")

        Future.failed(MQDoNotNackException(s"${Logger.logRequestId} EmailScheduled.find error"))

      case Success(None) =>

        Logger.error(s"scheduleEmailAccountForReply EmailScheduled.getScheduleDetailsForSending NONE found: $emailScheduledId")

        Future.successful((0, None))

      case Success(Some(emailScheduledDetailsForReply)) =>

        val receiverEmailAccountId = emailScheduledDetailsForReply.sender_email_account_id //sender become receiver
      val senderEmailAccountId = emailScheduledDetailsForReply.receiver_email_account_id //receiver become sender

        EmailAccount.findForScheduling(id = receiverEmailAccountId) match {

          case Failure(e) =>

            Logger.fatal(s"scheduleEmailAccountForReply EmailAccount.findForScheduling receiverEmailAccount error receiverEmailAccountId: $receiverEmailAccountId", err = e)

            Future.successful((0, None))

          case Success(None) =>

            Logger.error(s"scheduleEmailAccountForReply EmailAccount.find NONE found: receiverEmailAccountId: $receiverEmailAccountId")

            Future.successful((0, None))

          case Success(Some(receiverEmailAccount)) =>

            Logger.info(s"scheduleEmailAccountForReply receiverEmailAccount found: ${receiverEmailAccount.email}")

            if (receiverEmailAccount.paused_till.isDefined && receiverEmailAccount.paused_till.get.isAfter(LocalDateTime.now())) {

              Logger.error(s"scheduleEmailAccountForReply receiverEmailAccount is paused:: receiverEmailAccountId: $receiverEmailAccountId")

              Future.failed(MQDoNotNackException(s"${Logger.logRequestId} email account has error:: receiverEmailAccountId: $receiverEmailAccountId"))

            } else {

              EmailAccount.findForScheduling(id = senderEmailAccountId) match {

                case Failure(e) =>

                  Logger.fatal(s"scheduleEmailAccountForReply EmailAccount.findForScheduling senderEmailAccount error:: senderEmailAccountId: $senderEmailAccountId", err = e)

                  Future.successful((0, None))

                case Success(None) =>

                  Logger.error(s"scheduleEmailAccountForReply EmailAccount.findForScheduling NONE found senderEmailAccountId: $senderEmailAccountId")

                  Future.successful((0, None))

                case Success(Some(senderEmailAccount)) =>

                  val canSendMoreReplies = checkAndMarkIfReceiverLimitReachedForReply(
                    senderEmailAccountId = senderEmailAccountId,
                    senderAccountId = senderEmailAccount.account_id,
                    emailScheduledId = emailScheduledId,
                  )(Logger = Logger) match {

                    case Failure(exception) =>

                      Logger.shouldNeverHappen(
                        msg = s"Failed to checkAndMarkIfReceiverLimitReachedForReply. senderEmailAccountId: $senderEmailAccountId :: senderAccountId: ${senderEmailAccount.account_id} :: emailScheduledId: $emailScheduledId",
                        err = Some(exception),
                      )

                      false

                    case Success(canSendMore) =>

                      canSendMore

                  }

                  Logger.info(s"scheduleEmailAccountForReply senderEmailAccount found: ${senderEmailAccount.email} :: canSendMoreReplies: $canSendMoreReplies")

                  if (!canSendMoreReplies) {

                    Logger.error(
                      msg = s"scheduleEmailAccountForReply receiver limit reached for reply. senderEmailAccountId: $senderEmailAccountId :: senderAccountId: ${senderEmailAccount.account_id} :: emailScheduledId: $emailScheduledId",
                    )

                    Future.failed(MQDoNotNackException(s"${Logger.logRequestId} [EmailReplyIngCronService] scheduleEmailAccountForReply: emailScheduledId: $emailScheduledId receiver limit reached for reply"))

                  } else if (senderEmailAccount.error.isDefined && senderEmailAccount.paused_till.get.isAfter(LocalDateTime.now())) {

                    Logger.error(s"scheduleEmailAccountForReply senderEmailAccount is paused:: senderEmailAccount: $senderEmailAccountId")

                    Future.failed(MQDoNotNackException(s"${Logger.logRequestId} email account has error:: senderEmailAccountId: $senderEmailAccountId"))

                  } else {

                    // If we pass all the above checks, we schedule the reply - success case

                    generateReplyEmailBody(
                      senderEmailAccount = senderEmailAccount,
                      receiverEmailAccount = receiverEmailAccount,
                      emailSubject = emailScheduledDetailsForReply.subject,
                      emailSubjectTemplateId = emailScheduledDetailsForReply.template_id,
                    )(
                      logger = Logger,
                      ws = ws,
                      ec = ec,
                    ).flatMap { emailBodyDetails =>

                      val salutationHtml = EmailHelper.getSalutationHtml(
                        firstName = receiverEmailAccount.first_name,
                      )

                      val signatureHtml = EmailHelper.getSignatureHtml(
                        signature = senderEmailAccount.signature,
                        firstName = senderEmailAccount.first_name,
                      )

                      val htmlBody = EmailHelper.makeHTMLBody(
                        baseBody = emailBodyDetails.bodyWithoutSignatureAndSalutation,
                        salutation = salutationHtml,
                        signature = signatureHtml,
                      )

                      val textBody = EmailHelperCommon.getTextBodyFromHtmlBody(htmlBody)

                      val messageId = if (emailScheduledDetailsForReply.message_id.isDefined) emailScheduledDetailsForReply.message_id else None

                      val emailsToBeScheduled = EmailToBeScheduled(
                        sender_email_account_id = senderEmailAccountId,
                        from_email = senderEmailAccount.email,
                        from_name = senderEmailAccount.sender_name,
                        receiver_email_account_id = receiverEmailAccount.id,
                        to_email = receiverEmailAccount.email,
                        to_name = receiverEmailAccount.sender_name,
                        htmlBody = htmlBody,
                        textBody = textBody,
                        subject = EmailHelper.getSubjectToReplay(sub = emailScheduledDetailsForReply.subject),
                        template_id = emailScheduledDetailsForReply.template_id,
                        is_reply = true,
                        account_id = senderEmailAccount.account_id,
                        warmup_email_account_id = emailScheduledDetailsForReply.warmup_email_account_id,
                        email_thread_id = emailScheduledDetailsForReply.email_thread_id,
                        /*primary email messageId will be used as in_reply_to_header and references_header for reply email*/
                        in_reply_to_header = messageId,
                        references_header = messageId
                      )

                      val savedEmailScheduled = EmailScheduled.saveEmailsToBeScheduled(
                        emailsToBeScheduled = emailsToBeScheduled,
                        Logger = Logger
                      ).get


                      val latestEmailScheduledAt = Some(DateTime.now())

                      Try {
                        EmailAccount._updateLastScheduled(emailAccountId = senderEmailAccountId, latestEmailScheduledAt = latestEmailScheduledAt,
                          Logger = Logger
                        )

                        EmailScheduled.updateIsScheduledForReply(emailScheduledId = emailScheduledId, Logger = Logger)
                      } match {

                        case Failure(e) =>
                          Logger.fatal(s"${Helpers.getStackTraceAsString(e)}")
                          Future.successful((savedEmailScheduled.length, latestEmailScheduledAt))


                        case Success(_) =>

                          Future.successful((savedEmailScheduled.length, latestEmailScheduledAt))

                      }

                    }

                  }
              }
            }
        }
    }

  }.flatMap(identity)

}
