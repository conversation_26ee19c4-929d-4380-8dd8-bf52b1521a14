package utils.cronjobs

import akka.actor.ActorSystem
import api.AppConfig
import api.models._
import play.api.Logger
import play.api.libs.ws.ahc.AhcWSClient
import utils.mq.{MQEmailScheduledForLandingCheck, MQEmailSchedulerForLandingCheck}
import utils.{Helpers, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


object EmailLandingCronService extends CronTrait {

  override val cronName: String = "EmailLandingCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronScheduleEmailIntervalInSeconds



  def executeCron(
                   logger: SRLogger
                 )(
    implicit system: ActorSystem,
    ec: ExecutionContext,
    wSClient: AhcWSClient
  ) = {
    // NOTE: https://stackoverflow.com/questions/32255919/akka-scheduler-stops-on-exception-is-it-expected
    Try {

      logger.info(s"[EmailLandingCronService] execute intervalSecs: $cronIntervalInSeconds")

      scheduleAllActiveSentEmailsForCheckLandingLocation(Logger = logger) match {

        case Failure(e) =>

          if (e.getMessage.contains("No active")) {
            logger.warn(s"[EmailLandingCronService] ${e.getMessage}")
          } else {
            logger.error(s"[EmailLandingCronService] error: ${Helpers.getStackTraceAsString(e)}")
          }

        case Success(_) =>
          logger.info(s"[EmailLandingCronService] success")

      }
    } match {

      case Failure(e) =>
        logger.error(s"[EmailLandingCronService] final FATAL error: ${Helpers.getStackTraceAsString(e)}")

      case Success(_) =>
        logger.info(s"[EmailLandingCronService] final success")

    }

  }


  def scheduleAllActiveSentEmailsForCheckLandingLocation(Logger: SRLogger) = Try {

    EmailScheduled.getAllSentEmailsReadyForCheckLandingFolder() match {

      case Failure(e) =>

        throw new Exception(s"[EmailLandingCronService] scheduleAllActiveSentEmailsForCheckLandingLocation error: ${Helpers.getStackTraceAsString(e)}")


      case Success(emailScheduled) =>

        if (emailScheduled.isEmpty) {

          throw new Exception("[EmailLandingCronService] scheduleAllActiveSentEmailsForCheckLandingLocation No active email scheduled found for landing check")

        } else {

          Logger.info(s"[EmailLandingCronService] scheduleAllActiveSentEmailsForCheckLandingLocation found: ${emailScheduled.mkString(", ")}")

          emailScheduled.foreach(esId => {

            Logger.info(s"[EmailLandingCronService] publishing to rabbimq MQEmailScheduler ${esId}")
            MQEmailSchedulerForLandingCheck.publish(MQEmailScheduledForLandingCheck(emailScheduledId = esId))

          })
        }

    }
  }

}
