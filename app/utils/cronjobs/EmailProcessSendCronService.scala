package utils.cronjobs

import akka.actor.ActorSystem
import api.AppConfig
import api.models._
import play.api.Logger
import play.api.libs.ws.ahc.AhcWSClient
import utils.mq.email.{MQEmail, MQEmailMessage}
import utils.{<PERSON><PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext}
import scala.util.{Failure, Success, Try}


object EmailProcessSendCronService extends CronTrait {

  override val cronName: String = "EmailSendingReplyIngCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronScheduleEmailIntervalInSeconds



  def executeCron(
                   logger: SRLogger
                 )(
    implicit system: ActorSystem,
    ec: ExecutionContext,
    wSClient: AhcWSClient
  ) = {
    // NOTE: https://stackoverflow.com/questions/32255919/akka-scheduler-stops-on-exception-is-it-expected
    Try {

      logger.info(s"[EmailProcessSendCronService] execute intervalSecs: $cronIntervalInSeconds")

      scheduleAllActiveEmailScheduledForSending() match {

        case Failure(e) =>

          if (e.getMessage.contains("No active")) {
            logger.warn(s"[EmailProcessSendCronService] ${e.getMessage}")
          } else {
            logger.error(s"[EmailProcessSendCronService] error: ${Helpers.getStackTraceAsString(e)}")
          }

        case Success(_) =>
          logger.info(s"[EmailProcessSendCronService] success")

      }
    } match {

      case Failure(e) =>
        logger.error(s"[EmailProcessSendCronService] final FATAL error: ${Helpers.getStackTraceAsString(e)}")

      case Success(_) =>
        logger.info(s"[EmailProcessSendCronService] final success")

    }

  }


  def scheduleAllActiveEmailScheduledForSending() = Try {

    EmailScheduled.getAllEmailScheduledReadyForSending() match {

      case Failure(e) =>

        throw new Exception(s"[EmailProcessSendCronService] getAllActiveEmailScheduledForSending error: ${Helpers.getStackTraceAsString(e)}")


      case Success(emailScheduled) =>

        logger.info(s"emailAccounts")

        if (emailScheduled.isEmpty) {

          throw new Exception("[EmailProcessSendCronService] getAllActiveEmailScheduledForSending No active email scheduled")

        } else {

          logger.info(s"[EmailProcessSendCronService] getAllActiveEmailScheduledForSending found: ${emailScheduled.mkString(", ")}")

          emailScheduled.foreach(esId => {

            logger.info(s"[EmailProcessSendCronService] publishing to rabbimq MQEmail ${esId}")
            MQEmail.publish(MQEmailMessage(emailScheduledId = esId))

          })
        }

    }
  }

}
