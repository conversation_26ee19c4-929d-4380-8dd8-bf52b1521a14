package utils.cronjobs

import akka.actor.ActorSystem
import api.dao.EmailHealthCheckDAO
import api.models.AccountDB
import play.api.Logger
import play.api.libs.ws.ahc.AhcWSClient
import utils.mq.{MQEmailHealthCheck, MQEmailHealthCheckMessage, MQResetSRCredits, MQResetSRCreditsMessage}
import utils.{Helpers, StringUtils}

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._
import scala.util.{Failure, Success, Try}


object OneMinutelyCronService {

  def start()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: AhcWSClient) = {

    Logger(s"[OneMinutelyCronService]").info("start schedule")

    val interval = 1 * 60 * 1000.milliseconds

    system.scheduler.scheduleWithFixedDelay(60000.milliseconds, interval)(() => execute())

  }


  def execute() = {
    val logRequestId = s"${StringUtils.genLogTraceId} [OneMinutelyCronService] "

    Logger(s"$logRequestId").info("execute")


    Try {

      Logger(s"[OneMinutelyCronService] ").info("execute")

      scheduleAccountsToResetSRCredits() match {

        case Failure(e) =>

          if (e.getMessage.contains("No active")) {
            Logger(s"[OneMinutelyCronService] ").warn(s"${e.getMessage}")
          } else {
            Logger(s"[OneMinutelyCronService] ").error(s"error: ${Helpers.getStackTraceAsString(e)}")
          }

        case Success(_) =>
          Logger(s"[OneMinutelyCronService] ").info("success")
      }

      EmailHealthCheckDAO.getPendingEmailHealthChecksForProcessing match {

        case Failure(e) =>

          Logger(s"[OneMinutelyCronService] - getPendingEmailHealthChecksForProcessing").error(s"error: ${Helpers.getStackTraceAsString(e)}")

        case Success(emailHealthCheckRecordIds) =>

          emailHealthCheckRecordIds.foreach { id =>

            MQEmailHealthCheck.publish(
              msg = MQEmailHealthCheckMessage(
                emailHealthCheckRecordId = id,
              ),
            )

          }

      }

    } match {

      case Failure(e) =>
        Logger(s"[OneMinutelyCronService]").error(s"final FATAL error: ${Helpers.getStackTraceAsString(e)}")

      case Success(_) =>
        Logger(s"[OneMinutelyCronService]").info(s"success")

    }
  }




  def scheduleAccountsToResetSRCredits() = Try {

    AccountDB.getAccountsToResetSRCredits() match {

      case Failure(e) =>

        throw new Exception(s"[OneMinutelyCronService] scheduleAccountsToResetSRCREdits error: ${Helpers.getStackTraceAsString(e)}")


      case Success(accounts) =>

        if (accounts.isEmpty) {

          throw new Exception("[OneMinutelyCronService] scheduleAccountsToResetSRCREdits No active accounts for reset sr credits")

        } else {

          Logger(s"[OneMinutelyCronService]").info(s"scheduleAccountsToResetSRCREdits found: ${accounts.mkString(", ")}")

          accounts.foreach(aid => {

            Logger(s"[OneMinutelyCronService]").info(s"publishing to rabbimq MQEmailScheduler ${aid}")

            MQResetSRCredits.publish(MQResetSRCreditsMessage(accounId = aid))

          })
        }

    }
  }
}
