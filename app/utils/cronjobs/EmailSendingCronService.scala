package utils.cronjobs

import api.models._
import utils.mq.{MQEmailSchedulerV2, MQEmailSchedulerMessageV2}
import org.joda.time._
import akka.actor.ActorSystem
import akka.stream.Materializer
import api.AppConfig
import api.services.AccountService
import io.smartreach.esp.utils.email.EmailHelperCommon
import io.sr.llm.bedrock.api.AwsBedrockLlama3Api
import io.sr.llm.bedrock.services.{GeneratedEmail, SrLLMService}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.email.{EmailHelper, SubjectTags}
import utils.{Helpers, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Random, Success, Try}

case class WarmupTopicAndPrompt(
  topic: String,
  prompt: String,
)

case class EmailSubjectAndBodyDetails(
  subjectTemplate: EmailSubjectTemplate,
  bodyWithoutSignatureAndSalutation: String,
)

object EmailSendingCronService extends CronTrait {

  override val cronName: String = "EmailSendingCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronScheduleEmailIntervalInSeconds


  private lazy val awsBedrockLlama3Api = new AwsBedrockLlama3Api

  private lazy val srLLMService = new SrLLMService(awsBedrockLlama3Api = awsBedrockLlama3Api)

  def executeCron(
                   logger: SRLogger
                 )(
    implicit system: ActorSystem,
    ec: ExecutionContext,
    wSClient: AhcWSClient
  ) = {
    // NOTE: https://stackoverflow.com/questions/********/akka-scheduler-stops-on-exception-is-it-expected
    Try {

      logger.info(s"[EmailSendingCronService] execute intervalSecs: $cronIntervalInSeconds")

      scheduleAllActiveEmailAccounts() match {

        case Failure(e) =>
          if (e.getMessage.contains("No active")) {
            logger.warn(s"[EmailSendingCronService] ${e.getMessage}")
          } else {
            logger.error(s"[EmailSendingCronService] error: ${Helpers.getStackTraceAsString(e)}")
          }
        case Success(_) =>
          logger.info(s"[EmailSendingCronService] success")

      }
    } match {

      case Failure(e) =>
        logger.error(s"[EmailSendingCronService] final FATAL error: ${Helpers.getStackTraceAsString(e)}")

      case Success(_) =>
        logger.info(s"[EmailSendingCronService] final success")

    }

  }


  def scheduleAllActiveEmailAccounts() = Try {

    EmailAccount.fetchAllActiveEmailAccountsForSchedulingSend() match {

      case Failure(e) =>

        throw new Exception(s"[EmailSendingCronService] scheduleAllActiveEmailAccounts error: ${Helpers.getStackTraceAsString(e)}")

      case Success(emailAccounts) =>

        if (emailAccounts.isEmpty) {

          throw new Exception("[EmailSendingCronService] scheduleAllActiveEmailAccounts No active email accounts found for initial send")

        } else {

          logger.info(s"[EmailSendingCronService] scheduleAllActiveEmailAccounts found: ${emailAccounts.map(_.id).mkString(", ")}")

          emailAccounts.foreach(ea => {

            // publish emailAccountId for scheduling, regardless of warning
            logger.info(s"[EmailSendingCronService] publishing to rabbimq MQEmailScheduler ${ea.id}")
            MQEmailSchedulerV2.publish(MQEmailSchedulerMessageV2(emailAccountId = ea.id, accountId = ea.account_id))

          })
        }

    }
  }


  private def getEmailSubjectAndBodyDetailsFromDb(
    senderEmailAccount: EmailAccountForCreateEmailSchedule,
    receiverEmailAccount: EmailAccountForCreateEmailSchedule,
  )(
    implicit logger: SRLogger,
  ): Future[EmailSubjectAndBodyDetails] = {

    EmailTemplate.findSubjectForEmailSchedule(emailAccountId = senderEmailAccount.id) match {

      case None =>

        logger.error(
          msg = s"scheduleEmailAccountForSend EmailTemplate.findTemplateForEmailSchedule NONE found emailAccountId: ${senderEmailAccount.id}"
        )

        Future.failed(new Exception("Failed to get email subject from DB."))

      case Some(template) =>

        val email_body = EmailHelper.getEmailBody()

        Future.successful(

          EmailSubjectAndBodyDetails(
            subjectTemplate = template,
            bodyWithoutSignatureAndSalutation = email_body,
          )

        )

    }


  }

  private def generateEmailSubjectAndBodyDetailsWithAI(
    senderEmailAccount: EmailAccountForCreateEmailSchedule,
    receiverEmailAccount: EmailAccountForCreateEmailSchedule,
    senderEmailType: EmailType,
    receiverEmailType: EmailType
  )(
    implicit ec: ExecutionContext,
    logger: SRLogger
  ): Future[EmailSubjectAndBodyDetails] = {

    val warmupTopicAndPrompt = AppConfig.getRandomWarmupTopicAndPrompt

    for {

      generatedEmail: GeneratedEmail <- srLLMService.generateEmailSubjectAndBody(
        emailPurpose = senderEmailType.getEmailPurpose,
        senderPosition = senderEmailType.getEmailAccountOwnerPosition,
        typeOfEmail = senderEmailType.toString,
        topic = warmupTopicAndPrompt.topic,
        additionalPrompt = warmupTopicAndPrompt.prompt,
      )

      cleanedSubject: String = EmailHelper.cleanAIGeneratedEmailSubject(
        subject = generatedEmail.subject
      )

      cleanedGeneratedEmail: GeneratedEmail = generatedEmail.copy(
        subject = cleanedSubject
      )

      template: EmailSubjectTemplate <- Future.fromTry {

        EmailTemplate.createSubjectTemplate(
          generatedEmail = cleanedGeneratedEmail,
          emailType = senderEmailType,
        ).flatMap {

          case None =>

            val errMsg = "Failed to fetch id of created template - Not found"

            logger.logAndSendWarmupSmsAlert(
              msg = s"$errMsg. senderAccountId: ${senderEmailAccount.account_id} subject: ${cleanedGeneratedEmail.subject}"
            )

            Failure(new Exception(errMsg))

          case Some(template) =>

            Success(template)

        }

      }

      _: Long <- Future.fromTry {

        EmailTemplate.createEmailBodyTemplate(
          generatedEmail = cleanedGeneratedEmail,
          emailSubjectTemplateId = template.id,
          isReply = false,
        ).flatMap {

          case None =>

            val errMsg = "Failed to fetch id of created email body template - Not found"

            logger.logAndSendWarmupSmsAlert(
              msg = s"$errMsg. senderAccountId: ${senderEmailAccount.account_id} body: ${cleanedGeneratedEmail.body}"
            )

            Failure(new Exception(errMsg))

          case Some(bodyTemplateId) =>

            Success(bodyTemplateId)

        }

      }

    } yield {

      EmailSubjectAndBodyDetails(
        subjectTemplate = template,
        bodyWithoutSignatureAndSalutation = cleanedGeneratedEmail.body,
      )

    }

  }

  private def generateEmailSubjectAndBody(
    senderEmailAccount: EmailAccountForCreateEmailSchedule,
    receiverEmailAccount: EmailAccountForCreateEmailSchedule,
  )(
    implicit ec: ExecutionContext,
    ws: WSClient,
    logger: SRLogger
  ): Future[EmailSubjectAndBodyDetails] = {

    // When sending email we need to check if send email account support AI warmup.
    val enableAIEmailGenerationForWarmupheroFut = AccountService.getAccountMetadataByAccountId(
      accountId = senderEmailAccount.account_id
    )
      .map(_.enable_ai_email_generation_for_warmuphero)
      .recover { case e =>

        logger.error(
          msg = s"Failed to fetch account metadata. accountId: ${senderEmailAccount.account_id}",
          err = e
        )

        // fallback
        false

      }

    enableAIEmailGenerationForWarmupheroFut.flatMap { enable_ai_email_generation_for_warmuphero =>

      if (!enable_ai_email_generation_for_warmuphero) {

        // TODO: Only get basic templates ??

        getEmailSubjectAndBodyDetailsFromDb(
          senderEmailAccount = senderEmailAccount,
          receiverEmailAccount = receiverEmailAccount,
        )

      } else {

        val senderEmailType: EmailType = senderEmailAccount.email_type

        EmailTemplate.findSubjectAndBodyForAIEmailSchedule(
          emailAccountId = senderEmailAccount.id,
          emailType = senderEmailType,
        ) match {

          case Failure(exception) =>

            logger.logAndSendWarmupSmsAlert(
              msg = s"Failed to findSubjectAndBodyForAIEmailSchedule. emailAccountId: ${senderEmailAccount.account_id} :: senderEmailType: $senderEmailType",
              err = Some(exception),
            )

            generateEmailSubjectAndBodyDetailsWithAI(
              senderEmailAccount = senderEmailAccount,
              receiverEmailAccount = receiverEmailAccount,
              senderEmailType = senderEmailType,
              receiverEmailType = receiverEmailAccount.email_type,
            )

          case Success(None) =>

            // TODO: If this fails should we fallback to DB?

            generateEmailSubjectAndBodyDetailsWithAI(
              senderEmailAccount = senderEmailAccount,
              receiverEmailAccount = receiverEmailAccount,
              senderEmailType = senderEmailType,
              receiverEmailType = receiverEmailAccount.email_type,
            )

          case Success(Some(emailSubjectAndBodyDetailsFromDB)) =>

            Future.successful(emailSubjectAndBodyDetailsFromDB)

        }

      }

    }

  }

  def scheduleEmailAccountForSend(emailAccountId: Long, Logger: SRLogger)(implicit ws: WSClient, ec: ExecutionContext, materializer: Materializer): Future[(Int, Option[DateTime])] = Future {

    Logger.info(s"scheduleEmailAccountForSend started")

    EmailAccount.findForScheduling(id = emailAccountId) match {

      case Failure(e)  =>

        Logger.fatal(s"scheduleEmailAccountForSend EmailAccount.findForScheduling error", err = e)

        Future.successful((0, None))

      case Success(None) =>

        Logger.error(s"scheduleEmailAccountForSend EmailAccount.findForScheduling NONE found")

        Future.successful((0, None))

      case Success(Some(senderEmailAccount)) =>

        EmailAccount.findReceiverEmailAccountForScheduling(email_domain = senderEmailAccount.email_domain, senderEmailAccountId = emailAccountId) match {

          case Failure(e) =>
            Logger.fatal(s"scheduleEmailAccountForSend EmailAccount.findReceiverEmailAccountForScheduling error", err = e)

            Future.successful((0, None))

          case Success(None) =>

            /*
            * NOTE:
            * When receiver email account not found this sender emailAccount will try after 15 minutes for receiverEmailAccount
            * */
            Logger.error(s"scheduleEmailAccountForSend EmailAccount.findReceiverEmailAccountForScheduling NONE found")

            Future.successful((0, None))

          case Success(Some(receiverEmailAccount)) =>

            generateEmailSubjectAndBody(
              senderEmailAccount = senderEmailAccount,
              receiverEmailAccount = receiverEmailAccount,
            )(
              logger = Logger,
              ws = ws,
              ec = ec,
            ).flatMap { emailSubjectAndBodyDetails =>

              val template = emailSubjectAndBodyDetails.subjectTemplate

              val account_id = senderEmailAccount.account_id

              val salutationHtml = EmailHelper.getSalutationHtml(firstName = receiverEmailAccount.first_name)

              val signatureHtml = EmailHelper.getSignatureHtml(signature = senderEmailAccount.signature, firstName = senderEmailAccount.first_name)

              val htmlBody = EmailHelper.makeHTMLBody(
                baseBody = emailSubjectAndBodyDetails.bodyWithoutSignatureAndSalutation,
                salutation = salutationHtml,
                signature = signatureHtml
              )

              val textBody = EmailHelperCommon.getTextBodyFromHtmlBody(htmlBody)

              val emailsToBeScheduled = EmailToBeScheduled(
                sender_email_account_id = senderEmailAccount.id,
                from_email = senderEmailAccount.email,
                from_name = senderEmailAccount.sender_name,
                receiver_email_account_id = receiverEmailAccount.id,
                to_email = receiverEmailAccount.email,
                to_name = receiverEmailAccount.sender_name,
                account_id = account_id,
                htmlBody = htmlBody,
                textBody = textBody,
                subject = s"${template.subject} | ${SubjectTags.SUFFIX}",
                template_id = template.id,
                is_reply = false,
                warmup_email_account_id = senderEmailAccount.id,
                email_thread_id = EmailHelper.getEmailThreadId(emailAccountId = senderEmailAccount.id),
                in_reply_to_header = None,
                references_header = None
              )

              val savedEmailScheduled = EmailScheduled.saveEmailsToBeScheduled(
                emailsToBeScheduled = emailsToBeScheduled,
                Logger = Logger
              ).get


              val latestEmailScheduledAt = Some(DateTime.now())


              Try {
                EmailAccount._updateLastScheduled(emailAccountId = emailAccountId, latestEmailScheduledAt = latestEmailScheduledAt,
                  Logger = Logger
                )

              } match {

                case Failure(e) =>
                  Logger.fatal(s"${Helpers.getStackTraceAsString(e)}")
                  Future.successful((savedEmailScheduled.length, latestEmailScheduledAt))


                case Success(_) =>
                  Logger.info(s"DONE")

                  Future.successful((savedEmailScheduled.length, latestEmailScheduledAt))

              }

            }
        }
    }

  }.flatMap(identity)

}
