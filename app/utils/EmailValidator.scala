package utils

import api.AppConfig
import org.apache.commons.validator
import play.api.Logging
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>sSuc<PERSON>, <PERSON><PERSON>, Reads}
import play.api.libs.ws.WSClient

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class EmailSplit(
  fullname: String,
  nameWithoutAlias: String,
  domain: String
)

case class MBLApiResponse(
  email: String,
  did_you_mean: String,
  format_valid: <PERSON><PERSON><PERSON>,
  user: Option[String],
  domain: Option[String],
  mx_found: Option[<PERSON>olean],
  smtp_check: <PERSON><PERSON><PERSON>,
  catch_all: Option[<PERSON><PERSON><PERSON>],
  role: Option[<PERSON>olean],
  disposable: Option[<PERSON><PERSON><PERSON>],
  free: Option[<PERSON>olean],
  score: Float
)

object MBLApiResponse {
  implicit val reads: Reads[MBLApiResponse] = Json.reads[MBLApiResponse]
}

object EmailValidator extends Logging{

  def validateEmail(email: String): Boolean = validator.routines.EmailValidator.getInstance().isValid(email)

  def validateDomain(domain: String): Boolean = validator.routines.DomainValidator.getInstance().isValid(domain)


  def getLowercasedNameAndDomainFromEmail(email: String): (String, String) = {

    Try {
      val splits = email.trim.toLowerCase.split('@')
      (splits(0), splits(1))
    } match {

      case Failure(e) =>
        logger.error(s"FATAL getLowercasedNameAndDomainFromEmail: ${email} : ${Helpers.getStackTraceAsString(e)}")

        ("", "")

      case Success(value) =>
        value

    }

  }

  def getNameDomainAndAliasFromEmailV2(email: String): EmailSplit = {

    val em = email.toLowerCase.trim

    val at = em.lastIndexOf("@")
    val fullname = em.substring(0, at)
    val domain = em.substring(at + 1)

    val aliasFrom = fullname.indexOf("+")
    val nameWithoutAlias = if(aliasFrom > -1) fullname.substring(0, aliasFrom) else fullname


    EmailSplit(
      fullname = fullname,
      nameWithoutAlias = nameWithoutAlias,
      domain = domain
    )

  }

}
