package utils

import scala.util.Try

object ParseUtils {

  def parseInt(s: String): Option[Int] = if (s.isEmpty) None else Try(s.toInt).toOption

  def parseLong(s: String): Option[Long] = if (s.isEmpty) None else Try(s.toLong).toOption

  def parseBool(s: String): Option[Boolean] = if (s.trim.isEmpty) None else if (s == "true") Some(true) else Some(false)

  // def parseHexToLong(hexString: String): Option[Long] = {
  //    Try(java.lang.Long.valueOf(hexString, 16)).toOption
  //  }


  def parseHexToLongStr(hexString: String): Option[String] = {
    Try(java.lang.Long.valueOf(hexString, 16)).toOption.map(_.toString)
  }

  def parseLongStrToHex(longString: String): Option[String] = {
    parseLong(s = longString).flatMap(l => Try(l.toHexString).toOption)
  }

}
