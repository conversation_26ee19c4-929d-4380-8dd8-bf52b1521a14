package utils

import java.io.{PrintWriter, StringWriter}
import api.AppConfig
import api.models.{Account, AccountProfileInfo, EmailAccountForm, EmailTestForm}
import play.api.Logger
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


object Helpers {

  def getAccountName(profile: AccountProfileInfo): String = {

    val first = profile.first_name
    val last = profile.last_name

    if (first.nonEmpty && last.nonEmpty) s"$first $last"
    else if (first.nonEmpty) first
    else if (last.nonEmpty) last
    else ""

  }

  // REF: https://stackoverflow.com/a/********/5290001
  def seqTryToTrySeq[A](xs: Seq[Try[A]]) =
    Try(xs.map(_.get))


  def toInt(s: String): Option[Int] = {
    try {
      Some(s.toInt)
    } catch {
      case e: Exception => None
    }
  }

  def getStackTraceAsString(t: Throwable) = {
    val sw = new StringWriter
    t.printStackTrace(new PrintWriter(sw))
    sw.toString
  }


  // REF: https://stackoverflow.com/a/********
  // shuffle a list / seq
  def shuffleList[T](list: Seq[T]): Seq[T] = {

    scala.util.Random.shuffle(list)

  }

  def getHourLabels(hour: Int): Try[(String, String)] = Try {

    if (hour > 23 || hour < 0) {

      throw new Exception(s"[FATAL] getHourLabels Invalid hour $hour")

    } else {

      val labels = Seq("12am", "1am", "2am", "3am", "4am", "5am", "6am", "7am", "8am", "9am", "10am", "11am", "12pm", "1pm", "2pm", "3pm", "4pm", "5pm", "6pm", "7pm", "8pm", "9pm", "10pm", "11pm", "HELLO")

      val from = labels(hour)
      val to = if(hour >= 23) labels(0) else labels(hour + 1)

      (from, s"$from - $to")

    }
  }

  def getPercent(value: Int, total: Int): Int = {
    Math.ceil(Math.ceil(value * 100) / total).toInt
  }


  def getPublicIPOfCurrentServer()(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[String] = {
    ws.url("https://checkip.amazonaws.com/")
      .get()
      .map(res => {

        val publicIP = res.body.trim
        Logger.info(s"getPublicIPOfCurrentServer Found: $publicIP")

        publicIP

      })
      .recover { case e =>

        Logger.info(s"getPublicIPOfCurrentServer [FATAL]: ${Helpers.getStackTraceAsString(e)}")

        throw e
      }

  }

  def getClientIPFromGCPLoadbalancer(
    request: play.api.mvc.Request[Any]
  )(
    implicit logger: SRLogger
  ): Option[String] = {

    Try {

      // REF: https://cloud.google.com/load-balancing/docs/https#x-forwarded-for_header
      val xForwardedHeader = request.headers.get("X-Forwarded-For")

      if (
        xForwardedHeader.nonEmpty
          && xForwardedHeader.get.nonEmpty
          && xForwardedHeader.get.split(",").nonEmpty
      ) {
        xForwardedHeader.get.split(",").headOption
      } else None

    } match {
      case Failure(e) =>
        logger.error(s"FATAL getClientIPFromGCPLoadbalancer (returning None): ${LogHelpers.getStackTraceAsString(e)} ")
        None

      case Success(clientIP) =>
        clientIP
    }

  }

  def getClientIPFromGCPLoadbalancerFallbackToRemoteAddr(
    request: play.api.mvc.Request[Any]
  )(
    implicit logger: SRLogger
  ): String = {

    Try {

      val ipFromGCP = getClientIPFromGCPLoadbalancer(
        request = request
      )

      if (ipFromGCP.isDefined) {
        ipFromGCP
      } else {
        Option(request.remoteAddress)
      }

    } match {
      case Failure(e) =>
        logger.fatal(s"FATAL ERROR getClientIPFromGCPLoadbalancerFallbackToRemoteAddr (returning 0.0.0.0)", err = e)
        "0.0.0.0"

      case Success(None) =>
        logger.fatal(s"FATAL None getClientIPFromGCPLoadbalancerFallbackToRemoteAddr (returning 0.0.0.0): ${request.remoteAddress}")
        "0.0.0.0"

      case Success(Some(clientIP)) =>
        clientIP
    }

  }

  // NOTE: https://stackoverflow.com/a/20415056
  // this helper is used do sequential execution of Futures in scala
  def seqFutures[T, U](items: IterableOnce[T])(yourfunction: T => Future[U])(implicit ec: ExecutionContext): Future[List[U]] = {
    items.iterator.foldLeft(Future.successful[List[U]](Nil)) {
      (f, item) => f.flatMap {
        x => yourfunction(item).map(_ :: x)
      }
    } map (_.reverse)
  }

  def getSenderName(a: EmailTestForm): String = {

    if (a.first_name.trim.nonEmpty && a.last_name.trim.nonEmpty) s"${a.first_name.trim} ${a.last_name.trim}"
    else if (a.first_name.trim.nonEmpty) a.first_name.trim
    else ""

  }

  def makeEmailAccountUrl(emailAccountId: Long) = {
    s"${AppConfig.emailAccountUrl}/$emailAccountId"
  }
}

