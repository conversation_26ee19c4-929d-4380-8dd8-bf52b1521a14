package utils

import org.apache.commons.codec.binary.{Base32, Base64}

import scala.util.Random

object StringUtils {


  // NOTE: check tests/StringUtilsSpec.scala for documentation about this method
  // TemplateService.render throws error if we have columns that have names with spaces in them, e.g. "follower Count": ideally this should be "follower_count"
  // allow only alphanumeric: regex: http://stackoverflow.com/a/1805527
  // test case:   genColumnName("132   32hfdaddadfds 23dfdsf121") = _32hfdaddadfds_23dfdsf121
  def genColumnName(userInputColName: String): String = {
    userInputColName.trim.toLowerCase.replaceAll("[^A-Za-z0-9 _]", "").split(" ").filter(part => part != "").mkString("_").replaceAll("^\\d+", "").trim
  }



  def genApiKey: String = Random.alphanumeric.take(32).mkString("")


  // this is used to generate message id
  def genRandomAlphaNumericString30Chars: String = {
    Random.alphanumeric.take(30).mkString
  }

  // this is used to generate trace id for logging purpose
  def genLogTraceId: String = {
    s"req_${Random.alphanumeric.take(30).mkString}"
  }


  def base64EncodeString(str: String): String = Base64.encodeBase64URLSafeString(str.getBytes("UTF-8"))

  def base64DecodeString(str: String): String = new String(Base64.decodeBase64(str))



  // Base32 provides case-insensitivity for urls
  def base32EncodeURIString(str: String): String = new Base32().encodeAsString(str.getBytes("UTF-8"))

  // even if url has lowercase chars, we convert them to uppercase before decoding for case-insensitivity
  def base32DecodeURIString(str: String): String = new String(new Base32().decode(str.toUpperCase()))

}
