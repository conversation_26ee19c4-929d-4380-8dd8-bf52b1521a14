package utils

import api.AppConfig
import utils.ISRLogger

final class SRLogger(val logRequestId: String) extends ISRLogger {

  override val isProd: Boolean = AppConfig.isProd

  private case object WARMUPHERO_SMS_ALERTS extends SrSmsAlert {

    override val smsAlertKeyword: String = "WARMUPHERO_SMS_ALERTS"

  }

  final def logAndSendWarmupSmsAlert(
    msg: String,
    err: Option[Throwable] = None
  ): Unit = {

    logAndSendSmsAlert(
      smsAlert = WARMUPHERO_SMS_ALERTS,
      msg = msg,
      err = err,
    )

  }

  // append additional info to logRequestId and return new instance of SRLogger
  def appendLogRequestId(appendLogReqId: String): SRLogger = {
    new SRLogger(logRequestId = s"$appendLogReqId ::: $logRequestId")
  }

}
