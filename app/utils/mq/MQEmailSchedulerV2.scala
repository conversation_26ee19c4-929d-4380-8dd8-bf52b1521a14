package utils.mq

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import api.models.{AccountId, EmailAccountId}
import api.services.EmailHealthCheckService
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import utils.cronjobs.EmailSendingCronService
import utils.{Help<PERSON>, SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class MQEmailSchedulerMessageV2(emailAccountId: EmailAccountId, accountId: AccountId)

object MQEmailSchedulerV2 extends MQService[MQEmailSchedulerMessageV2] {


  implicit val system: ActorSystem = ActorSystem()

  val queueBaseName: String = MQConfig.emailAccountSchedulerForSendingQueueBaseNameV2
  val prefetchCount: Int = MQConfig.emailSchedulerPrefetchCountV2

  def publish(msg: MQEmailSchedulerMessageV2) = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: MQEmailSchedulerMessageV2)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()

    val logBase = s"( ${StringUtils.genLogTraceId} ) :: [MQEmailSchedulerV2] scheduleEmailAccountForSend ea_${msg.emailAccountId} :: "

    implicit val Logger: SRLogger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: ${msg}: started at $startTime")

    val canSendEmailFut = EmailHealthCheckService.checkEmailHealthAndCanSendEmail(
      emailAccountId = msg.emailAccountId,
      accountId = msg.accountId,
    ).recover { err =>

      // If creating an email health check fails, we will still send the email for now.
      // We will remove this, once we know the flow is working correctly.

      Logger.error(
        msg = s"Failed to checkEmailHealthAndCanSendEmail. emailAccountId: ${msg.emailAccountId} :: accountId: ${msg.accountId}",
        err = err,
      )

      true

    }

    canSendEmailFut.flatMap { canSend =>

      if (canSend) {

        // TODO: FIXME - VALUE CLASS
        EmailSendingCronService.scheduleEmailAccountForSend(emailAccountId = msg.emailAccountId.id, Logger = Logger)

      } else {

        Future.successful(0, None)

      }

    }.map { res =>

      val endTime = DateTime.now()
      val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

      Logger.info(s"scheduled:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

      res._1

    }.recover { case e =>

      val endTime = DateTime.now()
      val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

      Logger.error(s"failed:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: ${Helpers.getStackTraceAsString(e)}")

      0

    }

  }

}
