package utils.mq

import com.typesafe.config.{Config, ConfigFactory}
import org.apache.commons.validator.routines.InetAddressValidator
import play.api.Logger

import scala.util.{Failure, Success, Try}

object MQConfig {
  val config: Config = ConfigFactory.load()

  val host: String = config.getString("rabbitmq.host")
  val username: String = config.getString("rabbitmq.username")
  val password: String = config.getString("rabbitmq.password")
  val virtualHost: String = config.getString("rabbitmq.virtualHost")

  val prefix: String = config.getString("rabbitmq.prefix") + "."

  val emailAccountSchedulerForSendingQueueBaseName: String = "emailAccountSchedulerForSending"
  val emailSchedulerPrefetchCount: Int = 10

  val emailAccountSchedulerForSendingQueueBaseNameV2: String = "emailAccountSchedulerForSendingV2"
  val emailSchedulerPrefetchCountV2: Int = 10

  val emailHealthCheckQueueBaseName: String = "emailHealthCheck"
  val emailHealthCheckPrefetchCount: Int = 5

  val emailAccountSchedulerForReplyQueueBaseName: String = "emailAccountSchedulerForReply"
  val emailSchedulerForReplyPrefetchCount: Int = 10

  val emailAccountSchedulerForLandingCheckQueueBaseName: String = "emailAccountSchedulerForLandingCheck"
  val emailSchedulerForForLandingCheckPrefetchCount: Int = 10

  val emailProcessSendQueueBaseName: String = "emailProcessSend"
  // 19 Feb 2025 - reducing the prefect count to avoid race condition - which might be causing over sending
  val emailProcessSendPrefetchCount: Int = 2

  val resetSRCreditsQueueBaseName: String = "resetSRCredits"
  val resetSRCreditsPrefetchCount: Int = 10

  val exchangeType: String = config.getString("rabbitmq.exchangeType")
  val durable: Boolean = config.getBoolean("rabbitmq.durable")
  val autoAck: Boolean = config.getBoolean("rabbitmq.autoAck")
  val prefetchCount: Int = config.getInt("rabbitmq.prefetchCount")
  val connectionTimeout: Int = config.getInt("rabbitmq.connectionTimeout")
}
