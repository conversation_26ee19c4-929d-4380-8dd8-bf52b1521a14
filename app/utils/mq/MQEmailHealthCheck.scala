package utils.mq

import akka.actor.ActorSystem
import api.services.{EmailHealthCheckRecordId, EmailHealthCheckService}
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import utils.{<PERSON><PERSON>, <PERSON>Logger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class MQEmailHealthCheckMessage(emailHealthCheckRecordId: EmailHealthCheckRecordId)

object MQEmailHealthCheck extends MQService[MQEmailHealthCheckMessage] {

  implicit val system: ActorSystem = ActorSystem()

  val queueBaseName: String = MQConfig.emailHealthCheckQueueBaseName
  val prefetchCount: Int = MQConfig.emailHealthCheckPrefetchCount

  def publish(msg: MQEmailHealthCheckMessage) = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: MQEmailHealthCheckMessage)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()

    val logBase = s"( ${StringUtils.genLogTraceId} ) :: [MQEmailHealthCheck] schedule for health check ${msg.emailHealthCheckRecordId} :: "

    implicit val Logger: SRLogger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: $msg: started at $startTime")

    EmailHealthCheckService.emailSettingHealthCheckTrack(
      emailHealthCheckRecordId = msg.emailHealthCheckRecordId,
    ).map { res =>

      val endTime = DateTime.now()
      val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

      Logger.info(s"checked:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

      1

    }.recover { case e =>

      val endTime = DateTime.now()
      val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

      Logger.error(s"failed:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: ${Helpers.getStackTraceAsString(e)}")

      0

    }

  }

}
