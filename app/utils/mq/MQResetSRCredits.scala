package utils.mq

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import api.models.AccountDB
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import utils.{Help<PERSON>, SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class MQResetSRCreditsMessage(accounId: Long)

object MQResetSRCredits extends MQService[MQResetSRCreditsMessage] {


  implicit val system: ActorSystem = ActorSystem()

  val queueBaseName: String = MQConfig.resetSRCreditsQueueBaseName
  val prefetchCount: Int = MQConfig.resetSRCreditsPrefetchCount

  def publish(msg: MQResetSRCreditsMessage) = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: MQResetSRCreditsMessage)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()

    val logBase = s"( ${StringUtils.genLogTraceId} ) :: [MQResetSRCredits] MQResetSRCredits a_${msg.accounId} :: "

    val Logger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: ${msg}: started at $startTime")

    AccountDB.resetSRCreditsDailyFromCron(accountId = msg.accounId, Logger = Logger)
      .map { res =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

        Logger.info(s"scheduled:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

        if(res.isDefined) {

          res

        } else {

          Logger.error(s"Account not found accountId: ${msg.accounId} :: failed:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken)}")

        }

      }
      .recover { case e =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds

        Logger.error(s"failed:: ${msg}: took from $startTime to $endTime (timetaken: $timeTaken) :: ${Helpers.getStackTraceAsString(e)}")

        Some(0)
      }


  }

}