package utils.mq.email

import akka.actor.ActorSystem
import api.emails.EmailToBeSent
import api.models.EmailScheduled
import play.api.libs.ws.WSClient
import utils.email.{EmailSendingService, SendEmailResponse}
import utils.{Help<PERSON>, SRLogger, StringUtils}
import utils.mq.{MQConfig, MQService}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

object MQEmail extends MQService[MQEmailMessage] {

  val prefetchCount: Int = MQConfig.emailProcessSendPrefetchCount

  def publish(message: MQEmailMessage) = {
    val queueBaseName = MQConfig.emailProcessSendQueueBaseName

    publishMsg(message = message, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    val queueBaseName = MQConfig.emailProcessSendQueueBaseName

    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: MQEmailMessage)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[SendEmailResponse] = {
    val _logRequestId = msg.logRequestId.getOrElse(StringUtils.genLogTraceId)
    implicit val Logger = new SRLogger(logRequestId = _logRequestId)

    Logger.info(s"[MQEmail] processMessage: msgId: ${msg.emailScheduledId}")
    EmailSendingService.processSendEmailRequest(
      msg = msg
    ).map(res => {

        Try {

          EmailScheduled.updateIsScheduledForSending(emailScheduledId = msg.emailScheduledId, Logger = Logger)

        } match  {

          case scala.util.Failure(e) =>
            Logger.fatal(s"${Helpers.getStackTraceAsString(e)}")
            res

          case scala.util.Success(_) =>
            Logger.info(s"DONE")
            res

        }

      })
  }

}