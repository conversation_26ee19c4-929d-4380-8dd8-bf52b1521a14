package utils.mq

import com.rabbitmq.client.{Channel, Connection, ConnectionFactory}
import play.api.{ Logging}

import scala.util.Try

object MQConnection  extends Logging{

  val factory: ConnectionFactory = new ConnectionFactory()

  factory.setUri(MQConfig.host)
  factory.setUsername(MQConfig.username)
  factory.setPassword(MQConfig.password)
  factory.setVirtualHost(MQConfig.virtualHost)
  factory.setAutomaticRecoveryEnabled(true)
  factory.setNetworkRecoveryInterval(10000)
  factory.setConnectionTimeout(MQConfig.connectionTimeout)


  // one TCP connection will be shared for all channels
  // NOTE: this can throw an exception
  var conn: Option[Connection] = None


  def getChannel(queueName: String, exchangeName: String, routingKey: String, prefetchCount: Int): Try[Channel] = Try {

    if (conn.isEmpty || !conn.get.isOpen) {
      val newConn = factory.newConnection()
      conn = Some(newConn)
    }

    logger.info(s"[MQConnection] Creating channel with queue $queueName, exchangeName - $exchangeName, routingKey - $routingKey")

    val channel: Channel = conn.get.createChannel()
    channel.exchangeDeclare(
      exchangeName,
      MQConfig.exchangeType,
      MQConfig.durable
    )
    channel.queueDeclare(queueName, MQConfig.durable, false, false, null).getQueue
    channel.queueBind(queueName, exchangeName, routingKey)
    channel.basicQos(prefetchCount)
    channel
  }

}
