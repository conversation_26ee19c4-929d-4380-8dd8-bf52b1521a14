package utils.mq

import java.io.{PrintWriter, StringWriter}
import akka.actor.ActorSystem
import com.rabbitmq.client._
import org.apache.commons.lang3.SerializationUtils
import play.api.{Logging}
import play.api.libs.ws.WSClient
import utils.Helpers

import scala.concurrent.{ExecutionContext, Future, blocking}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

case class MQDoNotNackException(message: String = "", cause: Throwable = None.orNull)
  extends Exception(message, cause)

trait MQService[T <: Serializable]  extends Logging {

  // PUBLISHER //

  // (queueName, Channel)
  private var channels: Seq[(String, Channel)] = Seq()


  protected final def publishMsg(
                                  message: T,
                                  queueBaseName: String,
                                  prefetchCount: Int
                                ): Try[Unit] = blocking {
    Try {


      val prefix = MQConfig.prefix

      val queueName = s"$prefix$queueBaseName"
      val exchangeName = s"$prefix${queueBaseName}ExchangeName"
      val routingKey = s"$prefix${queueBaseName}RoutingKey"
      // val consumerTag = s"$prefix${queueBaseName}ConsumerTag"

      logger.info(s"[MQService] publish 1: with $queueName :: $message")


      logger.info(s"[MQService] publish 2: with $queueName :: ${SerializationUtils.serialize(message)}")
      var channel: Option[Channel] = channels.find(ch => ch._1 == queueName).map(_._2)

      if (channel.isEmpty) {

        MQConnection.getChannel(queueName, exchangeName, routingKey, prefetchCount = prefetchCount) match {

          case Failure(e) =>
            logger.error(s"MQService FATAL error while creating channel: $queueName ::: ${Helpers.getStackTraceAsString(e)}")
            throw e

          case Success(ch) =>

            channel = Some(ch)

            channels = channels ++ Seq((queueName, ch))

        }

      }

      if (channel.isEmpty) {

        logger.error(s"MQService fatal no channel: $queueName")

      } else {

        channel.get.basicPublish(
          exchangeName,
          routingKey,
          true,
          MessageProperties.PERSISTENT_TEXT_PLAIN,
          SerializationUtils.serialize(message)
        )

      }

    }
  }

  // CONSUMER //

  protected final def startMsgConsumer(
                                        queueBaseName: String,
                                        prefetchCount: Int
                                      )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {

    val prefix = MQConfig.prefix

    val queueName = s"$prefix$queueBaseName"
    val exchangeName = s"$prefix${queueBaseName}ExchangeName"
    val routingKey = s"$prefix${queueBaseName}RoutingKey"
    val consumerTag = s"$prefix${queueBaseName}ConsumerTag"

    logger.info(s"[MQService] startConsumer: $queueName - $exchangeName - $routingKey - $prefetchCount")

    MQConnection.getChannel(queueName, exchangeName, routingKey, prefetchCount = prefetchCount).flatMap {
      channel =>

        val consumerCallback = getConsumerCallback(
          channel = channel,
          queueName = queueName,
          queueBaseName = queueBaseName,
          channelConsumerTag = consumerTag,
          prefetchCount = prefetchCount,
          processMessage = processMessage)

        basicConsume(channel, queueName, consumerTag, consumerCallback).map {
          consumerTag =>

            logger.info(s"[MQService] startConsumer started: $queueName")

            consumerTag

        }

    }


  }

  def processMessage(msg: T)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any]


  private final def basicConsume(channel: Channel, queueName: String, consumerTag: String, consumerCallback: DefaultConsumer) = Try {
    channel.basicConsume(queueName, MQConfig.autoAck, consumerTag, consumerCallback)
  }

  private final def getStackTraceAsString(t: Throwable) = {
    val sw = new StringWriter
    t.printStackTrace(new PrintWriter(sw))
    sw.toString
  }

  private final def getConsumerCallback(

                                         channel: Channel,
                                         queueBaseName: String,
                                         queueName: String,
                                         channelConsumerTag: String,
                                         prefetchCount: Int,
                                         processMessage: (T) => Future[Any]

                                       )(implicit ws: WSClient, ec: ExecutionContext) = new DefaultConsumer(channel: Channel) {

    override def handleDelivery(consumerTag: String, envelope: Envelope, properties: AMQP.BasicProperties, body: Array[Byte]) = Try {

      logger.info(s"[MQService] handleDelivery: queueName: $queueName , consumerTag: $consumerTag")

      val deliveryTag: Long = envelope.getDeliveryTag
      Try { SerializationUtils.deserialize[T](body) } match {
        case Failure(e) =>
          logger.error(s"[MQService] [FATAL] handleDelivery SerializationUtils.deserialize ERROR: queueName $queueName, deliveryTag: $deliveryTag :: ${Helpers.getStackTraceAsString(e)}")

          // DO NOT REQUE
          channel.basicNack(deliveryTag, false, false)


        case Success(mqMsg) =>

          if (channelConsumerTag != consumerTag) {

            logger.error(s"[MQService] [NO REQUE] handleDelivery: queueName $queueName : incorrect consumerTag: $channelConsumerTag != $consumerTag ;; msg: $mqMsg, deliveryTag: $deliveryTag")

            // DO NOT REQUE
            channel.basicNack(deliveryTag, false, false)

            //      throw new Exception(s"[MQService] $queueName : handleDelivery: channelConsumerTag ($channelConsumerTag) is not equal to consumerTag ($consumerTag)")

          } else {

            //        Logger.info(s"[MQService] $queueName : correct consumerTag: $channelConsumerTag == $consumerTag")


            logger.info(s"[MQService] handleDelivery: queueName: $queueName : Received message : $mqMsg")

            processMessage(mqMsg)
              .map {
                _ =>

                  logger.info(s"[MQService] handleDelivery processMessage success: $queueName: Calling ack")

                  channel.basicAck(deliveryTag, false)
              }

              .recover {

                case c: MQDoNotNackException =>

                  logger.error(s"[MQDoNotNackException] [MQService] handleDelivery: processMessage error: $queueName: Calling ack: ${c.getMessage} ::: " +
                    s"${if (c.getMessage.contains("error email is now paused")) "" else Helpers.getStackTraceAsString(c)}")

                  channel.basicAck(deliveryTag, false)


                case NonFatal(e) =>

                  logger.error(s"[MQService] handleDelivery: processMessage error: $queueName: Calling nack: ${e.getMessage} ::: ${getStackTraceAsString(e)} ::: message: $mqMsg")

                  //channel.basicNack(deliveryTag, false, true)

                  // NOTE: reque at the end of the queue
                  channel.basicAck(deliveryTag, false)

                    //TODO in initial case it going to infinite when it has only two items ion queue need to discuss about this
//                  publishMsg(mqMsg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)

              }

          }

      }


    }
  }



}
