


import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import api.controllers.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EmailAccountController, IndexController, OAuthController, ReportController}
import api.middleware.{LoggingAction, SmartReachAuthMiddleware}
import api.services.CacheService
import com.softwaremill.macwire._
import controllers.Assets
import controllers.AssetsComponents
import play.api.cache.redis.RedisCacheComponents
import play.api.cache.redis.impl.RedisCaches
import play.api.mvc.{BodyParsers, ControllerComponents, PlayBodyParsers}
import router.Routes
//import controllers.Assets
import play.api.ApplicationLoader.Context
import play.api._
import play.api.db.evolutions.{DynamicEvolutions, EvolutionsComponents}
import play.api.db.{DBComponents, HikariCPComponents}
import play.api.i18n.I18nComponents
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.api.routing.Router
import scalikejdbc._
import scalikejdbc.config.DBs
import scala.concurrent.{ExecutionContext, Future}


/**
  * REF:
  *
  * macwire:
  * 1. http://di-in-scala.github.io/
  * 2. https://github.com/playframework/play-macwire-di
  * 3. https://groups.google.com/forum/#!topic/play-framework/GTh3wsc1wNQ
  *
  * evolutions and scalikejdbc:
  * 4. https://github.com/denisftw/modern-web-scala/blob/f4131e4c37572b5672f6b9b70e83c462294a91f1/app/AppLoader.scala
  * 5. http://scalikejdbc.org/
  */

/**
  * Application loader that wires up the application dependencies using Macwire
  */
class AppApplicationLoader extends ApplicationLoader {

  def load(context: Context): Application = {

    // set up logger
    LoggerConfigurator(context.environment.classLoader).foreach {
      _.configure(context.environment)
    }

    new AppComponents(context).application

  }

}

trait CacheModule extends RedisCacheComponents {

  // REF: https://github.com/KarelCemus/play-redis/blob/master/doc/10-integration.md#compile-time-dependency-injection
  val playCache: RedisCaches = cacheApi("play")

  // expose `play.api.cache.redis.CacheAsyncApi`
  val asynchronousRedis = playCache.async

  val executionContext: ExecutionContext

  val cacheService: CacheService = wire[CacheService]

}

trait LoggingModule {

  implicit val actorSystemForLogging: ActorSystem = ActorSystem()
  val bodyParser: BodyParsers.Default = new BodyParsers.Default()

}

class AppComponents(context: Context) extends BuiltInComponentsFromContext(context)
  with AhcWSComponents
  with EvolutionsComponents
  with DBComponents
  with HikariCPComponents
  with I18nComponents
  with AssetsComponents
  with FiltersModule
  with EmailAccountModule
  with ReportsModule
  with IndexModule
  with AuthModule
  with OAuthModule
  with Logging {

  // lazy val assets: Assets = wire[Assets]

  lazy val router: Router = {
    // add the prefix string in local scope for the Routes constructor
    val prefix: String = "/"
    wire[Routes]

  }


  override lazy val dynamicEvolutions = new DynamicEvolutions

  override lazy val httpFilters: Seq[EssentialFilter] = filters.filters


  private def isTest = environment.mode == Mode.Test

  //  private def isDev = environment.mode == Mode.Dev


  applicationLifecycle.addStopHook { () =>
    logger.info("The app is about to stop")
    DBs.closeAll()
    Future.successful(())
  }


  val onStart = {
    logger.info("The app is about to start")

    DBs.setupAll()

    if (isTest) {

      logger.info("Test Mode: drop existing tables")

      implicit val session: AutoSession.type = AutoSession
      DB.localTx { implicit session =>
        sql"DROP SCHEMA public CASCADE;".execute().apply()
        sql"CREATE SCHEMA public;".execute().apply()
      }


      logger.info("Test Mode: apply evolutions")

      applicationEvolutions

    } else {

      applicationEvolutions

    }


  }


}

trait AuthModule extends LoggingModule with CacheModule {
//  val cacheService: CacheService

  implicit val controllerComponents: ControllerComponents
  implicit val authExecutionContext: ExecutionContext = controllerComponents.executionContext
  implicit val authLoggingAction: LoggingAction = wire[LoggingAction]
  implicit val wsClient: WSClient

  implicit val smartreachAuthMiddleware: SmartReachAuthMiddleware = wire[SmartReachAuthMiddleware]

  lazy val authController = wire[AuthController]
}

trait OAuthModule extends LoggingModule with CacheModule {
  //  val cacheService: CacheService

  implicit val controllerComponents: ControllerComponents
  implicit val oauthExecutionContext: ExecutionContext = controllerComponents.executionContext
  implicit val oauthLoggingAction: LoggingAction = wire[LoggingAction]
  implicit val wsClient: WSClient

  implicit val oauthSmartreachAuthMiddleware: SmartReachAuthMiddleware = wire[SmartReachAuthMiddleware]

  implicit val actorSystemForOAuthController: ActorSystem = ActorSystem()

  lazy val oauthController = wire[OAuthController]
}

trait EmailAccountModule extends LoggingModule with CacheModule {

  implicit val wsClient: WSClient

  implicit val cacheService: CacheService

  implicit val controllerComponents: ControllerComponents
  implicit val emailAccountexecutionContext: ExecutionContext = controllerComponents.executionContext
  implicit val emailAccountLoggingAction: LoggingAction = wire[LoggingAction]

  implicit val actorSystemForEmailAccountController: ActorSystem = ActorSystem()

  implicit val smartReachAuthMiddleware: SmartReachAuthMiddleware = wire[SmartReachAuthMiddleware]

  lazy val emailAccountController = wire[EmailAccountController]
}

trait ReportsModule extends LoggingModule with CacheModule {

  implicit val wsClient: WSClient

  implicit val cacheService: CacheService

  implicit val controllerComponents: ControllerComponents
  implicit val reportexecutionContext: ExecutionContext = controllerComponents.executionContext
  implicit val reportLoggingAction: LoggingAction = wire[LoggingAction]

  lazy val reportController = wire[ReportController]
}

trait IndexModule {

  implicit val controllerComponents: ControllerComponents

  lazy val indexController: IndexController = wire[IndexController]
}



