package api.accounts

import api.models.EmailAccount
import io.smartreach.esp.api.microsoftOAuth.{EmailSettingUpdateAccessToken, MicrosoftOAuthApi}
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.Logging

import scala.util.Try

object MicrosoftOAuth extends Logging with MicrosoftOAuthApi {

  override def updateAccessTokenAndRefreshToken(emailSettingId: EmailSettingId, data: EmailSettingUpdateAccessToken): Try[Option[EmailSettingId]] = {

    EmailAccount.updateAccessTokenAndRefreshToken(
      emailAccountId = emailSettingId.emailSettingId,
      data = data
    )
  }
}