package api.dao

import api.AppConfig
import api.services.CacheService
import utils.StringUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

object AccessTokenCacheDAO {

  def generateNewToken(
    orgId: Long
  ): String = {
    s"auth_pat_${orgId}_${StringUtils.genRandomAlphaNumericString30Chars}"
  }

  private def getCacheKey(
    token: String
  ): String = {

    s"auth:pat:::$token"

  }

  def saveNewToken(
    token: String,
    orgId: Long,
    cacheService: CacheService,
  )(implicit ec: ExecutionContext): Future[Boolean] = {

    cacheService.setStr(
      key = getCacheKey(token),
      value = orgId.toString,
      expirySeconds = AppConfig.AUTO_LOGIN_ACCESS_TOKEN_VALID_FOR_SECONDS,
    ).map { _ =>
      true
    }

  }

  def getOrgIdFromToken(
    token: String,
    cacheService: CacheService,
  )(implicit ec: ExecutionContext): Future[Long] = {

    cacheService.getStr(
      key = getCacheKey(token),
    ).flatMap {

      case None =>

        Future.failed(new Exception(s"Invalid token: $token"))

      case Some(orgIdStr) =>

        Future.fromTry {

          Try(orgIdStr.toLong)

        }

    }

  }

  def deleteToken(
    token: String,
    cacheService: CacheService,
  )(
    implicit ec: ExecutionContext,
  ): Future[Boolean] = {

    cacheService.removeStr(
      key = getCacheKey(token),
    ).map { _ =>
      true
    }

  }

}
