package api.dao

import api.AppConfig
import api.models.{AccountId, EmailAccountId, EmailHealthCheckRecord, EmailHealthCheckStatus}
import api.services.{EmailAuthStatus, EmailAuthStatusDetails, EmailHealthCheckDetails, EmailHealthCheckRecordId}
import scalikejdbc._

import scala.util.Try

object EmailHealthCheckDAO {

  def updateEmailHealthCheckResultOnSuccess(
    accountId: Long,
    emailHealthCheckDetails: EmailHealthCheckDetails,
    emailAuthStatusDetails: EmailAuthStatusDetails,
    dkimSelectorOpt: Option[String],
    emailHealthCheckRecordId: EmailHealthCheckRecordId
  ): Try[EmailHealthCheckRecordId] = Try {

    val spfAuthStatusStr = emailAuthStatusDetails.spf_auth_status.toString

    val dkimAuthStatusStr = emailAuthStatusDetails.dkim_auth_status.toString

    val dmarcAuthStatusStr = emailAuthStatusDetails.dmarc_auth_status.toString

    val spfRecordOptTrimmed = emailHealthCheckDetails.spf_record.map(_.trim)

    val dkimRecordOptTrimmed = emailHealthCheckDetails.dkim_record.map(_.trim)

    val dmarcRecordOptTrimmed = emailHealthCheckDetails.dmarc_record.map(_.trim)

    DB.autoCommit { implicit session =>

      sql"""
           UPDATE email_health_check_records
           SET
             spf_auth_status = $spfAuthStatusStr,
             dkim_auth_status = $dkimAuthStatusStr,
             dmarc_auth_status = $dmarcAuthStatusStr,

             dkim_selector = $dkimSelectorOpt,

             spf_record = $spfRecordOptTrimmed,
             dkim_record = $dkimRecordOptTrimmed,
             dmarc_record = $dmarcRecordOptTrimmed,

             status = ${EmailHealthCheckStatus.Completed.toString},

             updated_at = now()
           WHERE
             account_id = $accountId
             AND id = ${emailHealthCheckRecordId.id}
           RETURNING id;
         """
        .map { rs =>
          EmailHealthCheckRecordId(id = rs.long("id"))
        }
        .single()
        .apply()
        .get

    }

  }

  def updateEmailHealthCheckResultOnFailure(
    accountId: Long,
    emailHealthCheckRecordId: EmailHealthCheckRecordId,
  ): Try[EmailHealthCheckRecordId] = Try {

    val maxRetryCount = AppConfig.EmailHealthCheck.maxRetryCount

    DB.autoCommit { implicit session =>

      sql"""
          UPDATE email_health_check_records
          SET
            updated_at = now(),
            retry_count = retry_count + 1,
            status = CASE
              WHEN retry_count >= $maxRetryCount THEN
                ${EmailHealthCheckStatus.Failed.toString}
              ELSE
                ${EmailHealthCheckStatus.Pending.toString}
            END
          WHERE
            account_id = $accountId
            AND id = ${emailHealthCheckRecordId.id}
          RETURNING id;
         """
        .map { rs =>
          EmailHealthCheckRecordId(id = rs.long("id"))
        }
        .single()
        .apply()
        .get

    }

  }

  def createPendingEmailHealthCheckRecord(
    accountId: Long,
    emailAccountIdForHealthCheck: Long,
  ): Try[EmailHealthCheckRecordId] = Try {

    val spfAuthStatusStr = EmailAuthStatus.None.toString

    val dkimAuthStatusStr = EmailAuthStatus.None.toString

    val dmarcAuthStatusStr = EmailAuthStatus.None.toString

    DB autoCommit { implicit session =>

      sql"""
          INSERT INTO email_health_check_records (
            account_id,
            email_account_id,

            spf_auth_status,
            dkim_auth_status,
            dmarc_auth_status,

            status
          )
          VALUES (
            $accountId,
            $emailAccountIdForHealthCheck,

            $spfAuthStatusStr,
            $dkimAuthStatusStr,
            $dmarcAuthStatusStr,

            ${EmailHealthCheckStatus.Pending.toString}
          )

          ON CONFLICT (account_id, email_account_id)

          DO UPDATE
          SET
            spf_auth_status = $spfAuthStatusStr,
            dkim_auth_status = $dkimAuthStatusStr,
            dmarc_auth_status = $dmarcAuthStatusStr,

            status = ${EmailHealthCheckStatus.Pending.toString},

            retry_count = 0,

            updated_at = now()

          RETURNING id;
         """
        .map { rs =>
          EmailHealthCheckRecordId(id = rs.long("id"))
        }
        .single()
        .apply()
        .get

    }

  }

  def getPendingEmailHealthChecksForProcessing: Try[List[EmailHealthCheckRecordId]] = Try {

    DB autoCommit { implicit session =>

      sql"""
          WITH pending_records AS (
            SELECT
              ehcr.id
            FROM
              email_health_check_records AS ehcr
              JOIN email_accounts AS ea ON ehcr.email_account_id = ea.id
              JOIN accounts AS acc ON ea.account_id = acc.id
            WHERE
              ehcr.status = ${EmailHealthCheckStatus.Pending.toString}
              AND retry_count < ${AppConfig.EmailHealthCheck.maxRetryCount}
            LIMIT 100
          )
          UPDATE
            email_health_check_records
          SET
            status = ${EmailHealthCheckStatus.Queued.toString},
            updated_at = now()
          WHERE
            id IN (SELECT id FROM pending_records)
          RETURNING id;
        """
        .map { rs =>
          EmailHealthCheckRecordId(id = rs.long("id"))
        }
        .list()
        .apply()

    }

  }

  def getEmailHealthCheckRecord(
    emailAccountId: EmailAccountId,
    accountId: AccountId,
  ): Try[Option[EmailHealthCheckRecord]] = Try {

    DB readOnly { implicit session =>

      sql"""
          ${EmailHealthCheckDAO.getQueryWithoutWhereClause}
          WHERE
            email_account_id = ${emailAccountId.id}
            AND account_id = ${accountId.id}
         """
        .map { rs =>

          EmailHealthCheckRecord.fromDB(rs = rs)

        }
        .single()
        .apply()

    }

  }

  def getEmailHealthCheckRecord(
    id: EmailHealthCheckRecordId,
  ): Try[Option[EmailHealthCheckRecord]] = Try {

    DB readOnly { implicit session =>

      sql"""
          ${EmailHealthCheckDAO.getQueryWithoutWhereClause}
          WHERE
            id = ${id.id}
         """
        .map { rs =>

          EmailHealthCheckRecord.fromDB(rs = rs)

        }
        .single()
        .apply()

    }

  }

  private def getQueryWithoutWhereClause: SQLSyntax = {

    sqls"""
        SELECT
          id,
          account_id,

          email_account_id,

          spf_auth_status,
          dkim_auth_status,
          dmarc_auth_status,

          dkim_selector,

          spf_record,
          dkim_record,
          dmarc_record,

          status,

          retry_count,

          updated_at,
          created_at
        FROM
          email_health_check_records
       """

  }


}
