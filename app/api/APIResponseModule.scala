package api

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, JsonValidationError}
import play.api.mvc.{Result, Results}
import utils.SRLogger
final class APINotFoundException(message: String, cause: Throwable = None.orNull) extends Exception(message, cause)
final class APIBadRequestException(message: String, cause: Throwable = None.orNull) extends Exception(message, cause)
final class APIManualEmailSentButSavingFailedException(message: String, cause: Throwable = None.orNull) extends Exception(message, cause)

final class SRAPIResponse(val Logger: SRLogger) extends Results {

  final def JsValidationError(
    errors: Seq[(JsPath, scala.collection.Seq[JsonValidationError])]
  ) = {

    val param = errors(0)._1.toString().substring(1)

    val message = s"Please send a valid value for the field: '${param}'"

    val errorResponse = Json.toJson(
      ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.INVALID_PARAM, param = Option(param), error_code = None))
    )

    Logger.fatal(s"SRAPIResponse JsValidationError: $message ::: errors: $errors")

    BadRequest(errorResponse)
  }


  final def BadRequestError(
    message: String,
    error_code: Option[ErrorCode.Value] = None
  ) = {

    Logger.fatal(s"SRAPIResponse BadRequestError: message: $message ::: error_code: $error_code")

    BadRequest(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.BAD_REQUEST, param = None, error_code = error_code))
      )
    )

  }

  final def InvalidParamError(message: String, param: String) = {

    BadRequest(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.BAD_REQUEST, param = Some(param), error_code = None))
      )
    )

  }


  final def NotFoundError(
    message: String
  ) = {

    Logger.fatal(s"SRAPIResponse NotFoundError: $message")

    NotFound(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.NOT_FOUND, param = None, error_code = None))
      )
    )

  }

  final def ServerError(
    message: String,
    e: Option[Throwable]
  ): Result = {

    if (e.isDefined) {
      Logger.fatal(s"SRAPIResponse ServerError: $message", err = e.get)
    } else {
      Logger.fatal(s"SRAPIResponse ServerError: $message")
    }

    InternalServerError(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.API_ERROR, param = None, error_code = None))
      )
    )

  }

  final def ServerError(err: Throwable): Result = {
    ServerError(message = CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(err))
  }

  final def ForbiddenError(
    message: String
  ) = {

    Logger.fatal(s"SRAPIResponse ForbiddenError: message: $message")

    Forbidden(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.FORBIDDEN, param = None, error_code = None))
      )
    )

  }

  final def UnauthorizedError(message: String) = {

    Logger.fatal(s"SRAPIResponse UnauthorizedError: message: $message")

    Unauthorized(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.UNAUTHORIZED, param = None, error_code = None))
      )
    )

  }

  final def UnverifiedError(message: String) = {

    Logger.fatal(s"SRAPIResponse UnverifiedError: message: $message")

    Unauthorized(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.UNVERIFIED_ACCOUNT, param = None, error_code = None))
      )
    )

  }


  final def PaymentRequiredError(message: String) = {
    PaymentRequired(
      Json.toJson(
        ErrorResponse(message = message, data = ErrorBody(error_type = ErrorType.PAYMENT_REQUIRED, param = None, error_code = None))
      )
    )
  }

  final def Success(message: String, data: JsValue) = {

    Ok(
      Json.toJson(
        SuccessResponse(message = message, data = data)
      )
    )

  }

}

