package api

import play.api.libs.json._

/*
object ErrorCode extends Enumeration {
  type ErrorCode = Value


  // payment errors
  val EMAIL_ACCOUNT_LIMIT = Value(40201)
  val TEAM_MEMBER_LIMIT = Value(40202)
  val PROSPECT_LIMIT = Value(40203)

}
*/

case class BadRequestErrorException(message: String = "", cause: Throwable = None.orNull) extends Exception(message, cause)
case class ServerErrorException(message: String = "", cause: Throwable = None.orNull) extends Exception(message, cause)


object ResponseStatus extends Enumeration {
  type ResponseStatus = Value
  val SUCCESS = Value("success")
  val ERROR = Value("error")
}

object ErrorType extends Enumeration {
  type ErrorType = Value
  val INVALID_PARAM = Value("invalid_param")
  val BAD_REQUEST = Value("bad_request")
  val NOT_FOUND = Value("not_found")
  val RATE_LIMIT = Value("rate_limit")
  val API_ERROR = Value("api_error")
  val FORBIDDEN = Value("forbidden")
  val UNAUTHORIZED = Value("unauthorized")
  val PAYMENT_REQUIRED = Value("payment_required")
  val UNVERIFIED_ACCOUNT = Value("unverified")

}


object ErrorCode extends Enumeration {
  type ErrorCode = Value
  val ACCOUNT_WITH_EMAIL_EXISTS = Value("account_with_email_exists")
  val ACCOUNT_WITH_EMAIL_EXISTS_BUT_NOT_VERIFIED = Value("account_with_email_exists_but_not_verified")

  val GMAIL_NOT_SUPPORTED = Value("gmail_not_supported")
  val GSUITE_DOMAIN_NOT_INSTALLED = Value("gsuite_domain_not_installed")

  val UPGRADE_PLAN_INTEGRATION = Value("upgrade_plan_integration")


}


case class ErrorBody(error_type: ErrorType.Value, param: Option[String], error_code: Option[ErrorCode.Value])

object ErrorBody {
  implicit val errorBodyWrites: OWrites[ErrorBody] = Json.writes[ErrorBody]
}


case class ErrorResponse(status: ResponseStatus.Value = ResponseStatus.ERROR, message: String, data: ErrorBody)

object ErrorResponse {
  implicit val errorResponseWrites: OWrites[ErrorResponse] = Json.writes[ErrorResponse]
}


case class SuccessResponse(status: ResponseStatus.Value = ResponseStatus.SUCCESS, message: String, data: JsValue)

object SuccessResponse {
  implicit val successResponseWrites: OWrites[SuccessResponse] = Json.writes[SuccessResponse]
}

