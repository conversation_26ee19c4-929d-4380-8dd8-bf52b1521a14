package api.middleware


import api.models.{Account, EmailAccount}
import api.SRAPIResponse
import play.api.mvc._
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class EmailAccountRequest[A](
                               emailAccount: EmailAccount,
                               account: Account,
                               Logger: SRLogger,
                               Response: SRAPIResponse,
                               request: Request[A]
                             ) extends WrappedRequest[A](request)


trait EmailAccountUtils {

  final def hasEmailAccount(emailAccountId: Long)(implicit ec: ExecutionContext) = new ActionRefiner[AccountRequest, EmailAccountRequest] {

    override def executionContext: ExecutionContext = ec

    override def refine[A](request: AccountRequest[A]): Future[Either[Result, EmailAccountRequest[A]]] = {

      Future {
        val Res = request.Response

        EmailAccount.find(id = emailAccountId) match {

          case Failure(exception) =>

            if (exception.getMessage.contains("Email Account not found")) {

              Left(Res.NotFoundError("Email Account not found"))
            }
            else {

              Left(Res.ServerError(exception.getMessage, None))
            }

          case Success(emailAccount) =>

            if (emailAccount.account_id != request.account.id) {

              Left(Res.ForbiddenError("You do not have the permission to access this email account"))

            } else {

              Right(EmailAccountRequest(
                emailAccount = emailAccount,
                account = request.account,
                Logger = request.Logger,
                Response = request.Response,
                request = request.request
              ))

            }
        }
      }
    }
  }


}
