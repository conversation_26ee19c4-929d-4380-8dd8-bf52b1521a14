package api.middleware

import api.models.{Account, AccountDB}
import api.{AppConfig, SRAPIResponse}
import play.api.mvc.{Action<PERSON>uilder, ActionRefiner, AnyContent, Request, Result, WrappedRequest}
import utils.{<PERSON><PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class ProductRequest[A](
  Logger: SRLogger,
  Response: SRAPIResponse,
  request: Request[A]
) extends WrappedRequest[A](request)

case class WarmupHeroAccountRequest[A](
  Logger: SRLogger,
  Response: SRAPIResponse,
  request: Request[A],
  account: Account,
) extends WrappedRequest[A](request)


class SmartReachAuthMiddleware(
  loggingAction: LoggingAction,
) {

  def isAuthorized(
    implicit ec: ExecutionContext
  ): ActionBuilder[ProductRequest, AnyContent] =
    loggingAction andThen new ActionRefiner[LoggingRequest, ProductRequest] {

      override def executionContext: ExecutionContext = ec

      override protected def refine[A](
        request: LoggingRequest[A],
      ): Future[Either[Result, ProductRequest[A]]] = {

        implicit val Logger: SRLogger = request.Logger.appendLogRequestId("[SmartReachAuthMiddleware]")

        val apiKeyOpt = request.headers.get("X-API-KEY").map(_.trim)

        val Res = new SRAPIResponse(Logger = Logger)

        apiKeyOpt match {

          case None =>

            Logger.debug(
              msg = s"API key not found"
            )

            Future.successful(
              Left(
                Res.UnauthorizedError(message = "API key not found")
              )
            )

          case Some(apiKey) =>

            if (apiKey != AppConfig.srApiKey) {

              Logger.debug(
                msg = s"Invalid API key :: API key: $apiKey"
              )

              Future.successful(
                Left(
                  Res.UnauthorizedError("Invalid API key")
                )
              )

            } else {

              Future.successful(
                Right(
                  ProductRequest(
                    Logger = request.Logger,
                    Response = request.Response,
                    request = request,
                  )
                )
              )

            }

        }

      }

    }


  def hasWarmupHeroAccount(
    implicit ec: ExecutionContext
  ): ActionBuilder[WarmupHeroAccountRequest, AnyContent] =
    loggingAction andThen new ActionRefiner[LoggingRequest, WarmupHeroAccountRequest] {

      override def executionContext: ExecutionContext = ec

      override protected def refine[A](
        request: LoggingRequest[A],
      ): Future[Either[Result, WarmupHeroAccountRequest[A]]] = {

        implicit val Logger: SRLogger = request.Logger.appendLogRequestId("[SmartReachAuthMiddleware - hasWarmupHeroAccount]")

        val accountApiKeyOpt = request.headers.get("X-API-KEY").map(_.trim)

        val Res = new SRAPIResponse(Logger = Logger)

        accountApiKeyOpt match {

          case None =>

            Logger.debug(
              msg = s"API key not found - ${request.body}",
            )

            Future.successful(
              Left(
                Res.UnauthorizedError(message = "API key not found")
              )
            )

          case Some(apiKey) =>

            AccountDB.findAllBySrApiKey(
              srApiKey = apiKey,
            ) match {

              case Failure(exception) =>

                Logger.shouldNeverHappen(
                  msg = s"Failed to find account by API key - findAllBySrApiKey.",
                  err = Some(exception),
                )

                Future.failed(exception)

              case Success(accounts) =>

                if (accounts.isEmpty) {

                  Logger.debug(
                    msg = s"Account not found by API key - findAllBySrApiKey.",
                  )

                  Future.successful(
                    Left(
                      Res.UnauthorizedError("Invalid API key")
                    )
                  )

                } else if (accounts.length > 1) {

                  Logger.debug(
                    msg = s"Multiple accounts found by API key - findAllBySrApiKey. accountIds: ${accounts.map(_.id)} :: accountEmails: ${accounts.map(_.email)}",
                  )

                  Future.successful(
                    Left(
                      Res.UnauthorizedError("Invalid API key")
                    )
                  )

                } else {

                  val account = accounts.head

                  Future.successful(
                    Right(
                      WarmupHeroAccountRequest(
                        Logger = request.Logger,
                        Response = request.Response,
                        request = request,
                        account = account,
                      )
                    )
                  )

                }

            }

        }

      }

    }

}
