package api.middleware

import api.models.{Account, AccountDB}
import api.services.CacheService
import api.SRAPIResponse
import play.api.{ Logging}
import play.api.mvc._
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Random, Try}

case class AccountRequest[A](
  account: Account,
  Logger: SRLogger,
  Response: SRAPIResponse,
  request: Request[A]
) extends WrappedRequest[A](request)

trait AuthUtils extends Logging{

  // abstract dependency
  // REF: http://stackoverflow.com/a/********
  def cacheService: CacheService



  protected final def genVerificationCode(accountId: Long): String = {

    val otp =  Random.nextInt(999999)

    s"$otp"
  }

  protected final def getAccountIdFromEmailVerificationCode(code: String): Option[Int] = {
    // REF: http://stackoverflow.com/a/********
    val accountId = code.split("\\|").headOption.flatMap(parseInt)
    accountId
  }

  protected final def parseInt(s: String): Option[Int] = if (s.isEmpty) None else Try(s.toInt).toOption

  final val MASTER_SUFFIX = "___ismaster"

  final val MASTER_PASS = "H^cJGyrJWGj2"

  protected final def getAccountById(accountId: Long)(implicit ec: ExecutionContext): Future[Option[Account]] = {

    cacheService.getSessionAccount(accountId)
      .flatMap {

        case Some(fromCache) => Future.successful(Some(fromCache))

        case None =>

          val account = AccountDB.find(accountId)

          if (account.isDefined) {

            cacheService.setSessionAccount(account.get)
              .map(_ => account)

          } else {

            Future.successful(None)

          }
      }
  }

  protected final def isLoggedIn()(
    implicit ec: ExecutionContext, loggingAction: LoggingAction
  ): ActionBuilder[AccountRequest, AnyContent] = loggingAction andThen new ActionRefiner[LoggingRequest, AccountRequest] {

    override def executionContext: ExecutionContext = ec

    override def refine[A](request: LoggingRequest[A]): Future[Either[Result, AccountRequest[A]]] = {

      def UNAUTHORIZED = Left(
        request.Response.UnauthorizedError("Please login").withNewSession
      )

      // if website session/cookie call
      val sessionAccountIdVal = request.session.get("account_id")

      var sessionIsMasterAccount = false

      val sessionAccountIdOpt = if (sessionAccountIdVal.isDefined) {

        if (sessionAccountIdVal.get.endsWith(MASTER_SUFFIX)) {

          sessionIsMasterAccount = true

          val numericPart = sessionAccountIdVal.get.substring(0, sessionAccountIdVal.get.length - MASTER_SUFFIX.length)

          parseInt(numericPart)

        } else {

          sessionAccountIdVal.flatMap { id => parseInt(id) }

        }

      } else None

      if (sessionAccountIdOpt.isDefined) {

        val accountId = sessionAccountIdOpt.get

        getAccountById(accountId)
          .map {

            case None => UNAUTHORIZED

            case Some(account) =>

              Right(AccountRequest(
                account = account,
                Logger = request.Logger,
                Response = request.Response,
                request = request.request
              ))

          }

      } else {

        Future.successful(UNAUTHORIZED)

      }
    }
  }

  protected final def isRequestFromExtension()(
    implicit ec: ExecutionContext, loggingAction: LoggingAction
  ) = loggingAction andThen new ActionRefiner[LoggingRequest, LoggingRequest] {

    override def executionContext: ExecutionContext = ec

    override def refine[A](request: LoggingRequest[A]): Future[Either[Result, LoggingRequest[A]]] = {

      def UNAUTHORIZED = Left(
        request.Response.UnauthorizedError("Invalid origin").withNewSession
      )

      val origin = request.headers.get("Origin").getOrElse("*")
      val logger = request.Logger

      if (origin.contains("chrome-extension://")) {

        Future.successful(Right(request))

      } else {

        logger.error(s"Invalid origin: $origin")
        Future.successful(UNAUTHORIZED)

      }
    }
  }

}
