package api.middleware

import api.SRAPIResponse
import play.api.mvc._
import utils.{SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}

case class LoggingRequest[A](
  Logger: SRLogger,
  Response: SRAPIResponse,
  request: Request[A]
) extends WrappedRequest[A](request)


// REF: https://www.playframework.com/documentation/2.6.x/ScalaActionsComposition#Authentication
class LoggingAction(
  val parser: BodyParsers.Default
)(implicit val executionContext: ExecutionContext)
  extends ActionBuilder[LoggingRequest, AnyContent]
    with ActionTransformer[Request, LoggingRequest] {

  def transform[A](request: Request[A]) = Future.successful {

    val logRequestId = StringUtils.genLogTraceId

    val Logger = new SRLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)

    LoggingRequest(
      Logger = Logger,
      Response = Res,
      request = request
    )
  }
}
