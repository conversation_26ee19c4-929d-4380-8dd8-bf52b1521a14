package api.models

import java.time.ZoneOffset
import play.api.libs.json._
import scalikejdbc._
import utils.email.FolderType

import scala.concurrent.blocking
import scala.util.Try


case class EmailAccountStats(
  total_scheduled: Int,
  total_landed_in_inbox: Int,
  total_landed_in_spam: Int,
  total_landed_in_category: Int,
  total_bounced_email: Int,
  total_landing_check_failed: Int
)


case class EmailAccountStatsDaily(
 time_axis_name: String,
 time_axis_timestamp: Long,
 total_scheduled: Int,
 total_landed_in_inbox: Int,
 total_landed_in_spam: Int,
 total_landed_in_category: Int,
 total_bounced_email: Int,
 total_landing_check_failed: Int
)

object EmailAccountStatsDaily {
  implicit val writes: OWrites[EmailAccountStatsDaily] = Json.writes[EmailAccountStatsDaily]

  def fromDb(rs: WrappedResultSet): EmailAccountStatsDaily = {

    val time_axis_timestamp = rs.localDateTime("time_axis_timestamp")
    val total_scheduled = rs.int("total_scheduled")

    EmailAccountStatsDaily(
      time_axis_name = rs.string("time_axis_name"),
      time_axis_timestamp = time_axis_timestamp.toInstant(ZoneOffset.UTC).toEpochMilli,
      total_scheduled = total_scheduled,
      total_landed_in_spam = rs.int("total_landed_in_spam"),
      total_landed_in_inbox = rs.int("total_landed_in_inbox"),
      total_landed_in_category = rs.int("total_landed_in_category"),
      total_landing_check_failed = rs.int("total_landing_check_failed"),
      total_bounced_email = rs.int("total_bounced")
    )
  }
}

case class ActivateOrDeactivateEmailAccounts(email_account_ids: List[Long])

object ActivateOrDeactivateEmailAccounts {
  implicit val reads: Reads[ActivateOrDeactivateEmailAccounts] = Json.reads[ActivateOrDeactivateEmailAccounts]
}

case class EmailAccountUpdateSignatureForm(signature: String)

object EmailAccountUpdateSignatureForm {
  implicit val reads: Reads[EmailAccountUpdateSignatureForm] = Json.reads[EmailAccountUpdateSignatureForm]
}

case class EmailAccountUpdateFrequencySettingsForm(
  emails_per_day: Int,
  emails_increase_per_day: Int,
  email_account_ids: List[Long],
)

object EmailAccountUpdateFrequencySettingsForm {
  implicit val reads: Reads[EmailAccountUpdateFrequencySettingsForm] = Json.reads[EmailAccountUpdateFrequencySettingsForm]
}

object EmailAccountStats {


  implicit val writes: OWrites[EmailAccountStats] = Json.writes[EmailAccountStats]

  implicit val session: AutoSession.type = AutoSession

  def fromDb(rs: WrappedResultSet): EmailAccountStats = EmailAccountStats(
    total_scheduled = rs.int("total_scheduled"),
    total_landed_in_spam = rs.int("total_landed_in_spam"),
    total_landed_in_inbox = rs.int("total_landed_in_inbox"),
    total_landed_in_category = rs.int("total_landed_in_category"),
    total_landing_check_failed = rs.int("total_landing_check_failed"),
    total_bounced_email = rs.int("total_bounced")
  )


  def getEmailAccountStats(emailAccountId: Long, accountId: Long, from: Option[Long], till: Option[Long]): Option[EmailAccountStats] = blocking {
    DB readOnly { implicit session =>


      val durationClause = if (from.isDefined && till.isDefined) sqls" AND e.sent_at >= to_timestamp($from) AND e.sent_at < to_timestamp($till) " else sqls""
      sql"""
        SELECT
          COUNT(e.*) FILTER (WHERE e.sent) AS total_scheduled,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.INBOX.toString}::folder_type_enum) AS total_landed_in_inbox,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.SPAM.toString}::folder_type_enum) AS total_landed_in_spam,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.LANDING_CHECK_FAILED.toString}::folder_type_enum) AS total_landing_check_failed,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.BOUNCED.toString}::folder_type_enum) AS total_bounced,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.CATEGORY.toString}::folder_type_enum) AS total_landed_in_category
        FROM emails_scheduled e
        WHERE e.sender_email_account_id = $emailAccountId
              AND e.account_id = $accountId
              $durationClause
    """
        .map(fromDb)
        .single()
        .apply()

    }
  }


  /*



  def getEmailAccountStatsDaily(emailAccountId: Long, accountId: Long, from: Long, till: Long, isReply: Boolean): List[EmailAccountStatsDaily] = blocking {
    DB readOnly { implicit session =>


      val isReplyClause = if(isReply) sqls"AND e.is_reply = TRUE" else sqls"AND e.is_reply = FALSE"

      sql"""
        SELECT
          timeaxis::date AS time_axis_name,
          timeaxis AS time_axis_timestamp,
          COUNT(e.*) AS total_scheduled,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.INBOX.toString}::folder_type_enum) AS total_landed_in_inbox,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.SPAM.toString}::folder_type_enum) AS total_landed_in_spam,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.LANDING_CHECK_FAILED.toString}::folder_type_enum) AS total_landing_check_failed,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.BOUNCED.toString}::folder_type_enum) AS total_bounced,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.CATEGORY.toString}::folder_type_enum) AS total_landed_in_category
        FROM generate_series (to_timestamp($from), to_timestamp($till), '1 day'::interval) AS timeaxis

        LEFT OUTER JOIN emails_scheduled e ON (
          e.sender_email_account_id = $emailAccountId
          AND e.account_id = $accountId
          AND e.sent = TRUE
          AND e.sent_at >= timeaxis
          AND e.sent_at <  timeaxis + interval '1 day'
          AND e.sent_at >= to_timestamp($from)
          AND e.sent_at < to_timestamp($till)
          $isReplyClause
        )
        GROUP BY timeaxis
        ORDER BY timeaxis;
      """
        .map(EmailAccountStatsDaily.fromDb)
        .list()
        .apply()

    }
  }

  */

  def getEmailAccountStatsDaily(emailAccountId: Long, accountId: Long, from: Long, till: Long): Try[List[EmailAccountStatsDaily]] = Try {
    DB readOnly { implicit session =>


      sql"""
        SELECT
          timeaxis::date AS time_axis_name,
          timeaxis AS time_axis_timestamp,
          COUNT(e.*) AS total_scheduled,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.INBOX.toString}::folder_type_enum) AS total_landed_in_inbox,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.SPAM.toString}::folder_type_enum) AS total_landed_in_spam,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.LANDING_CHECK_FAILED.toString}::folder_type_enum) AS total_landing_check_failed,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.BOUNCED.toString}::folder_type_enum) AS total_bounced,
          COUNT(e.*) FILTER (WHERE e.landed_folder_type IS NOT NULL AND e.landed_folder_type = ${FolderType.CATEGORY.toString}::folder_type_enum) AS total_landed_in_category
        FROM generate_series (to_timestamp($from), to_timestamp($till), '1 day'::interval) AS timeaxis

        LEFT OUTER JOIN emails_scheduled e ON (
          e.sender_email_account_id = $emailAccountId
          AND e.account_id = $accountId
          AND e.sent = TRUE
          AND e.sent_at >= timeaxis
          AND e.sent_at <  timeaxis + interval '1 day'
          AND e.sent_at >= to_timestamp($from)
          AND e.sent_at < to_timestamp($till)
        )
        GROUP BY timeaxis
        ORDER BY timeaxis;
      """
        .map(EmailAccountStatsDaily.fromDb)
        .list()
        .apply()

    }
  }


}
