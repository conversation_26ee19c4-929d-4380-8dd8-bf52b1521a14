package api.models

import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait EmailType {

  def getEmailPurpose: String

  def getEmailAccountOwnerDepartment: String

  def getEmailAccountOwnerPosition: String

}

object EmailType {

  private val sales = "sales"
  private val marketing = "marketing"
  private val recruitment = "recruitment"

  case object Sales extends EmailType {

    override def toString: String = sales

    override def getEmailPurpose = "Sales Outreach"

    override def getEmailAccountOwnerDepartment = "Sales"

    override def getEmailAccountOwnerPosition = "Sales Leader"

  }

  case object Marketing extends EmailType {

    override def toString: String = marketing

    override def getEmailPurpose = "Generate Leads"

    override def getEmailAccountOwnerDepartment = "Marketing"

    override def getEmailAccountOwnerPosition = "Head of Marketing"

  }

  case object Recruitment extends EmailType {

    override def toString: String = recruitment

    override def getEmailPurpose = "Candidate Recruitment"

    override def getEmailAccountOwnerDepartment = "Human Resources"

    override def getEmailAccountOwnerPosition = "Talent Acquisition Specialist"

  }

  def fromKey(key: String): Try[EmailType] = Try {

    key match {

      case `sales` => Sales

      case `marketing` => Marketing

      case `recruitment` => Recruitment

    }

  }

  implicit def format: Format[EmailType] = new Format[EmailType] {

    override def reads(json: JsValue): JsResult[EmailType] = {

      fromKey(json.as[String]) match {

        case Failure(e) => JsError(e.toString)

        case Success(currency) => JsSuccess(currency)

      }

    }

    override def writes(o: EmailType): JsValue = {

      JsString(o.toString)

    }

  }

}

