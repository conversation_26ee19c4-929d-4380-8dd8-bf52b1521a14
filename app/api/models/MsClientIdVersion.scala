package api.models

import scala.util.{Failure, Success, Try}

sealed trait MsClientIdVersion

object MsClientIdVersion {

  private val v1 = "v1"
  private val v2 = "v2"

  case object V1 extends MsClientIdVersion {
    override val toString: String = v1
  }

  case object V2 extends MsClientIdVersion {
    override val toString: String = v2
  }

  def from<PERSON>ey(key: String): Try[MsClientIdVersion] = {

    key match {

      case `v1` => Success(MsClientIdVersion.V1)

      case `v2` => Success(MsClientIdVersion.V2)

      case _ => Failure(new IllegalArgumentException(s"Invalid MsClientIdVersion value: $key"))

    }

  }

}
