package api.models

import play.api.libs.json.{Format, Js<PERSON>rror, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait TemplateGenerationType

object TemplateGenerationType {

  private val basic = "basic"
  private val ai = "ai"

  case object Basic extends TemplateGenerationType {

    override def toString: String = basic

  }

  case object AI extends TemplateGenerationType {

    override def toString: String = ai

  }


  def fromKey(key: String): Try[TemplateGenerationType] = Try {

    key match {

      case `basic` => Basic

      case `ai` => AI

    }

  }

  implicit def format: Format[TemplateGenerationType] = new Format[TemplateGenerationType] {

    override def reads(json: JsValue): JsResult[TemplateGenerationType] = {

      fromKey(json.as[String]) match {

        case Failure(e) => JsError(e.toString)

        case Success(currency) => JsSuccess(currency)

      }

    }

    override def writes(o: TemplateGenerationType): JsValue = {

      JsString(o.toString)

    }

  }

}