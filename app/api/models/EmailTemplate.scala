package api.models

import io.sr.llm.bedrock.services.GeneratedEmail
import scalikejdbc.DB
import scalikejdbc._
import utils.cronjobs.{EmailBodyDetails, EmailSubjectAndBodyDetails}

import scala.util.Try




case class EmailSubjectTemplate(
   id: Long,
   subject: String
)
object EmailTemplate {


  def createSubjectTemplate(
    generatedEmail: GeneratedEmail,
    emailType: EmailType,
  ): Try[Option[EmailSubjectTemplate]] = Try {

    DB.autoCommit { implicit session =>

      sql"""
          INSERT INTO email_templates
            (
              subject,
              generation_type,
              email_purpose,
              prompt_token_count,
              generation_token_count
            )
            VALUES (
              ${generatedEmail.subject.trim},
              ${TemplateGenerationType.AI.toString},
              ${emailType.getEmailPurpose},
              ${generatedEmail.subject_prompt_token_count},
              ${generatedEmail.subject_generation_token_count}
            )
          RETURNING
            id,
            subject;
         """
        .map { rs =>

          EmailSubjectTemplate(
            id = rs.long("id"),
            subject = rs.string("subject")
          )

        }
        .single()
        .apply()

    }

  }

  def createEmailBodyTemplate(
    generatedEmail: GeneratedEmail,
    emailSubjectTemplateId: Long,
    isReply: Boolean,
  ): Try[Option[Long]] = Try {

    DB.autoCommit { implicit session =>

      sql"""
          INSERT INTO email_template_body
            (
              email_body,
              subject_template_id,
              prompt_token_count,
              generation_token_count,
              is_reply
            )
            VALUES (
              ${generatedEmail.body.trim},
              $emailSubjectTemplateId,
              ${generatedEmail.body_prompt_token_count},
              ${generatedEmail.body_generation_token_count},
              $isReply
            )
          RETURNING
            id
         """
        .map { rs =>

          rs.long("id")

        }
        .single()
        .apply()

    }

  }


  def findSubjectForEmailSchedule(emailAccountId: Long):Option[EmailSubjectTemplate] = {

    // Maybe remove the TemplateGenerationType.Basic from where clause

    DB readOnly { implicit session =>

      sql"""
        SELECT et.id, et.subject
          FROM email_templates et
          WHERE
          NOT EXISTS (SELECT * FROM emails_scheduled es
            WHERE es.sender_email_account_id = $emailAccountId
            AND es.template_id = et.id
            AND (et.generation_type IS NULL OR et.generation_type = ${TemplateGenerationType.Basic.toString})
            AND es.sent_at > now() - interval '24 hours'
          )
          ORDER BY random()
        LIMIT 1
      """
        .map(rs => {

          EmailSubjectTemplate(
            id = rs.long("id"),
            subject = rs.string("subject")
          )
        })
        .headOption()
        .apply()

    }
  }


  def findEmailBodyForEmailSchedule():Option[String] = {
    DB readOnly { implicit session =>

      sql"""
        SELECT email_body
          FROM email_template_body
          WHERE subject_template_id is NULL -- don't use AI generated email body templates, remove later.
          ORDER BY random()
          LIMIT 1
      """
        .map(rs => rs.string("email_body"))
        .headOption()
        .apply()

    }
  }

  def findSubjectAndBodyForAIEmailSchedule(
    emailAccountId: Long,
    emailType: EmailType,
  ): Try[Option[EmailSubjectAndBodyDetails]] = Try {

    val isReply = false

    DB readOnly { implicit session =>

      sql"""
          SELECT
            et.id,
            et.subject,
            etb.email_body
          FROM
            email_templates AS et
            JOIN email_template_body AS etb ON et.id = etb.subject_template_id
          WHERE
            et.generation_type = ${TemplateGenerationType.AI.toString}
            AND et.email_purpose = ${emailType.getEmailPurpose}
            AND etb.is_reply = $isReply
            AND NOT EXISTS (
              SELECT
                *
              FROM
                emails_scheduled es
              WHERE
                es.sender_email_account_id = $emailAccountId
                AND es.template_id = et.id
                AND es.scheduled_at > now() - interval '48 hours'
            )
          ORDER BY
            random()
          LIMIT 1
         """
        .map { rs =>

          val subjectTemplate = EmailSubjectTemplate(
            id = rs.long("id"),
            subject = rs.string("subject")
          )

          EmailSubjectAndBodyDetails(
            subjectTemplate = subjectTemplate,
            bodyWithoutSignatureAndSalutation = rs.string("email_body")
          )

        }
        .single()
        .apply()

    }

  }

  def findBodyForAIEmailReply(
    emailSubjectTemplateId: Long,
    emailSubject: String,
  ): Try[Option[EmailBodyDetails]] = Try {

    val isReply = true

    DB readOnly { implicit session =>

      sql"""
          SELECT
            etb.email_body
          FROM
            email_templates AS et
            JOIN email_template_body AS etb ON et.id = etb.subject_template_id
          WHERE
            et.id = $emailSubjectTemplateId
            AND et.subject = $emailSubject
            AND et.generation_type = ${TemplateGenerationType.AI.toString}
            AND etb.is_reply = $isReply
          LIMIT 1
         """
        .map { rs =>

          EmailBodyDetails(
            bodyWithoutSignatureAndSalutation = rs.string("email_body")
          )

        }
        .single()
        .apply()

    }

  }

  def findSalutationForEmailSchedule():Option[String] = {
    DB readOnly { implicit session =>

      sql"""
        SELECT salutation
          FROM email_template_salutations
          ORDER BY random()
          LIMIT 1
      """
        .map(rs => rs.string("salutation"))
        .headOption()
        .apply()

    }
  }

  def findEndingRemarkForEmailSchedule():Option[String] = {
    DB readOnly { implicit session =>

      sql"""
        SELECT ending_remark
          FROM email_template_ending_remarks
          ORDER BY random()
          LIMIT 1
      """
        .map(rs => rs.string("ending_remark"))
        .headOption()
        .apply()

    }
  }
}
