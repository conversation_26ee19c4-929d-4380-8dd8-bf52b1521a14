package api.models

import play.api.libs.json._

import scala.util.{Failure, Success, Try}

sealed trait EmailHealthCheckStatus {
  def toString: String
}

object EmailHealthCheckStatus {

  private val pending = "pending"
  private val queued = "queued"
  private val completed = "completed"
  private val failed = "failed"

  case object Pending extends EmailHealthCheckStatus {
    override def toString: String = pending
  }

  case object Queued extends EmailHealthCheckStatus {
    override def toString: String = queued
  }

  case object Completed extends EmailHealthCheckStatus {
    override def toString: String = completed
  }

  case object Failed extends EmailHealthCheckStatus {
    override def toString: String = failed
  }


  def fromKey(actionStatus: String): Try[EmailHealthCheckStatus] = {

    actionStatus match {

      case `pending` => Success(Pending)

      case `queued` => Success(Queued)

      case `completed` => Success(Completed)

      case `failed` => Success(Failed)

      case _ => Failure(new IllegalArgumentException(s"Invalid EmailHealthCheckStatus value: $actionStatus"))

    }

  }

  implicit val reads: Reads[EmailHealthCheckStatus] = new Reads[EmailHealthCheckStatus] {

    override def reads(json: JsValue): JsResult[EmailHealthCheckStatus] = {

      fromKey(json.as[String]) match {

        case Success(status) => JsSuccess(status)

        case Failure(d) => JsError(s"Invalid EmailHealthCheckStatus value: ${d.getMessage}")

      }

    }

  }

  implicit val writes: Writes[EmailHealthCheckStatus] = new Writes[EmailHealthCheckStatus] {

    override def writes(status: EmailHealthCheckStatus): JsValue = JsString(status.toString)

  }

}
