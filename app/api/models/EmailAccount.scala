package api.models

import java.time._
import api.{AppConfig, CONSTANTS}
import api.emails.EmailToBeSent
import api.models.MsClientIdVersion
import api.services.{EmailAuthStatus, FromTill}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import org.joda.time.DateTime
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import play.api.libs.json.{JsError, JsString, JsSuccess, JsValue, Json, OWrites, Reads, Writes}
import scalikejdbc.DB
import scalikejdbc._
import scalikejdbc.jodatime.JodaWrappedResultSet._
import utils.dbutils.SQLUtils
import utils.email.{EmailHelper, FolderType}
import utils.{Help<PERSON>, SRLogger}
import utils.enum.EnumUtils
import utils.security.EncryptionService

import scala.util._
import scala.concurrent.blocking


case class EmailAccountWarmupStatus(
  email: String,
  service_provider: EmailServiceProvider,
  status: EmailAccountStatus.Value,
)

object EmailAccountWarmupStatus {

  implicit val writes: OWrites[EmailAccountWarmupStatus] = Json.writes[EmailAccountWarmupStatus]

}

case class BasicEmailAccountDetails(
  emailAccountId: Long,
  accountIdOpt: Option[Long],
)


sealed trait EmailServiceProvider

object EmailServiceProvider {
  private val google_workspace="google_workspace"
  private val microsoft_365="microsoft_365"
  private val microsoft_365_api="microsoft_365_api"
  private val other="other"

  case object GOOGLE_WORKSPACE extends EmailServiceProvider {
    override val toString: String = google_workspace
  }
  case object MICROSOFT_365 extends EmailServiceProvider {
    override val toString: String = microsoft_365
  }
  case object MICROSOFT_365_API extends EmailServiceProvider {
    override val toString: String = microsoft_365_api
  }
  case object OTHER extends EmailServiceProvider {
    override val toString: String = other
  }

  def withName(str: String): Try[EmailServiceProvider] = Try {
    str match {
      case `google_workspace` => GOOGLE_WORKSPACE
      case `microsoft_365` => MICROSOFT_365
      case `microsoft_365_api` => MICROSOFT_365_API
      case `other` => OTHER
    }
  }

  implicit def writes: Writes[EmailServiceProvider] = (i: EmailServiceProvider) => {
    JsString(i.toString)
  }

  implicit def reads: Reads[EmailServiceProvider] = (json: JsValue) => {
    withName(str = json.as[String]) match {
      case Success(provider) => JsSuccess(provider)
      case Failure(e) => JsError(s"Invalid EmailServiceProvider error = $e")
    }
  }
}

object EmailAccountStatus extends Enumeration {
  type CampaignStatus = Value
  val RUNNING = Value("running")
  val STOPPED = Value("stopped")
  val PAUSED = Value("paused")
}


sealed trait EmailSettings

object EmailSettings {

  sealed trait EmailSendSettings

  case class SmtpEmailAccount(
                               smtp_username: String,
                               smtp_password: String,
                               smtp_host: String,
                               smtp_port: Int
                             ) extends EmailSendSettings

  case class ImapEmailAccount(
                               imap_username: String,
                               imap_password: String,
                               imap_host: String,
                               imap_port: Int
                             )

  case class SmtpImapSettings(
                               smtp_settings: SmtpEmailAccount,
                               imap_settings: ImapEmailAccount
                             ) extends EmailSettings


  case class OAuthTokens(
                          oauth2_refresh_token: String,
                          oauth2_access_token: String,
                          oauth2_access_token_expires_at: DateTime,
                        ) extends EmailSettings with EmailSendSettings
}

case class EmailAccount(
                         id: Long,
                         account_id: Long,
                         email: String,
                         service_provider: EmailServiceProvider,
                         sender_name: String,
                         first_name: String,
                         last_name: String,
                         email_type: EmailType,
                         signature: Option[String],
                         active: Boolean,
                         warm_up_emails_per_day: Int,
                         warm_up_emails_increase_per_day: Int,

                         message_id_suffix: String,

                         settings: EmailSettings,

                         basic_email_health_check_details: Option[BasicEmailHealthCheckRecord],

                         status: EmailAccountStatus.Value,
                         error: Option[String],
                         raw_error: Option[String],
                         error_reported_at: Option[LocalDateTime],
                         paused_till: Option[LocalDateTime],
                         created_at: LocalDateTime,
                         ms_client_id_version: MsClientIdVersion
                       )

case class BasicEmailHealthCheckRecord(
  spf_auth_status: EmailAuthStatus,
  dkim_auth_status: EmailAuthStatus,
  dmarc_auth_status: EmailAuthStatus,
  status: EmailHealthCheckStatus,
)

object BasicEmailHealthCheckRecord {

  implicit val writes: Writes[BasicEmailHealthCheckRecord] = Json.writes[BasicEmailHealthCheckRecord]

}


case class EmailAccountBasicWithStats(
                                       id: Long,
                                       email: String,
                                       service_provider: EmailServiceProvider,
                                       status: EmailAccountStatus.Value,
                                       error: Option[String],
                                       total_sent_today: Int,
                                       total_landed_in_inbox_today: Int,
                                       total_landed_in_spam_today: Int,
                                       total_landed_in_categories_today: Int,
                                       total_landing_check_failed_today: Int,
                                       total_bounced_today: Int,
                                       basic_email_health_check_details: Option[BasicEmailHealthCheckRecord],
                                       created_at: OffsetDateTime
                                     )

object EmailAccountBasicWithStats {

  implicit val writes: Writes[EmailAccountBasicWithStats] = new Writes[EmailAccountBasicWithStats] {
    def writes(c: EmailAccountBasicWithStats) = Json.obj(
      "id" -> c.id,
      "email" -> c.email,
      "service_provider" -> c.service_provider,
      "status" -> c.status.toString,

      "error" -> c.error,
      "created_at" -> c.created_at,
      "total_sent_today" -> c.total_sent_today,
      "total_landed_in_inbox_today"-> c.total_landed_in_inbox_today,
      "total_landed_in_spam_today"-> c.total_landed_in_spam_today,
      "total_landed_in_categories_today"-> c.total_landed_in_categories_today,

      "basic_email_health_check_details" -> c.basic_email_health_check_details,

      "total_bounced_today" -> c.total_bounced_today,
      "total_landing_check_failed_today" -> c.total_landing_check_failed_today
    )
  }
}

case class EmailOAuthAccountForm(
                                  email: String,
                                  service_provider: EmailServiceProvider,
                                  first_name: String,
                                  last_name: String,

                                  oauth2_access_token : String,
                                  oauth2_refresh_token : String,
                                  oauth2_token_type : String,
                                  oauth2_token_expires_in : Int,
                                  oauth2_access_token_expires_at : DateTime,
                                )

case class EmailAccountForm(
                             email: String,
                             service_provider: EmailServiceProvider,
                             first_name: String,
                             last_name: String,

                             smtp_username: String,
                             smtp_password: String,
                             smtp_host: String,
                             smtp_port: Int,

                             imap_host: String,
                             imap_username: String,
                             imap_password: String,
                             imap_port: Int
                           )

case class BasicAccountForm(
                             first_name: String,
                             last_name: String,
                             email_type: EmailType,
                           )

object BasicAccountForm {
  implicit val reads: Reads[BasicAccountForm] = Json.reads[BasicAccountForm]
}

case class EmailTestForm(
                          email: String,
                          first_name: String,
                          last_name: String
                        )


object EmailAccountForm {
  implicit val reads: Reads[EmailAccountForm] = Json.reads[EmailAccountForm]
}


case class EmailAccountForCreateEmailSchedule(
                                               id: Long,
                                               email: String,
                                               email_domain: String,
                                               sender_name: String,
                                               first_name: String,
                                               last_name: String,
                                               email_type: EmailType,
                                               signature: Option[String],
                                               account_id: Long,
                                               error: Option[String],
                                               paused_till: Option[LocalDateTime]
                                             )

object EmailAccountForCreateEmailSchedule {
  def fromDb(rs: WrappedResultSet): EmailAccountForCreateEmailSchedule = {
    val senderName = rs.string("sender_name").trim
    val firstName = rs.string("first_name").trim
    val lastName = rs.string("last_name").trim

    val emailType: EmailType =
      rs.stringOpt("email_type")
        .map(et => EmailType.fromKey(key = et).get)
        .getOrElse(EmailType.Sales) // default

    EmailAccountForCreateEmailSchedule(
      id = rs.long("id"),
      email = rs.string("email"),
      email_domain = rs.string("email_domain"),
      sender_name = senderName,
      first_name = firstName,
      last_name = lastName,
      email_type = emailType,
      signature = rs.stringOpt("signature"),
      account_id = rs.long("account_id"),
      error = rs.stringOpt("error"),
      paused_till = rs.localDateTimeOpt("paused_till")
    )
  }
}

object EmailAccount {

  implicit val writes: Writes[EmailAccount] = new Writes[EmailAccount] {

    def writes(ea: EmailAccount) = {

      val commonFields = Json.obj(
        "id" -> ea.id,
        "email" -> ea.email,

        "sender_name" -> ea.sender_name,
        "first_name" -> ea.first_name,
        "last_name" -> ea.last_name,

        "email_type" -> ea.email_type,

        "signature" -> ea.signature,
        "active" -> ea.active,

        "emails_per_day" -> ea.warm_up_emails_per_day,
        "emails_increase_per_day" -> ea.warm_up_emails_increase_per_day,

        "basic_email_health_check_details" -> ea.basic_email_health_check_details,

        "status" -> ea.status,
        "error" -> ea.error,
        "raw_error" -> ea.raw_error,
        "created_at" -> ea.created_at,
      )

      ea.settings match {

        case s: EmailSettings.SmtpImapSettings =>

          commonFields ++ Json.obj(
            "service_provider_info" -> Json.obj(

              "service_provider" -> ea.service_provider,

              "password" ->  "",// 4-8-2023 : Avoiding passwords to frontend   ; s.smtp_settings.smtp_password, // TODO: This field is not getting used in FrontEnd.

              "smtp_username" -> s.smtp_settings.smtp_username,
              "smtp_password" -> "",// 4-8-2023 : Avoiding passwords to frontend
              "smtp_host" -> s.smtp_settings.smtp_host,
              "smtp_port" -> s.smtp_settings.smtp_port,

              "imap_username" -> s.imap_settings.imap_username,
              "imap_password" -> "",// 4-8-2023 : Avoiding passwords to frontend
              "imap_host" -> s.imap_settings.imap_host,
              "imap_port" -> s.imap_settings.imap_port,
            )
          )

        case _: EmailSettings.OAuthTokens =>

          // Don't send OAUTH tokens to FrontEnd.
          commonFields ++ Json.obj(
            "service_provider_info" -> Json.obj(

              "service_provider" -> ea.service_provider,
            )
          )
      }
    }
  }

  def fromDbWithStats(rs: WrappedResultSet): EmailAccountBasicWithStats = {

    val active = rs.boolean("active");
    val error = rs.stringOpt("error");
    val status = if(active && error.isDefined) EmailAccountStatus.PAUSED else if(active) EmailAccountStatus.RUNNING else EmailAccountStatus.STOPPED

    val emailHealthCheckStatusOpt = rs.stringOpt("email_health_check_status")
      .map(EmailHealthCheckStatus.fromKey(_).get)

    val basicEmailHealthCheckRecordOpt = emailHealthCheckStatusOpt.map { emailHealthCheckStatus =>

      // If email health check status is present then other fields should also be present.

      BasicEmailHealthCheckRecord(
        spf_auth_status = EmailAuthStatus.fromKey(key = rs.string("spf_auth_status")),
        dkim_auth_status = EmailAuthStatus.fromKey(key = rs.string("dkim_auth_status")),
        dmarc_auth_status = EmailAuthStatus.fromKey(key = rs.string("dmarc_auth_status")),
        status = emailHealthCheckStatus,
      )

    }

    EmailAccountBasicWithStats(
      id = rs.long("id"),
      email = rs.string("email"),
      service_provider = EmailServiceProvider.withName(rs.string("service_provider")).get,
      status = status,
      error = error,
      total_sent_today = rs.int("total_sent_today"),
      total_landed_in_inbox_today = rs.int("total_landed_in_inbox_today"),
      total_landed_in_spam_today = rs.int("total_landed_in_spam_today"),
      total_landed_in_categories_today = rs.int("total_landed_in_categories_today"),
      total_landing_check_failed_today = rs.int("total_landing_check_failed_today"),
      basic_email_health_check_details = basicEmailHealthCheckRecordOpt,
      total_bounced_today = rs.int("total_bounced_today"),
      created_at = rs.offsetDateTime("created_at")
    )

  }

  def startAndEndOfToday = {
    FromTill(
      from = LocalDate.now().atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli / 1000,
      till = LocalDate.now().atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli / 1000,
    )
  }

  def markReceiverLimitReachedForReply(
    emailScheduledId: Long,
    senderEmailAccountId: Long,
  ): Try[Unit] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE emails_scheduled
          SET
            receiver_limit_reached_for_reply = TRUE
          WHERE
            id = $emailScheduledId
            AND sender_email_account_id = $senderEmailAccountId
        """
        .update()
        .apply()

    }

  }

  def findAll(accountId: Long): Try[Seq[EmailAccountBasicWithStats]] = DB readOnly { implicit session =>

    Try {

      val ft = startAndEndOfToday

      val from = ft.from
      val till = ft.till

      DB readOnly { implicit session =>

        sql"""
          SELECT
            ea.id,
            ea.email,
            ea.service_provider,
            ea.active,
            ea.error,
            ea.created_at,

            ehcr.spf_auth_status,
            ehcr.dkim_auth_status,
            ehcr.dmarc_auth_status,
            ehcr.status AS email_health_check_status,

            -- only non replies emails will be counted in stats
            -- 5 feb 2025 - consider both replies and non-replies
            COUNT(ea.*) FILTER (WHERE es.sent) AS total_sent_today,
            COUNT(ea.*) FILTER (WHERE es.landed_folder_type IS NOT NULL AND es.landed_folder_type = ${FolderType.INBOX.toString}::folder_type_enum) AS total_landed_in_inbox_today,
            COUNT(ea.*) FILTER (WHERE es.landed_folder_type IS NOT NULL AND es.landed_folder_type = ${FolderType.SPAM.toString}::folder_type_enum) AS total_landed_in_spam_today,
            COUNT(ea.*) FILTER (WHERE es.landed_folder_type IS NOT NULL AND es.landed_folder_type = ${FolderType.LANDING_CHECK_FAILED.toString}::folder_type_enum) AS total_landing_check_failed_today,
            COUNT(ea.*) FILTER (WHERE es.landed_folder_type IS NOT NULL AND es.landed_folder_type = ${FolderType.BOUNCED.toString}::folder_type_enum) AS total_bounced_today,
            COUNT(ea.*) FILTER (WHERE es.landed_folder_type IS NOT NULL AND es.landed_folder_type = ${FolderType.CATEGORY.toString}::folder_type_enum) AS total_landed_in_categories_today
          FROM email_accounts ea
          LEFT JOIN email_health_check_records ehcr ON
            ehcr.email_account_id = ea.id
            AND ehcr.account_id = ea.account_id
          LEFT JOIN emails_scheduled es ON es.sent = TRUE
            -- AND es.is_reply = FALSE
            AND es.sender_email_account_id = ea.id
            AND es.sent_at >= to_timestamp($from)
            AND es.sent_at < to_timestamp($till)
          WHERE ea.account_id = $accountId
           GROUP BY ea.id,
            ehcr.spf_auth_status,
            ehcr.dkim_auth_status,
            ehcr.dmarc_auth_status,
            ehcr.status
           ORDER BY ea.active DESC
       """
          .map(fromDbWithStats)
          .list()
          .apply()
      }
    }

  }

  def isEmailAccountInDB(
    email: String,
    serviceProvider: EmailServiceProvider
  ): Try[Option[BasicEmailAccountDetails]] = Try {

    DB readOnly { implicit session =>

      /**
        * 30-Mar-2024
        *
        * User should not able to integrate an email account,
        * if same email with same service provider is already integrated
        * into their or some other user account.
        *
        * We have a unique index on (account_id, email, service_provider)
        *
        * We will check only email and service provider, as we don't want
        * same emails integrated in two different user accounts.
        */

      sql"""
         SELECT
           ea.id,
           ea.account_id
         FROM
           email_accounts ea
         WHERE
           ea.email = $email
           AND ea.service_provider = ${serviceProvider.toString}
        """
        .map { rs =>

          BasicEmailAccountDetails(
            emailAccountId = rs.long("id"),
            accountIdOpt = rs.longOpt("account_id")
          )

        }
        .single()
        .apply()

    }

  }

  def updateAccessTokenAndRefreshToken(
                                        emailAccountId: Long,
                                        data: EmailSettingUpdateAccessToken
                                      ): Try[Option[EmailSettingId]] = Try {

    // We are subtracting 5 minutes from access token expiration time, as in other places
    // we first check if access token has expired before actually making the API call to refresh the access token.

    // So, subtracting 5 minutes acts as a buffer and we at least have 5 minutes before the access token expires.

    val accessTokenExpiresAt = data.oauth2_access_token_expires_at.minusMinutes(5)

    val fiveMinutesInSeconds = 5 * 60

    val accessTokenExpiresIn = data.oauth2_token_expires_in - fiveMinutesInSeconds

    val esId = DB localTx { implicit session =>
      sql"""
        UPDATE email_accounts
        SET
          oauth2_access_token_enc = ${EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey, data.oauth2_access_token)},
          oauth2_access_token_expires_at = $accessTokenExpiresAt,
          oauth2_access_token_expires_in = $accessTokenExpiresIn,
          oauth2_refresh_token_enc = ${EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey, data.oauth2_refresh_token)},
          oauth2_access_token_updated_at = ${DateTime.now()}

        WHERE id = ${emailAccountId}
        RETURNING id;
      """
        .map(_.long("id"))
        .single()
        .apply()

    }
    esId.map(id => EmailSettingId(id))
  }

  def createEmailOAuthAccount(accountId: Long, data: EmailOAuthAccountForm, emailDomain: String)(implicit Logger: SRLogger): Try[Option[EmailAccount]] = {

    val tryOfEaIdOpt = Try {

      DB localTx { implicit session =>

      val firstName = data.first_name.trim
      val lastName = data.last_name.trim
      val senderName = if (firstName.nonEmpty && lastName.nonEmpty) s"$firstName $lastName" else firstName
      val isActive = true

      val email = data.email.trim.toLowerCase

      val oauth2_access_token = EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey,data.oauth2_access_token)
      val oauth2_refresh_token = EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey,data.oauth2_refresh_token)

      val oauth2_token_type = data.oauth2_token_type
      val oauth2_token_expires_in = data.oauth2_token_expires_in
      val oauth2_access_token_updated_at = DateTime.now()
      val oauth2_access_token_expires_at = data.oauth2_access_token_expires_at

      val serviceProvider = data.service_provider.toString
      val msgIdSuffix = EmailHelper.genMessageIdSuffix()


      sql"""
          INSERT INTO email_accounts
          (
           account_id,
           email,
           service_provider,
           sender_name,
           first_name,
           last_name,

           oauth2_access_token_enc,
           oauth2_refresh_token_enc,
           oauth2_token_type,
           oauth2_access_token_expires_in,
           oauth2_access_token_updated_at,
           oauth2_access_token_expires_at,

           warm_up_emails_per_day,

           active,
           email_domain,
           message_id_suffix,
           ms_client_id_version
          )
          VALUES (
            $accountId,
            $email,
            $serviceProvider,
            $senderName,
            $firstName,
            $lastName,


            $oauth2_access_token,
            $oauth2_refresh_token,
            $oauth2_token_type,
            $oauth2_token_expires_in,
            $oauth2_access_token_updated_at,
            $oauth2_access_token_expires_at,

            ${AppConfig.defaultWarmUpEmailsPerDay},

            $isActive,
            $emailDomain,
            $msgIdSuffix,
            ${MsClientIdVersion.V2.toString} -- use new client ID for new accounts
          )
          RETURNING id;
        """
        .map(_.long("id"))
        .single()
        .apply()

      }
    }

    tryOfEaIdOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Success(None)

      case Success(Some(eaId)) =>
        find(id = eaId).map(emailAccount => Some(emailAccount))

    }
  }


  def createEmailAccount(
    accountId: Long,
    data: EmailAccountForm,
    emailDomain: String,
    isPurchasedFromSr: Boolean,
  )(implicit Logger: SRLogger): Try[Option[EmailAccount]] = {

    val tryOfEaIdOpt = Try {

      DB localTx { implicit session =>

      val firstName = data.first_name.trim
      val lastName = data.last_name.trim
      val senderName = if (firstName.nonEmpty && lastName.nonEmpty) firstName + " " + lastName else firstName
      val isActive = true

      /* doing trim and toLower all login and host details */
      val email = data.email.trim.toLowerCase

      val smtpUsername = data.smtp_username.trim.toLowerCase

      val smtpPassword = EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey, data.smtp_password.trim)

      val smtpHost = data.smtp_host.trim.toLowerCase

      val imapUsername = data.imap_username.trim.toLowerCase

      val imapPassword = EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey, data.imap_password.trim)

      val imapHost = data.imap_host.trim.toLowerCase

      val serviceProvider = data.service_provider.toString

      val smtp_enable_ssl = true

      val imap_enable_ssl = true

      val msgIdSuffix = EmailHelper.genMessageIdSuffix()

      sql"""
          INSERT INTO email_accounts
          (
           account_id,
           email,
           service_provider,
           sender_name,
           first_name,
           last_name,

           smtp_username,
           smtp_password,
           smtp_host,
           smtp_port,
           smtp_enable_ssl,

           imap_username,
           imap_password,
           imap_host,
           imap_port,
           imap_enable_ssl,

           warm_up_emails_per_day,

           is_purchased_from_sr,

           active,
           email_domain,
           message_id_suffix
          )
          VALUES (
            $accountId,
            $email,
            $serviceProvider,
            $senderName,
            $firstName,
            $lastName,

            $smtpUsername,
            $smtpPassword,
            $smtpHost,
            ${data.smtp_port},
            $smtp_enable_ssl,

            $imapUsername,
            $imapPassword,
            $imapHost,
            ${data.imap_port},
            $imap_enable_ssl,

            ${AppConfig.defaultWarmUpEmailsPerDay},

            $isPurchasedFromSr,

            $isActive,
            $emailDomain,
            $msgIdSuffix
          )
          RETURNING id;
        """
          .map(_.long("id"))
          .single()
          .apply()

      }

    }

    tryOfEaIdOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Success(None)

      case Success(Some(eaId)) =>
        find(id = eaId).map(emailAccount => Some(emailAccount))

    }
  }


  def update(id: Int, data: EmailAccountForm): Try[Option[EmailAccount]] = {

    val tryOfEaIdOpt = Try {

      DB autoCommit { implicit session =>


      val firstName = data.first_name.trim
      val lastName = data.last_name.trim
      val senderName = if (firstName.nonEmpty && lastName.nonEmpty) firstName + " " + lastName else firstName

      val smtpPassword = EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey, data.smtp_password.trim)

      val imapPassword = EncryptionService.encrypt(AppConfig.emailAccountEncryptionKey, data.imap_password.trim)

      val smtpHost = data.smtp_host.trim.toLowerCase

      val imapHost = data.imap_host.trim.toLowerCase

      sql"""
          UPDATE email_accounts
          SET

            sender_name = $senderName,
            first_name = $firstName,
            last_name = $lastName,

            smtp_password = $smtpPassword,
            smtp_host = $smtpHost,
            smtp_port = ${data.smtp_port},

            imap_password = $imapPassword,
            imap_host = $imapHost,
            imap_port = ${data.imap_port},

            error = null,
            error_reported_at = null,
            paused_till = null,
            auth_failed_count = 0

          WHERE id = $id
          RETURNING id;
        """
        .map(_.long("id"))
        .single()
        .apply()

      }

    }

    tryOfEaIdOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Success(None)

      case Success(Some(eaId)) =>
        find(id = eaId).map(emailAccount => Some(emailAccount))

    }
  }

  def updateBasicDetails(id: Int, data: BasicAccountForm): Try[Option[EmailAccount]] = {

    val tryOfEaIdOpt: Try[Option[Long]] = Try {

      DB autoCommit { implicit session =>

      val firstName = data.first_name.trim
      val lastName = data.last_name.trim
      val senderName = if (firstName.nonEmpty && lastName.nonEmpty) firstName + " " + lastName else firstName

      sql"""
            UPDATE email_accounts
            SET

              sender_name = $senderName,
              first_name = $firstName,
              last_name = $lastName,
              email_type = ${data.email_type.toString},

              error = null,
              error_reported_at = null,
              paused_till = null,
              auth_failed_count = 0

            WHERE id = $id
            RETURNING id;
          """
        .map(_.long("id"))
        .single()
        .apply()

    }

    }

    tryOfEaIdOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Success(None)

      case Success(Some(eaId)) =>
        find(id = eaId).map(emailAccount => Some(emailAccount))

    }
  }

  def delete(emailAccountId: Long): Try[Int] = Try {
    DB.autoCommit { implicit session =>
      sql"DELETE FROM email_accounts where id = $emailAccountId".update().apply()
    }
  }


  def fetchAllActiveEmailAccountsForSchedulingSend(): Try[Seq[EmailAccountForScheduling]] = Try {

    val waitForMinutesBeforeNextSchedule = AppConfig.waitForMinutesBeforeNextSchedule

    val waitForMinutesBeforeNextScheduleSql = SQLSyntax.createUnsafely(waitForMinutesBeforeNextSchedule.toString)

    /**
      * 29 Aug 2024
      *
      * A customer raised an issue where the number of emails
      * sent from his email was more than the specified limit.
      *
      * It was because we used to use the entire limit
      * for both scheduling new emails and replies.
      *
      * Now, we will divide the specified limit in half and use 50% for new email and 50% for replies.
      *
      * So, dividing the limit by 2 and doing a ceil on it to avoid decimal numbers.
      *
      * As we are doing a ceil it can result into 1 extra email sent in both new and reply.
      */

    DB autoCommit { implicit session =>

      val query =
        sql"""

                 UPDATE email_accounts es_rows SET
                   in_queue_for_scheduling = TRUE,
                   pushed_to_queue_for_scheduling_at = now()

                 FROM
                    (
                       SELECT DISTINCT ON (ea.id)
                         ea.id, ea.email, ea.account_id
                       FROM
                        email_accounts ea
                        JOIN accounts AS acc ON ea.account_id = acc.id

                       WHERE ea.active
                         AND (acc.account_paused IS NULL OR acc.account_paused = FALSE)
                         AND (ea.paused_till IS NULL OR ea.paused_till < now())
                         --not check against error, checking on paused_till
                         --AND ea.error IS NULL
                         --every 15 minutes it will go for sending queue
                         --OR if receiver not found after 1 hour it will check again
                         AND ((ea.in_queue_for_scheduling = FALSE AND (ea.pushed_to_queue_for_scheduling_at IS NULL OR ea.pushed_to_queue_for_scheduling_at < now() - interval '15 minutes')) OR ea.pushed_to_queue_for_scheduling_at < now() - interval '1 hours')
                         --15 minutes after latest_email_scheduled_at it will fetch for sending queue
                         AND (ea.latest_email_scheduled_at IS NULL OR ea.latest_email_scheduled_at < now() - interval '$waitForMinutesBeforeNextScheduleSql minutes')
                         AND (SELECT count(*) as count
                              FROM emails_scheduled es
                              WHERE es.sender_email_account_id = ea.id AND
                                    es.is_reply = FALSE AND
                                    ( es.scheduled_at >= current_date::timestamp AT TIME ZONE 'UTC' OR
                                      es.sent_at >= current_date::timestamp AT TIME ZONE 'UTC' )) < (SELECT ceiling((least(ea.warm_up_emails_per_day, greatest(ea.last_used_max_sends_per_day, ea.warm_up_emails_increase_per_day) + ea.warm_up_emails_increase_per_day * (now()::date - (ea.warm_up_emails_increase_per_day_updated_at)::date))) / 2))
                         ORDER BY ea.id, ea.pushed_to_queue_for_scheduling_at ASC

                    ) filtered_email_account_rows

                 WHERE es_rows.id = filtered_email_account_rows.id
                 RETURNING filtered_email_account_rows.*

               ;
             """


      query
        .map(rs => EmailAccountForScheduling(
          id = EmailAccountId(id = rs.int("id")),
          sender_email = rs.string("email"),
          account_id = AccountId(id = rs.long("account_id")),
        ))
        .list()
        .apply()

    }
  }


  def findForScheduling(id: Long): Try[Option[EmailAccountForCreateEmailSchedule]] = blocking {

    Try {
      DB readOnly { implicit session =>

        sql"""
        SELECT
          ea.id,
          ea.email,
          ea.email_domain,
          ea.sender_name,
          ea.first_name,
          ea.last_name,
          ea.email_type,
          ea.signature,
          ea.account_id,
          ea.error,
          ea.paused_till
        FROM email_accounts ea
        WHERE ea.id = $id AND ea.active
      """
          .map(EmailAccountForCreateEmailSchedule.fromDb)
          .single()
          .apply()

      }
    }
  }


  def _updateLastScheduled(emailAccountId: Long, latestEmailScheduledAt: Option[DateTime],
                           Logger: SRLogger
                          ): Int = {
    blocking {
      DB autoCommit { implicit session =>
        Logger.info(s"_updateLastScheduled in_queue_for_scheduling: $emailAccountId :: $latestEmailScheduledAt")

        if (latestEmailScheduledAt.isEmpty) {

          sql"""
          UPDATE email_accounts
           SET in_queue_for_scheduling = false
          WHERE id = $emailAccountId
        """
            .update()
            .apply()
        } else {

          sql"""
          UPDATE email_accounts
            SET in_queue_for_scheduling = false,
              latest_email_scheduled_at = ${latestEmailScheduledAt.get}
          WHERE id = $emailAccountId
        """
            .update()
            .apply()

        }

      }
    }
  }

  def getEmailAccountDailyLimit(
    emailAccountId: Long,
    accountId: Long,
  ): Try[Option[Int]] = Try {

    DB.readOnly { implicit session =>

      sql"""
          SELECT
            	ceiling((least(ea.warm_up_emails_per_day, greatest(ea.last_used_max_sends_per_day, ea.warm_up_emails_increase_per_day) + ea.warm_up_emails_increase_per_day * (now()::date - (ea.warm_up_emails_increase_per_day_updated_at)::date))) / 2) AS daily_send_limit
          FROM
            email_accounts AS ea
          WHERE
            ea.id = $emailAccountId
            AND ea.account_id = $accountId
         """
        .map { rs =>

          rs.int("daily_send_limit")

        }
        .single()
        .apply()

    }

  }


  def findReceiverEmailAccountForScheduling(email_domain: String, senderEmailAccountId: Long): Try[Option[EmailAccountForCreateEmailSchedule]] = Try {

    /**
      * 29 Aug 2024
      *
      * A customer raised an issue where the number of emails
      * sent from his email was more than the specified limit.
      *
      * It was because we used to use the entire limit
      * for both scheduling new emails and replies.
      *
      * Now, we will divide the specified limit in half and use 50% for new email and 50% for replies.
      *
      * So, dividing the limit by 2 and doing a ceil on it to avoid decimal numbers.
      *
      * As we are doing a ceil it can result into 1 extra email sent in both new and reply.
      *
      *
      * 19 Jul 2025
      *
      * (
      * SELECT COUNT(*) -- order by the count of sends in the last 5 days
      * FROM emails_scheduled es_recent_sends_count
      * WHERE es_recent_sends_count.sender_email_account_id = $senderEmailAccountId
      * AND es_recent_sends_count.receiver_email_account_id = ea.id
      * AND es_recent_sends_count.is_reply = FALSE
      * AND (
      * es_recent_sends_count.sent_at >= NOW() - INTERVAL '5 days'
      * OR es_recent_sends_count.scheduled_at >= NOW() - INTERVAL '5 days'
      * )
      * ) AS recent_sent_count
      *
      * This ensures that we keep picking receiver who we have not sent to in the last 5 days.
      * If we have sent to all receivers in the last 5 days,
      * then we will start sending to the receivers who have sent the least number of emails.
      */

    DB readOnly { implicit session => {
      sql"""
          SELECT ea.id,
           count(es.*) as prev_sent_count,
           ea.email,
           ea.sender_name,
           ea.first_name,
           ea.last_name,
           ea.email_type,
           ea.signature,
           ea.account_id,
           ea.email_domain,
           ea.error,
           ea.paused_till,

           (
              SELECT COUNT(*) -- order by the count of sends in the last 5 days
              FROM emails_scheduled es_recent_sends_count
              WHERE es_recent_sends_count.sender_email_account_id = $senderEmailAccountId
                AND es_recent_sends_count.receiver_email_account_id = ea.id
                AND es_recent_sends_count.is_reply = FALSE
                AND (
                  es_recent_sends_count.sent_at >= NOW() - INTERVAL '5 days'
                  OR es_recent_sends_count.scheduled_at >= NOW() - INTERVAL '5 days'
                )
           ) AS recent_sent_count

          FROM email_accounts ea
          --with left join fetching all matched rows and taking it by count 24 hours clause should come in JOIN
          --and it should't pick paused(errored) email account
          JOIN accounts AS acc ON ea.account_id = acc.id
          LEFT JOIN emails_scheduled es ON es.sender_email_account_id = $senderEmailAccountId
            AND es.receiver_email_account_id = ea.id
            AND es.is_reply = FALSE
            AND es.sent_at >= current_date::timestamp AT TIME ZONE 'UTC'
          --while selecting receiver email, do not give preference to emails from the same business domain insted of same account
          WHERE lower(ea.email_domain) != $email_domain
            AND (acc.account_paused IS NULL OR acc.account_paused = FALSE)
            AND ea.active = TRUE
            AND ea.error IS NULL
            -- 30 is max warm up email that can be sent
            -- 2 is the increment per day initially, so 2 emails on first day, 4 on second day and so on
            AND (SELECT count(*) as count
                 FROM emails_scheduled es
                 WHERE es.sender_email_account_id = ea.id AND
                       es.is_reply = TRUE AND
                       ( es.scheduled_at >= current_date::timestamp AT TIME ZONE 'UTC' OR
                         es.sent_at >= current_date::timestamp AT TIME ZONE 'UTC' )) < (SELECT ceiling((least(ea.warm_up_emails_per_day, greatest(ea.last_used_max_sends_per_day, ea.warm_up_emails_increase_per_day) + ea.warm_up_emails_increase_per_day * (now()::date - (ea.warm_up_emails_increase_per_day_updated_at)::date))) / 2))
          GROUP BY ea.id
          --ORDER BY prev_sent_count ASC
          --ORDER BY random()
          ORDER BY
              recent_sent_count ASC,
              prev_sent_count ASC, -- Then, prefer those with fewer sends today
              random() -- Finally, randomize if prev_sent_count is also the same
          LIMIT 1
       """
        .map(EmailAccountForCreateEmailSchedule.fromDb)
        .headOption()
        .apply()
    }
    }
  }


  def find(id: Long): Try[EmailAccount] = {

    val tryOfEmailAccountOpt: Try[Option[EmailAccount]] = __findEmailAccountSQL(
      whereClause = sqls" WHERE ea.id = $id "
    )
      .map(_.headOption)

    tryOfEmailAccountOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Failure(new Exception(s"Email Account not found. id: $id"))

      case Success(Some(emailAccount: EmailAccount)) =>
        Success(emailAccount)

    }
  }

  private def findEmailAccountWarmupStatusInternal(
    apiKey: String,
    accountId: Long,
    emails: List[String],
  ): Try[List[EmailAccountWarmupStatus]] = Try {

    if (emails.isEmpty) {

      List()

    } else {

      DB.readOnly { implicit session =>

        sql"""
             SELECT
                ea.email,
                ea.service_provider,
                ea.active,
                ea.error
             FROM
                email_accounts AS ea
                JOIN accounts AS acc ON ea.account_id = acc.id
             WHERE
                acc.sr_api_key = $apiKey
                AND acc.id = $accountId
                AND ea.email IN ${SQLUtils.generateSQLValuesClause(arr = emails.map(_.trim))}
           """
          .map { rs =>

            val active = rs.boolean("active")
            val error = rs.stringOpt("error")

            val status = if (active && error.isDefined) {
              EmailAccountStatus.PAUSED
            } else if (active) {
              EmailAccountStatus.RUNNING
            } else {
              EmailAccountStatus.STOPPED
            }

            val service_provider = EmailServiceProvider.withName(rs.string("service_provider")).get

            EmailAccountWarmupStatus(
              email = rs.string("email"),
              service_provider = service_provider,
              status = status,
            )

          }
          .list()
          .apply()
      }

    }

  }

  def findEmailAccountWarmupStatus(
    apiKey: String,
    accountId: Long,
    emails: List[String],
  ): Try[List[EmailAccountWarmupStatus]] = {

    Helpers.seqTryToTrySeq {

      emails.grouped(80).map { emailsGrp =>

        findEmailAccountWarmupStatusInternal(
          apiKey = apiKey, accountId = accountId, emails = emailsGrp,
        )

      }.toList

    }.map(_.flatten.toList)

  }

  def findAllByEmail(
    accountId: Long,
    emails: List[String]
  ): Try[List[EmailAccount]] = {

    if (emails.isEmpty) {

      Success(List())

    } else {

      __findEmailAccountSQL(
        whereClause =
          sqls"""
                WHERE
                  ea.email IN ${SQLUtils.generateSQLValuesClause(arr = emails)}
                  AND ea.account_id = $accountId
              """
      )
        .map(_.toList)

    }

  }

  def findEmailAccounts(
    accountId: Long,
    emailAccountIds: List[Long],
  ): Try[List[EmailAccount]] = {

    __findEmailAccountSQL(
      whereClause =
        sqls"""
              WHERE
                ea.id IN ${SQLUtils.generateSQLValuesClause(arr = emailAccountIds)}
                AND ea.account_id = $accountId
            """
    ).map(_.toList)

  }

  def __findEmailAccountSQL(whereClause: SQLSyntax): Try[Seq[EmailAccount]] = blocking {

    Try {

    DB readOnly { implicit session =>

      sql"""
        SELECT ea.*,
          ehcr.spf_auth_status,
          ehcr.dkim_auth_status,
          ehcr.dmarc_auth_status,
          ehcr.status AS email_health_check_status
        FROM email_accounts ea
        LEFT JOIN email_health_check_records ehcr ON
          ehcr.email_account_id = ea.id
          AND ehcr.account_id = ea.account_id
        $whereClause
       """
        .map(rs => {

          val active = rs.boolean("active");
          val error = rs.stringOpt("error");
          val raw_error = rs.stringOpt("raw_error");
          val status = if (active && error.isDefined) EmailAccountStatus.PAUSED else if (active) EmailAccountStatus.RUNNING else EmailAccountStatus.STOPPED

          val key = AppConfig.emailAccountEncryptionKey

          val service_provider = EmailServiceProvider.withName(rs.string("service_provider")).get

          // Here we are assuming that either SMTP/IMAP or OAUTH related fields will be present in th DB.

          val emailType: EmailType =
            rs.stringOpt("email_type")
              .map(et => EmailType.fromKey(key = et).get)
              .getOrElse(EmailType.Sales) // default



          val emailHealthCheckStatusOpt = rs.stringOpt("email_health_check_status")
            .map(EmailHealthCheckStatus.fromKey(_).get)


          val basicEmailHealthCheckRecordOpt = emailHealthCheckStatusOpt.map { emailHealthCheckStatus =>

            // If email health check status is present then other fields should also be present.

            BasicEmailHealthCheckRecord(
              spf_auth_status = EmailAuthStatus.fromKey(key = rs.string("spf_auth_status")),
              dkim_auth_status = EmailAuthStatus.fromKey(key = rs.string("dkim_auth_status")),
              dmarc_auth_status = EmailAuthStatus.fromKey(key = rs.string("dmarc_auth_status")),
              status = emailHealthCheckStatus,
            )

          }

          val settings: EmailSettings = service_provider match {

            case api.models.EmailServiceProvider.GOOGLE_WORKSPACE
                 | api.models.EmailServiceProvider.MICROSOFT_365
                 | api.models.EmailServiceProvider.OTHER =>

              val smtp_username = rs.string("smtp_username")
              val smtp_password = rs.string("smtp_password")
              val smtp_host = rs.string("smtp_host")
              val smtp_port = rs.int("smtp_port")

              val imap_username = rs.string("imap_username")
              val imap_password = rs.string("imap_password")
              val imap_host = rs.string("imap_host")
              val imap_port = rs.int("imap_port")

              val decryptedSMTPPassword = EncryptionService.decrypt(key, smtp_password)

              val decryptedIMAPPassword = EncryptionService.decrypt(key, imap_password)

              val smtp_settings = EmailSettings.SmtpEmailAccount(
                smtp_username = smtp_username,
                smtp_password = decryptedSMTPPassword,
                smtp_host = smtp_host,
                smtp_port = smtp_port
              )

              val imap_settings = EmailSettings.ImapEmailAccount(
                imap_username = imap_username,
                imap_password = decryptedIMAPPassword,
                imap_host = imap_host,
                imap_port = imap_port
              )

              EmailSettings.SmtpImapSettings(
                smtp_settings = smtp_settings, imap_settings = imap_settings
              )

            case api.models.EmailServiceProvider.MICROSOFT_365_API =>

              val oauth2_refresh_token = EncryptionService.decrypt(
                key = key, encryptedValue = rs.string("oauth2_refresh_token_enc")
              )

              val oauth2_access_token = EncryptionService.decrypt(
                key = key, encryptedValue = rs.string("oauth2_access_token_enc")
              )

              val oauth2_access_token_expires_at = rs.jodaDateTime("oauth2_access_token_expires_at")

              EmailSettings.OAuthTokens(
                oauth2_refresh_token = oauth2_refresh_token,
                oauth2_access_token = oauth2_access_token,
                oauth2_access_token_expires_at = oauth2_access_token_expires_at
              )
          }

          val msClientIdVersion = rs.stringOpt("ms_client_id_version")
            .map(v => MsClientIdVersion.fromKey(key = v).get)
            .getOrElse(MsClientIdVersion.V1) // If client id version is not present, then default to V1.

          EmailAccount(
            id = rs.long("id"),

            account_id = rs.long("account_id"),

            email = rs.string("email"),

            service_provider = service_provider,

            sender_name = rs.string("sender_name"),
            first_name = rs.string("first_name"),
            last_name = rs.string("last_name"),

            email_type = emailType,

            signature = rs.stringOpt("signature"),

            active = rs.boolean("active"),

            warm_up_emails_per_day = rs.int("warm_up_emails_per_day"),
            warm_up_emails_increase_per_day = rs.int("warm_up_emails_increase_per_day"),

            message_id_suffix = rs.string("message_id_suffix"),

            settings = settings,

            basic_email_health_check_details = basicEmailHealthCheckRecordOpt,

              status = status,
              error = error,
              raw_error = raw_error,
              error_reported_at = rs.localDateTimeOpt("error_reported_at"),
              paused_till = rs.localDateTimeOpt("paused_till"),
              created_at = rs.localDateTime("created_at"),
              ms_client_id_version = msClientIdVersion,
            )
          })
          .list()
          .apply()
      }
    }
  }

  def getImapEmailAccount(emailAccount: EmailAccount): Try[EmailSettings.ImapEmailAccount] = Try {
    emailAccount.settings match {
      case s: EmailSettings.SmtpImapSettings =>
        s.imap_settings

      case _: EmailSettings.OAuthTokens =>
        throw new Exception("IMAP settings do not exist for this email account.")
    }
  }

  def getImapEmailAccount(emailSetting: EmailAccountForm) = Try {
    EmailSettings.ImapEmailAccount(
      imap_host = emailSetting.imap_host.trim,
      imap_port = emailSetting.imap_port,
      imap_username = emailSetting.imap_username.trim,
      imap_password = emailSetting.imap_password.trim
    )
  }

  def getSMTPEmailAccount(emailAccount: EmailAccountForm) = Try {
    EmailSettings.SmtpEmailAccount(
      smtp_host = emailAccount.smtp_host.trim,
      smtp_port = emailAccount.smtp_port,
      smtp_username = emailAccount.smtp_username.trim,
      smtp_password = emailAccount.smtp_password.trim
    )
  }


  def addError(
                emailAccountId: Long,
                error: String,
                rawErrorOpt: Option[String],
                errorReportedAt: LocalDateTime,
                pausedTill: LocalDateTime
              ): Try[Option[EmailAccount]] = blocking {

    val tryOfEaIdOpt = Try {

      DB localTx { implicit session =>

        sql"""
          UPDATE email_accounts
          SET
            error = $error,
            raw_error = $rawErrorOpt,
            error_reported_at = $errorReportedAt,
            paused_till = $pausedTill

          WHERE id = $emailAccountId
          RETURNING id;
        """
          .map(_.long("id"))
          .single()
          .apply()

      }

    }

    tryOfEaIdOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Success(None)

      case Success(Some(eaId)) =>
        find(id = eaId).map(emailAccount => Some(emailAccount))

    }
  }

  def incrementLandingCheckFailedCount(emailAccountId: Long): Try[Option[EmailAccount]] = {

    val tryOfEaIdOpt = Try {

      DB autoCommit { implicit session =>

        sql"""
          UPDATE
            email_accounts
          SET
            landing_check_failed_count = landing_check_failed_count + 1
          WHERE
            id = $emailAccountId
          RETURNING
            id;
         """
          .map(_.long("id"))
          .single()
          .apply()

      }
    }

    tryOfEaIdOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Success(None)

      case Success(Some(eaId)) =>
        find(id = eaId).map(emailAccount => Some(emailAccount))
    }
  }

  def getLandingCheckFailedCount(emailAccountId: Long): Try[Option[Int]] = Try {

    DB readOnly  { implicit session =>

      sql"""
          SELECT
            landing_check_failed_count
          FROM
            email_accounts
          WHERE
            id = $emailAccountId
         """
        .map { rs =>
          rs.int("landing_check_failed_count")
        }
        .single()
        .apply()
    }
  }

  def getEmailAccountAuthenticationFailedCount(emailAccountId: Long): Try[Option[Int]] = Try {

    DB autoCommit { implicit session =>
      sql"""
            UPDATE email_accounts
            SET
              auth_failed_count = auth_failed_count + 1
            WHERE id = $emailAccountId
            RETURNING auth_failed_count;
        """
        .map(rs => rs.int("auth_failed_count"))
        .single()
        .apply()
    }
  }


  def getTestEmail(
                    data: EmailTestForm,
                    accountEmail: String
                  ) = {
    val subject = s"Test Email From ${CONSTANTS.APP_NAME}"
    val emailBody = s"Congrats! Your email account ${data.email} for email warmup is properly configured. Thanks!"
    EmailToBeSent(

      to_emails = Seq(IEmailAddress(
        email = accountEmail,
        name = None // dont need for integration test email
      )),
      from_email = data.email,
      from_name = Helpers.getSenderName(data),
      subject = subject,
      body = emailBody,
      text_body = emailBody,

      message_id = None,

      sender_email_settings_id = 0,
      is_reply = false,

      in_reply_to_header = None,
      references_header = None

    )
  }

  def updateSignature(emailAccountId: Long, date: EmailAccountUpdateSignatureForm): Try[Option[EmailAccount]] = {

    val tryOfEaIdOpt = Try {

      DB autoCommit { implicit session =>

      sql"""
          UPDATE email_accounts
          SET
            signature = ${date.signature.trim}
          WHERE id = $emailAccountId
          RETURNING id;
        """
        .map(_.long("id"))
        .single()
        .apply()

      }
    }

    tryOfEaIdOpt match {

      case Failure(exception) =>
        Failure(exception)

      case Success(None) =>
        Success(None)

      case Success(Some(eaId)) =>
        find(id = eaId).map(emailAccount => Some(emailAccount))

    }
  }

  private def updateFrequencySettingsInternal(
    accountId: Long,
    emailAccountIds: List[Long],
    date: EmailAccountUpdateFrequencySettingsForm,
  ): Try[List[EmailAccount]] = {

    if (emailAccountIds.isEmpty) {

      Success(List())

    } else {

      val tryOfEaIdOpt = Try {

        DB autoCommit { implicit session =>

      sql"""
          UPDATE email_accounts
          SET
          	last_used_max_sends_per_day = least(warm_up_emails_per_day, last_used_max_sends_per_day + warm_up_emails_increase_per_day * date_part('day', now() - warm_up_emails_increase_per_day_updated_at)),
            warm_up_emails_increase_per_day_updated_at = now(),
            warm_up_emails_per_day = ${date.emails_per_day},
            warm_up_emails_increase_per_day = ${date.emails_increase_per_day}
          WHERE
            id IN ${SQLUtils.generateSQLValuesClause(emailAccountIds)}
            AND account_id = $accountId
          RETURNING id;
        """
            .map(_.long("id"))
            .list()
            .apply()

        }
      }

      tryOfEaIdOpt match {

        case Failure(exception) =>

          Failure(exception)

        case Success(eaIds) =>

          findEmailAccounts(
            emailAccountIds = eaIds,
            accountId = accountId,
          )

      }

    }

  }

  def updateFrequencySettings(
    accountId: Long,
    emailAccountIds: List[Long],
    date: EmailAccountUpdateFrequencySettingsForm,
  ): Try[List[EmailAccount]] = {

    val res = emailAccountIds
      .grouped(100)
      .map { emailAccountIdsGrp =>

        updateFrequencySettingsInternal(
          accountId = accountId,
          emailAccountIds = emailAccountIdsGrp,
          date = date,
        )

      }

    Helpers.seqTryToTrySeq(res.toList).map(_.flatten.toList)

  }

  private def activateOrDeactivateEmailAccountInternal(
    accountId: Long,
    emailAccountIds: List[Long],
    active: Boolean,
  ): Try[List[EmailAccount]] = {

    if (emailAccountIds.isEmpty) {

      Success(List())

    } else {

      // TODO: We will start again from 0 if they deactivate and then again active the email account?

      val tryOfEaIdOpt = Try {

        DB autoCommit { implicit session =>

      sql"""
          UPDATE email_accounts
          SET
            last_used_max_sends_per_day = 0,
            warm_up_emails_increase_per_day_updated_at = now(),
            active = $active
            WHERE id IN ${SQLUtils.generateSQLValuesClause(emailAccountIds)}
            AND account_id = $accountId
            RETURNING id;
          ;
      """
            .map(_.long("id"))
            .list()
            .apply()
        }

      }

      tryOfEaIdOpt match {

        case Failure(exception) =>

          Failure(exception)

        case Success(eaIds) =>

          findEmailAccounts(
            emailAccountIds = eaIds,
            accountId = accountId,
          )

      }

    }

  }


  def activateOrDeactivateEmailAccount(
    accountId: Long,
    emailAccountIds: List[Long],
    active: Boolean,
  ): Try[List[EmailAccount]] = {

    val res = emailAccountIds
      .grouped(100)
      .map { emailAccountIdsGrp =>

        activateOrDeactivateEmailAccountInternal(
          accountId = accountId,
          emailAccountIds = emailAccountIdsGrp,
          active = active,
        )

      }

    Helpers.seqTryToTrySeq(res.toList).map(_.flatten.toList)

  }

  private def clearErrorInternal(emailAccountIds: List[Long], accountId: Long): Try[List[Long]] = Try {

    if (emailAccountIds.isEmpty) {

      List()

    } else {

      DB autoCommit { implicit session =>

      sql"""
          UPDATE email_accounts
          SET
            error = NULL,
            error_reported_at = NULL,
            paused_till = NULL,
            auth_failed_count = 0,
            landing_check_failed_count = 0
          WHERE
            id IN ${SQLUtils.generateSQLValuesClause(emailAccountIds)}
            AND account_id = $accountId
          RETURNING id;
          ;
      """
          .map(_.long("id"))
          .list()
          .apply()
      }

    }

  }

  def clearError(
    emailAccountIds: List[Long],
    accountId: Long,
  ): Try[List[Long]] = {

    val res = emailAccountIds
      .grouped(100)
      .map { emailAccountIdsGrp =>

        clearErrorInternal(
          emailAccountIds = emailAccountIdsGrp,
          accountId = accountId,
        )

      }

    Helpers.seqTryToTrySeq(res.toList).map(_.flatten.toList)

  }

}

