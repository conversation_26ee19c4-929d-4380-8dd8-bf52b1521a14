package api.models

import org.joda.time.DateTime
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import play.api.libs.json.{JsError, JsObject, JsResult, JsSuccess, JsValue, <PERSON>son, <PERSON>s, Writes}

import scala.util.{Failure, Success, Try}

case class WarmupHeroEmailSettingResponse(
  id: String,
  owner_id: String,

  service_provider: EmailServiceProvider,
  can_auto_connect: Boolean,

  first_name: String,
  last_name: String,

  email: String,

  created_at: Option[DateTime],

  smtp_username: Option[String],
  smtp_host: Option[String],
  smtp_port: Option[Int],

  imap_username: Option[String],
  imap_host: Option[String],
  imap_port: Option[Int],
)


object WarmupHeroEmailSettingResponse {

  implicit val reads: Reads[WarmupHeroEmailSettingResponse] = new Reads[WarmupHeroEmailSettingResponse] {

    override def reads(json: JsValue): JsResult[WarmupHeroEmailSettingResponse] = Try {

      val serviceProviderStr = (json \ "service_provider").as[String]

      val serviceProvider = serviceProviderStr match {
        case "office365" => EmailServiceProvider.MICROSOFT_365_API
        case _ => EmailServiceProvider.OTHER
      }

      WarmupHeroEmailSettingResponse(
        id = (json \ "id").as[String],
        owner_id = (json \ "owner_id").as[String],

        service_provider = serviceProvider,
        can_auto_connect = (json \ "can_auto_connect").as[Boolean],

        first_name = (json \ "first_name").as[String],
        last_name = (json \ "last_name").as[String],
        email = (json \ "email").as[String],

        created_at = (json \ "created_at").asOpt[DateTime],

        smtp_username = (json \ "smtp_username").asOpt[String],
        smtp_host = (json \ "smtp_host").asOpt[String],
        smtp_port = (json \ "smtp_port").asOpt[Int],

        imap_username = (json \ "imap_username").asOpt[String],
        imap_host = (json \ "imap_host").asOpt[String],
        imap_port = (json \ "imap_port").asOpt[Int],
      )

    } match {

      case Failure(e) => JsError(e.getMessage)

      case Success(emailSettingResponseV3) => JsSuccess(emailSettingResponseV3)

    }

  }

  implicit val writes: Writes[WarmupHeroEmailSettingResponse] = new Writes[WarmupHeroEmailSettingResponse] {

    def writes(data: WarmupHeroEmailSettingResponse): JsObject = {
      Json.obj(
        "id" -> data.id,
        "owner_id" -> data.owner_id,

        "service_provider" -> data.service_provider,
        "can_auto_connect" -> data.can_auto_connect,

        "first_name" -> data.first_name,
        "last_name" -> data.last_name,
        "email" -> data.email,

        "created_at" -> data.created_at,

        "smtp_username" -> data.smtp_username,
        "smtp_host" -> data.smtp_host,
        "smtp_port" -> data.smtp_port,

        "imap_username" -> data.imap_username,
        "imap_host" -> data.imap_host,
        "imap_port" -> data.imap_port,

      )

    }

  }

}


case class WarmupHeroEmailSettingWithPasswordResponse(
  id: String,
  owner_id: String,

  service_provider: EmailServiceProvider,
  can_auto_connect: Boolean,

  is_purchased_from_sr: Boolean,

  first_name: String,
  last_name: String,

  email: String,

  created_at: Option[DateTime],

  smtp_username: Option[String],
  smtp_password: Option[String],
  smtp_host: Option[String],
  smtp_port: Option[Int],

  imap_username: Option[String],
  imap_password: Option[String],
  imap_host: Option[String],
  imap_port: Option[Int],
)


object WarmupHeroEmailSettingWithPasswordResponse {

  implicit val reads: Reads[WarmupHeroEmailSettingWithPasswordResponse] = new Reads[WarmupHeroEmailSettingWithPasswordResponse] {

    override def reads(json: JsValue): JsResult[WarmupHeroEmailSettingWithPasswordResponse] = Try {

      val serviceProviderStr = (json \ "service_provider").as[String]

      val serviceProvider = serviceProviderStr match {
        case "office365" => EmailServiceProvider.MICROSOFT_365_API
        case _ => EmailServiceProvider.OTHER
      }

      WarmupHeroEmailSettingWithPasswordResponse(
        id = (json \ "id").as[String],
        owner_id = (json \ "owner_id").as[String],

        service_provider = serviceProvider,
        can_auto_connect = (json \ "can_auto_connect").as[Boolean],

        is_purchased_from_sr = (json \ "is_purchased_from_sr").as[Boolean],

        first_name = (json \ "first_name").as[String],
        last_name = (json \ "last_name").as[String],
        email = (json \ "email").as[String],

        created_at = (json \ "created_at").asOpt[DateTime],

        smtp_username = (json \ "smtp_username").asOpt[String],
        smtp_password = (json \ "smtp_password").asOpt[String],
        smtp_host = (json \ "smtp_host").asOpt[String],
        smtp_port = (json \ "smtp_port").asOpt[Int],

        imap_username = (json \ "imap_username").asOpt[String],
        imap_password = (json \ "imap_password").asOpt[String],
        imap_host = (json \ "imap_host").asOpt[String],
        imap_port = (json \ "imap_port").asOpt[Int],
      )

    } match {

      case Failure(e) => JsError(e.getMessage)

      case Success(emailSettingResponseV3) => JsSuccess(emailSettingResponseV3)

    }

  }

  implicit val writes: Writes[WarmupHeroEmailSettingWithPasswordResponse] = new Writes[WarmupHeroEmailSettingWithPasswordResponse] {

    def writes(data: WarmupHeroEmailSettingWithPasswordResponse): JsObject = {
      Json.obj(
        "id" -> data.id,
        "owner_id" -> data.owner_id,

        "service_provider" -> data.service_provider,
        "can_auto_connect" -> data.can_auto_connect,

        "is_purchased_from_sr" -> data.is_purchased_from_sr,

        "first_name" -> data.first_name,
        "last_name" -> data.last_name,
        "email" -> data.email,

        "created_at" -> data.created_at,

        "smtp_username" -> data.smtp_username,
        "smtp_password" -> data.smtp_password,
        "smtp_host" -> data.smtp_host,
        "smtp_port" -> data.smtp_port,

        "imap_username" -> data.imap_username,
        "imap_password" -> data.imap_password,
        "imap_host" -> data.imap_host,
        "imap_port" -> data.imap_port,

      )

    }

  }

}


