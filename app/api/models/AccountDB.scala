package api.models

import api.AppConfig
import api.accounts.PasswordHasher
import org.joda.time.DateTime
import play.api.Logger
import play.api.libs.json._
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import play.api.libs.ws.WSClient
import scalikejdbc._
import scalikejdbc.jodatime.JodaWrappedResultSet._
import utils.{Helpers, SRLogger, StringUtils}
import utils.email.EmailNotificationService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class NavigationLinksPrevNextResponse(
  prev: Option[String],
  next: Option[String]
)


object NavigationLinksPrevNextResponse {

  implicit val format: OFormat[NavigationLinksPrevNextResponse] = Json.format[NavigationLinksPrevNextResponse]

}


case class SrEmailSettingsRes(
  email_settings: List[WarmupHeroEmailSettingResponse],
  links: NavigationLinksPrevNextResponse
)

object SrEmailSettingsRes {

  implicit val format: OFormat[SrEmailSettingsRes] = Json.format[SrEmailSettingsRes]

}

case class UnauthorizedSrApiKeyException(
                                          private val message: String = "Failed while fetching credits with SrApiKey Unauthorized",
                                          private val cause: Throwable = None.orNull
                                        ) extends Exception(message, cause)

case class ResetBillingMonthlyCycleForOrg(
  orgId: Long,
  currentCycleStartedAt: DateTime
)

case class UpdateAccountProfile(
  first_name: String,
  last_name: String,
  timezone: Option[String],
  sr_api_key: Option[String]
)

object UpdateAccountProfile {
  implicit val reads: Reads[UpdateAccountProfile] = Json.reads[UpdateAccountProfile]
}

case class AccountCreateForm(
  first_name: String,
  last_name: String,
  email: String,
  password: String,
)

object AccountCreateForm {
  implicit val format: OFormat[AccountCreateForm] = Json.format[AccountCreateForm]
}

case class ResetPasswordForm(
  password: String,
  code: String,
  email: String
)

object ResetPasswordForm {
  implicit val reads: Reads[ResetPasswordForm] = Json.reads[ResetPasswordForm]
}

case class ChangePasswordForm(
  old_password: String,
  new_password: String
)

object ChangePasswordForm {
  implicit val reads: Reads[ChangePasswordForm] = Json.reads[ChangePasswordForm]
}

case class VerifyEmailForm(
  code: String,
  email: String

)

object VerifyEmailForm {
  implicit val reads: Reads[VerifyEmailForm] = Json.reads[VerifyEmailForm]
}

case class ResendVerifyEmailForm(
  email: String
)

object ResendVerifyEmailForm {
  implicit val reads: Reads[ResendVerifyEmailForm] = Json.reads[ResendVerifyEmailForm]
}

// case class and companion object for ForgotPasswordForm
case class ForgotPasswordForm(
  email: String
)

object ForgotPasswordForm {
  implicit val reads: Reads[ForgotPasswordForm] = Json.reads[ForgotPasswordForm]
}


case class AccountProfileInfo(
  first_name: String,
  last_name: String,
)

object AccountProfileInfo {
  implicit val format: OFormat[AccountProfileInfo] = Json.format[AccountProfileInfo]
}

case class SRCredits(
  org_id: Long,
  total_allowed_warmup_emails: Int,
  message_for_user: String
)

object SRCredits {
  implicit val writes: OWrites[SRCredits] = Json.writes[SRCredits]
}

case class CreditResponse(
  total_allowed_warmup_emails: Int,
  message_for_user: String
)

object CreditResponse {
  implicit val writes: OWrites[CreditResponse] = Json.writes[CreditResponse]
}

case class Account(
  id: Long,
  email: String,
  password: String,
  created_at: DateTime,

  email_verification_code: Option[String],
  email_verification_code_created_at: Option[DateTime],
  email_verified: Boolean,

  profile: AccountProfileInfo,
  sr_api_key: Option[String],
  total_allowed_warmup_emails: Int,
  total_active_warmup_emails: Int,

  account_paused: Boolean,

  active: Boolean
)

object Account {

  // ignore password while returning account as JSON
  implicit val writes: Writes[Account] = new Writes[Account] {
    def writes(account: Account): JsValue = {

      val sr_spi_key = account.sr_api_key
      val hasSRApiKey = account.sr_api_key.isDefined
      val masked_sr_spi_key = if(hasSRApiKey) Some(s"${sr_spi_key.get.take(2)}***********${sr_spi_key.get.takeRight(4)}") else None
      Json.obj(
        "account_id" -> account.id,
        "email" -> account.email,

        "profile" -> account.profile,

        "created_at" -> account.created_at,

        "active" -> account.active,

        "account_paused" -> account.account_paused,

        "email_verified" -> account.email_verified,

        "sr_api_key" -> masked_sr_spi_key,

        "is_sr_api_key_exists" -> sr_spi_key.isDefined,

        "total_allowed_warmup_emails" -> account.total_allowed_warmup_emails,
        "total_active_warmup_emails" -> account.total_active_warmup_emails

      )
    }
  }

  implicit val reads: Reads[Account] = Json.reads[Account]


  def fromDb(rs: WrappedResultSet): Account = {

    val accountId = rs.int("id")

    val isActive = rs.boolean("active")

    val allowed_warmup_emails = rs.int("total_allowed_warmup_emails")

    /**
      * 23rd Sep 2022
      * discussed with @prateek and adding new column additional_fixed_allowed_email_accounts
      * additional_fixed_allowed_email_accounts is manually adding additional email accounts to handle bellow cases
      * 1. The customer referred 1 account but inactive in SmartReach
      * 2. Customer has given G2 review and he is trial
      * 3. Customer has given 5 G2 reviews and he is in 1 individual plan
      *
      * */
    val additional_fixed_allowed_email_accounts = rs.int("additional_fixed_allowed_email_accounts")

    Account(
      id = accountId,
      email = rs.string("email"),
      email_verification_code = rs.stringOpt("email_verification_code"),
      email_verification_code_created_at = rs.jodaDateTimeOpt("email_verification_code_created_at"),
      password = rs.string("password"),
      created_at = rs.jodaDateTime("created_at"),

      profile = AccountProfileInfo(
        first_name = rs.string("first_name"),
        last_name = rs.string("last_name")
      ),

      active = isActive,
      email_verified = rs.boolean("email_verified"),

      account_paused = rs.booleanOpt("account_paused").getOrElse(false),

      sr_api_key = rs.stringOpt("sr_api_key"),
      total_allowed_warmup_emails = (allowed_warmup_emails + additional_fixed_allowed_email_accounts),
      total_active_warmup_emails = rs.int("total_active_warmup_emails")
    )
  }
}

object AccountDB {

  implicit val session: AutoSession.type = AutoSession

  def find(id: Long): Option[Account] = DB readOnly { implicit session =>

    findAccountSql(sqls" WHERE acc.id = $id ")
      .map(Account.fromDb)
      .headOption()
      .apply()

  }

  def findByEmail(email: String): Option[Account] = DB readOnly { implicit session =>

    val param = email.toLowerCase
    findAccountSql(sqls" WHERE acc.email = $param ")
      .map(Account.fromDb)
      .headOption()
      .apply()

  }

  def findAllBySrApiKey(srApiKey: String): Try[List[Account]] = Try {

    DB readOnly { implicit session =>

      findAccountSql(sqls"WHERE acc.sr_api_key = $srApiKey")
        .map(Account.fromDb)
        .list()
        .apply()

    }

  }

  def findByOrgId(orgId: Long): Try[List[Account]] = Try {

    // org_id is not unique

    DB readOnly { implicit session =>

      findAccountSql(sqls"WHERE acc.org_id = $orgId")
        .map(Account.fromDb)
        .list()
        .apply()

    }

  }

  def findAllWithApiKey: Try[List[Account]] = Try {

    DB readOnly { implicit session =>

      findAccountSql(sqls"WHERE acc.sr_api_key IS NOT NULL")
        .map(Account.fromDb)
        .list()
        .apply()

    }

  }

  def autoCreateAccount(
    first_name: String,
    last_name: String,
    email: String,
    api_key: String,
    org_id: Long,
  ): Try[Option[Account]] = Try {

    val aid = DB autoCommit { implicit session =>

      /**
        * 12 Feb 2024
        *
        * Generating a random password when creating a WH account through auto-login flow,
        * because password in a NOT NULL field.
        *
        * - Password is not needed for login in auto-login flow
        * - If the user really wants to manually login, then they can do a password
        *   reset and it will allow them to login
        */
      val password = StringUtils.genRandomAlphaNumericString30Chars.take(16)

      val hashedPassword = PasswordHasher.hash(password)

      sql"""
        INSERT INTO accounts (
          email,
          org_id,
          password,
          sr_api_key,
          first_name,
          last_name
        )
        VALUES (
          ${email.trim.toLowerCase},
          $org_id,
          $hashedPassword,
          $api_key,
          ${first_name.trim},
          ${last_name.trim}
        )
        RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()

    }

    aid.flatMap(accountId => find(id = accountId))

  }

  def findAccountSql(whereClause: SQLSyntax): SQL[_, _] = {
    sql"""
           SELECT
             acc.id,
             acc.first_name,
             acc.last_name,
             acc.email,
             acc.password,
             acc.email_verified,
             acc.email_verification_code,
             acc.email_verification_code_created_at,
             acc.active,
             acc.sr_api_key,
             acc.total_allowed_warmup_emails,
             acc.created_at,
             acc.account_paused,
             count(ea.*) as total_active_warmup_emails,
             acc.additional_fixed_allowed_email_accounts as additional_fixed_allowed_email_accounts
             FROM accounts acc
             LEFT JOIN email_accounts ea ON
                ea.account_id = acc.id
                AND ea.active = TRUE
                AND ea.error IS NULL
                -- 6 Feb 2025: omit sr purchased emails from limit count.
                AND is_purchased_from_sr = FALSE
           $whereClause
           GROUP BY acc.id
    """
  }

  def create(data: AccountCreateForm): Try[Option[Account]] = Try {
    val aid = DB autoCommit { implicit session =>

      val hashedPassword = PasswordHasher.hash(data.password)
      sql"""
        INSERT INTO accounts
        (
         email,
         password,
         first_name,
         last_name
         )
        VALUES (
        ${data.email.trim.toLowerCase},
        $hashedPassword,
        ${data.first_name},
        ${data.last_name}
        )
        RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()

    }

    aid.flatMap(accountId => find(id = accountId))
  }


  def getAccountIdFromOTP(code: String, email: String): Option[Long] = {
    DB autoCommit { implicit session =>

      sql"""
          SELECT id
          FROM accounts
          WHERE email = $email AND email_verification_code = $code
         ;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
    }
  }

  def getUnVerifiedOTP(accountId: Long): Option[String] = {
    DB autoCommit { implicit session =>

      sql"""
          SELECT email_verification_code
          FROM accounts
          WHERE id = $accountId
          AND email_verification_code IS NOT NULL
          AND NOT email_verified
          AND (email_verification_code_created_at IS NOT NULL AND email_verification_code_created_at > now() - interval '15 minutes')
         ;
      """
        .map(rs => rs.string("email_verification_code"))
        .single()
        .apply()
    }
  }

  def updatePasswordAndResetVerificationCode(accountId: Long, password: String, code: String): Try[Option[Account]] = Try {
    DB autoCommit { implicit session =>

      val hashedPassword = PasswordHasher.hash(password)

      sql"""
          UPDATE accounts
          SET
            password = $hashedPassword,
            email_verified = TRUE,
            email_verification_code = NULL,
            email_verification_code_created_at = NULL
          WHERE id = $accountId
            AND email_verification_code = $code
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
        .flatMap(id => find(id))
    }
  }

  def updatePasswordWhenUserIsLoggedIn(accountId: Long, password: String): Try[Option[Account]] = Try {
    DB autoCommit { implicit session =>

      val hashedPassword = PasswordHasher.hash(password)

      sql"""
          UPDATE accounts
          SET
            password = $hashedPassword,
            email_verification_code = NULL,
            email_verification_code_created_at = NULL
          WHERE id = $accountId
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
        .flatMap(id => find(id))
    }
  }


  def updateAccountEmailStatusIsVerified(accountId: Long): Try[Option[Account]] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE accounts
          SET
            email_verified = TRUE,
            email_verification_code = NULL,
            email_verification_code_created_at = NULL
          WHERE id = $accountId
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
        .flatMap(id => find(id))
    }
  }



  def addEmailVerificationCode(accountId: Long, emailVerificationCode: String): Try[Option[Account]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE accounts
          SET
            email_verification_code = $emailVerificationCode,
            email_verification_code_created_at = now()
          WHERE id = $accountId
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
        .flatMap(id => find(id))

    }
  }

  def updateAccountOrgId(
    accountId: Long,
    orgId: Long,
  ): Try[Option[Long]] = Try {

    DB autoCommit { implicit session =>

      sql"""
           UPDATE
             accounts
           SET
             org_id = $orgId
           WHERE
             id = $accountId
             AND org_id IS NULL
           RETURNING id;
         """
        .map(rs => rs.long("id"))
        .single()
        .apply()

    }

  }

  def getEmailVerificationCodeByAccountIDForResend(accountId: Long): Try[Option[String]] = Try {
    DB autoCommit { implicit session =>

      sql"""
        SELECT a.email_verification_code
        FROM accounts a
        WHERE a.id = $accountId
         AND a.email_verified = FALSE
         AND a.email_verification_code IS NOT NULL
       """
        .map(rs => rs.string("email_verification_code"))
        .single()
        .apply()

    }
  }

  def updateProfile(accountId: Long, data: UpdateAccountProfile): Try[Option[Account]] = Try {
    DB autoCommit { implicit session =>


      sql"""
          UPDATE accounts
          SET
            first_name = ${data.first_name},
            last_name = ${data.last_name}
          WHERE id = $accountId
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
        .flatMap(id => find(id))

    }

  }

  def getAccountsToResetSRCredits(): Try[List[Long]] = Try {
    DB autoCommit { implicit session =>

      sql"""
        SELECT a.id
        FROM accounts a
        WHERE a.sr_api_key IS NOT NULL
         AND a.sr_api_key_error IS NULL
         AND (a.last_sr_credits_rest_at IS NULL OR a.last_sr_credits_rest_at < now() - interval '24 hours')
         AND a.active = TRUE
         AND (a.account_paused IS NULL OR a.account_paused = FALSE)
         LIMIT 5
       """
        .map(rs => rs.long("id"))
        .list()
        .apply()

    }

  }

  def updateSRSettings(
    accountId: Long,
    total_allowed_warmup_emails: Int,
    apiKey: String,
    orgId: Long,
  ): Try[Option[Account]] = Try {
    DB localTx  { implicit session =>

      sql"""
          UPDATE accounts
          SET
            sr_api_key = $apiKey,
            sr_api_key_error = NULL,
            total_allowed_warmup_emails = $total_allowed_warmup_emails,
            org_id = $orgId,
            account_paused = FALSE
          WHERE id = $accountId
          RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
        .flatMap(id => find(id))

    }

  }

  def __updateSRCredits(
                         accountId: Long,
                         total_allowed_warmup_emails: Int,
                         has_sr_api_key_error: Option[String]
                       ): Try[Option[Long]] = Try {
    DB autoCommit { implicit session =>

      sql"""
          UPDATE accounts
          SET
            total_allowed_warmup_emails = $total_allowed_warmup_emails,
            last_sr_credits_rest_at = now(),
            sr_api_key_error = $has_sr_api_key_error
          WHERE id = $accountId
            RETURNING id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()

    }

  }

  private def pauseAccount(
                    accountId: Long
                  ): Try[Option[Long]] = Try {

    val totalAllowedWarmupEmails = 0

    val srApiKeyError = "Invalid SmartReach API Key"

    DB autoCommit { implicit session =>

      sql"""
        UPDATE
          accounts
        SET
          total_allowed_warmup_emails = $totalAllowedWarmupEmails,
          sr_api_key_error_at = now(),
          sr_api_key_error = $srApiKeyError,
          account_paused = TRUE
        WHERE
          id = $accountId
        RETURNING
          id;
      """
        .map(rs => rs.long("id"))
        .single()
        .apply()
    }
  }

  def __pauseEmailAccounts(accountId: Long, limit: Int, reason: String): Seq[String] = {
     DB autoCommit { implicit session =>

      sql"""
         WITH update_ea AS (
            SELECT
              id
            FROM
              email_accounts
            WHERE
              account_id = $accountId
              AND active = TRUE
              AND is_purchased_from_sr = FALSE
            LIMIT $limit
         )
         UPDATE email_accounts as ea
           SET
            active = FALSE,
            error = $reason,
            error_reported_at = now()
           FROM update_ea
           WHERE ea.id = update_ea.id
           RETURNING email;
      """
        .map(_.string("email"))
        .list()
        .apply()

    }

  }


  /**
    * pausing all email accounts when sr_api_key is invalid
    * */
  def __deactivateAllEmailAccountsDueToInvalidApiKey(accountId: Long, reason: String): Seq[String] = {
    DB autoCommit { implicit session =>

      sql"""
           UPDATE email_accounts
           SET
              active = FALSE,
              error = $reason,
              error_reported_at = now()
           WHERE account_id = $accountId
           RETURNING email;
      """
        .map(_.string("email"))
        .list()
        .apply()

    }

  }


  def getEmailAccountsFromSR(
    sr_api_key: String,
    older_than: Option[Long],
    newer_than: Option[Long]
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    logger: SRLogger,
  ): Future[SrEmailSettingsRes] = {

    val paramOpt = if (older_than.isDefined) {

      Some(s"?older_than=${older_than.get}")

    } else if (newer_than.isDefined) {

      Some(s"?newer_than=${newer_than.get}")

    } else {

      None

    }

    val srBaseDomain = AppConfig.srBaseDomain

    val authUrl = s"$srBaseDomain/api/v3/settings/warmuphero/email_settings${paramOpt.getOrElse("")}"

    ws.url(authUrl)
      .withHttpHeaders("X-API-KEY" -> sr_api_key)
      .get()
      .flatMap { response =>

        if (response.status == 401) {

          logger.error(
            msg = s"FATAL: Failed while getting email accounts 401 Unauthorized: $sr_api_key :: ${response.body}"
          )

          Future.failed(UnauthorizedSrApiKeyException())

        } else if (response.status != 200) {

          logger.error(s"FATAL: Failed while getting SmartReach email accounts ${response.body}")

          Future.failed(new Exception(s"Failed while fetching SmartReach email accounts"))

        } else {

          response.json.validate[SrEmailSettingsRes] match {

            case JsError(errors) =>

              logger.error(msg = s"Failed to parse SrEmailSettingsRes: $errors")

              Future.failed(new Exception(s"Failed to parse SrEmailSettingsRes"))

            case JsSuccess(srEmailSettingsRes, _) =>

              Future.successful(srEmailSettingsRes)

          }

        }

      }

  }


  def getCreditsBySRApiKey(sr_api_key: String ,Logger: SRLogger)
                          (implicit ws: WSClient, ec: ExecutionContext): Future[Option[SRCredits]] = {

    val srBaseDomain = AppConfig.srBaseDomain
    val authUrl = s"${srBaseDomain}/api/v2/auth/warmupbox_credits"
    ws.url(authUrl)
      .withHttpHeaders("X-API-KEY" -> sr_api_key)
      .get()
      .map(response => {

        if (response.status == 401) {

          Logger.error(s"FATAL: Failed while getting warmupbox_credits 401 Unauthorized: $sr_api_key :: ${response.body}")

          throw UnauthorizedSrApiKeyException()

        }
        else if (response.status != 200) {

          Logger.error(s"FATAL: Failed while getting warmupbox_credits: $sr_api_key :: ${response.body}")

          throw new Exception(s"Failed while fetching credits")


        } else {
          val total_allowed_warmup_emails = (response.json \ "data" \ "total_allowed_warmup_emails").asOpt[Int]
          val message_for_user = (response.json \ "data" \ "message_for_user").asOpt[String]
          val org_id = (response.json \ "data" \ "org_id").asOpt[Long]
          if (
            total_allowed_warmup_emails.isDefined &&
              message_for_user.isDefined &&
              org_id.isDefined
          ) {
            Some(
              SRCredits(
                org_id = org_id.get,
                total_allowed_warmup_emails = total_allowed_warmup_emails.get,
                message_for_user = message_for_user.get
              )
            )
          } else {

            Logger.error(s"FATAL: malformed response received from warmupbox_credits api: $sr_api_key :: ${response.body}")

            throw new Exception(s"malformed response received while fetching credits")

          }
        }


      })
  }

  def getAccountMetadata(
    sr_api_key: String,
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    logger: SRLogger,
  ): Future[WarmupHeroAccountMetadata] = {

    val srBaseDomain = AppConfig.srBaseDomain

    val metadataUrl = s"$srBaseDomain/api/v2/warmuphero/metadata"

    ws.url(metadataUrl)
      .withHttpHeaders("X-API-KEY" -> sr_api_key)
      .get()
      .flatMap { response =>

        if (response.status == 401) {

          logger.error(
            msg = s"FATAL: Failed while getting WarmupHeroAccountMetadata 401 Unauthorized: $sr_api_key :: ${response.body}"
          )

          Future.failed(UnauthorizedSrApiKeyException())

        } else if (response.status != 200) {

          logger.error(
            msg = s"FATAL: Failed while getting WarmupHeroAccountMetadata :: ${response.body}"
          )

          Future.failed(new Exception(s"Failed to fetch WarmupHeroAccountMetadata"))

        } else {

          (response.json \ "data").validate[WarmupHeroAccountMetadata] match {

            case JsError(errors) =>

              val errMsg = "Failed to parse WarmupHeroAccountMetadata"

              logger.error(
                msg = s"$errMsg - validation error: $response ::: ${response.body} ::: $errors"
              )

              Future.failed(throw new Throwable(errMsg))

            case JsSuccess(accountMetadata, _) =>

              Future.successful(accountMetadata)

          }

        }

      }

  }


  def resetSRCreditsDailyFromCron(accountId: Long, Logger: SRLogger)
                                 (implicit ws: WSClient, ec: ExecutionContext): Future[Option[Long]] = {

    find(id = accountId) match {

      case None =>

        Future.successful(None)

      case Some(account) =>

        if (account.sr_api_key.isDefined) {

          resetSRCredits(
            accountId = account.id,
            sr_api_key = account.sr_api_key.get,
            total_active_warmup_emails = account.total_active_warmup_emails,
            Logger = Logger
          )

        } else {

          Future.successful(None)

        }

    }

  }

  def resetSRCredits(
                           accountId: Long,
                           sr_api_key: String,
                           total_active_warmup_emails: Int,
                           Logger: SRLogger)
                         (implicit ws: WSClient, ec: ExecutionContext): Future[Option[Long]] = {

    getCreditsBySRApiKey(sr_api_key = sr_api_key, Logger = Logger).flatMap(credits => {

      if (credits.isDefined) {
        val total_allowed_warmup_emails_base_plan = credits.get.total_allowed_warmup_emails

        __updateSRCredits(
          accountId = accountId,
          total_allowed_warmup_emails = total_allowed_warmup_emails_base_plan,
          has_sr_api_key_error = None,
        ) match {

          case Failure(e) =>
            Logger.error(s"AccountDB.resetSRCredits FATAL error: ${Helpers.getStackTraceAsString(e)} :: $sr_api_key:: ${accountId}")

            Future.successful(None)

          case Success(None) =>
            Logger.error(s"AccountDB.resetSRCredits NONE :: $sr_api_key:: ${accountId}")

            Future.successful(None)

          case Success(Some(res)) =>

            /**
              * The `total_allowed_warmup_emails_base_plan` limit comes from the base plan,
              * but sometimes we also manually add an additional limit for some accounts
              * (`additional_fixed_allowed_email_accounts`).
              * So, when checking if the limit has been exceeded,
              * we need to add both `total_allowed_warmup_emails_base_plan` + `additional_fixed_allowed_email_accounts`.
              * We handle this in the fromDb method for the Account case class.
              */
            find(id = accountId) match {

              case None =>

                val errMsg = "Not found account"

                Logger.logAndSendWarmupSmsAlert(
                  msg = s"$errMsg - resetSRCredits. accountId: $accountId",
                )

                Future.failed(new Exception(errMsg))

              case Some(account) =>

                if (total_active_warmup_emails > account.total_allowed_warmup_emails) {
                  val diff = total_active_warmup_emails - account.total_allowed_warmup_emails
                  val pausedEmails = __pauseEmailAccounts(accountId = accountId, limit = diff, reason = "Email account paused due to accounts exceeds warm up limit")

                  EmailNotificationService.sendEmailAccountPauseEmail(
                    accountId = accountId,
                    emailAddress = pausedEmails,
                    Logger = Logger
                  )
                }

                Future.successful(Some(res))

            }

        }

      } else {

        Logger.error(s"AccountDB.getCreditsBySRApiKey empty credits :: $sr_api_key:: ${accountId}")

        __updateSRCredits(
          accountId = accountId,
          total_allowed_warmup_emails = 0,
          has_sr_api_key_error = Some("Invalid SmartReach API Key")
        ) match {

          case Failure(e) =>
            Logger.error(s"AccountDB.resetSRCredits FATAL error: ${Helpers.getStackTraceAsString(e)} :: $sr_api_key:: ${accountId}")

            Future.successful(None)

          case Success(None) =>

            Logger.error(s"AccountDB.resetSRCredits NONE :: $sr_api_key:: ${accountId}")

            Future.successful(None)

          case Success(Some(_)) =>

            EmailNotificationService.sendEmailForDeactivatedAllEmailAccountsDueToInvalidAPIKey(
              accountId = accountId,
              Logger = Logger
            )

            /**
              * pauseAllEmailAccounts when api_key is not valid
              */
            __deactivateAllEmailAccountsDueToInvalidApiKey(accountId = accountId, reason = "Email account deactivated due to invalid SmartReach API key")

            Future.successful(None)
        }

      }
    }).recover { case e =>

      Logger.error(s"CacheService.setSessionAccount FATAL: ${Helpers.getStackTraceAsString(e)} :: $sr_api_key:: ${accountId}")

      if (e.getMessage == UnauthorizedSrApiKeyException().getMessage) {

        pauseAccount(
          accountId = accountId
        ) match {
          case Failure(exception) =>
            Logger.error(s"Failed to pause account $sr_api_key :: ${accountId}", err = exception)

          case Success(None) =>
            Logger.error(s"Failed to pause account, account not found $sr_api_key :: ${accountId}")

          case Success(Some(pausedAccountId: Long)) =>

            Logger.info(s"Successfully paused account with id: $pausedAccountId")

            EmailNotificationService.sendEmailForDeactivatedAllEmailAccountsDueToInvalidAPIKey(
              accountId = pausedAccountId,
              Logger = Logger
            )
        }
      }

      None
    }

  }

}