package api.models

import play.api.libs.json._

case class EmailAccountId(id: Long) extends AnyVal {
  override def toString: String = id.toString
}

object EmailAccountId {

  implicit val writes: Writes[EmailAccountId] = new Writes[EmailAccountId] {

    override def writes(o: EmailAccountId): JsValue = JsNumber(o.id)

  }

  implicit val reads: Reads[EmailAccountId] = new Reads[EmailAccountId] {

    override def reads(ev: JsValue): JsResult[EmailAccountId] = {

      ev match {

        case JsNumber(id) => JsSuccess(EmailAccountId(id = id.toLong))

        case randomValue => JsError(s"expected number, got some random value - $randomValue")

      }

    }

  }

}
