package api.models

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>ber, JsResult, JsSuccess, JsV<PERSON>ue, Reads, Writes}

case class AccountId(id: Long) extends AnyVal {
  override def toString: String = id.toString
}

object AccountId {

  implicit val writes: Writes[AccountId] = new Writes[AccountId] {

    override def writes(o: AccountId): JsValue = JsNumber(o.id)

  }

  implicit val reads: Reads[AccountId] = new Reads[AccountId] {

    override def reads(ev: JsValue): JsResult[AccountId] = {

      ev match {

        case JsNumber(id) => JsSuccess(AccountId(id = id.toLong))

        case randomValue => JsError(s"expected number, got some random value - $randomValue")

      }

    }

  }

}
