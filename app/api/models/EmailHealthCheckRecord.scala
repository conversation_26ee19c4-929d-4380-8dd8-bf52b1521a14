package api.models

import api.services.{EmailAuthStatus, EmailHealthCheckRecordId}
import org.joda.time.DateTime
import play.api.libs.json.{Json, OWrites}
import scalikejdbc.jodatime.JodaWrappedResultSet._
import play.api.libs.json.JodaWrites._
import scalikejdbc.WrappedResultSet


case class EmailHealthCheckRecord(
  id: EmailHealthCheckRecordId,
  account_id: Long,

  email_account_id: Long,

  spf_auth_status: EmailAuthStatus,
  dkim_auth_status: EmailAuthStatus,
  dmarc_auth_status: EmailAuthStatus,

  dkim_selector_opt: Option[String],

  spf_record_opt: Option[String],
  dkim_record_opt: Option[String],
  dmarc_record_opt: Option[String],

  status: EmailHealthCheckStatus,

  retry_count: Int,

  updated_at: DateTime,
  created_at: DateTime,
)

object EmailHealthCheckRecord {

  implicit val writes: OWrites[EmailHealthCheckRecord] = Json.writes[EmailHealthCheckRecord]

  def fromDB(rs: WrappedResultSet): EmailHealthCheckRecord = {

    // from dig
    val spf_record_opt = rs.stringOpt("spf_record")
    val dkim_record_opt = rs.stringOpt("dkim_record")
    val dmarc_record_opt = rs.stringOpt("dmarc_record")

    // from email headers
    val spf_auth_status = EmailAuthStatus.fromKey(key = rs.string("spf_auth_status"))
    val dkim_auth_status = EmailAuthStatus.fromKey(key = rs.string("dkim_auth_status"))
    val dmarc_auth_status = EmailAuthStatus.fromKey(key = rs.string("dmarc_auth_status"))

    val spf_updated = if (
      spf_auth_status == EmailAuthStatus.None &&
        spf_record_opt.exists(_.toLowerCase.contains("v=spf"))
    ) {
      EmailAuthStatus.Pass
    } else {
      spf_auth_status
    }

    val dmarc_updated = if (
      dmarc_auth_status == EmailAuthStatus.None &&
        dmarc_record_opt.exists(_.toLowerCase.contains("v=dmarc"))
    ) {
      EmailAuthStatus.Pass
    } else {
      dmarc_auth_status
    }

    val dkim_updated = if (
      dkim_auth_status == EmailAuthStatus.None &&
        dkim_record_opt.exists(_.toLowerCase.contains("v=dkim"))
    ) {
      EmailAuthStatus.Pass
    } else {
      dkim_auth_status
    }

    EmailHealthCheckRecord(
      id = EmailHealthCheckRecordId(id = rs.long("id")),

      account_id = rs.long("account_id"),

      email_account_id = rs.long("email_account_id"),

      spf_auth_status = spf_updated,
      dkim_auth_status = dkim_updated,
      dmarc_auth_status = dmarc_updated,

      dkim_selector_opt = rs.stringOpt("dkim_selector"),

      spf_record_opt = spf_record_opt,
      dkim_record_opt = dkim_record_opt,
      dmarc_record_opt = dmarc_record_opt,

      status = EmailHealthCheckStatus.fromKey(actionStatus = rs.string("status")).get,

      retry_count = rs.int("retry_count"),

      updated_at = rs.jodaDateTime("updated_at"),
      created_at = rs.jodaDateTime("created_at"),
    )

  }

}
