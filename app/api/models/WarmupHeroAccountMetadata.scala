package api.models

import play.api.libs.json.{Format, JsError, JsResult, JsSuccess, JsValue, <PERSON>son}

import scala.util.{Failure, Success, Try}

case class WarmupHeroAccountMetadata(

  enable_internal_email_accounts_api_for_warmuphero: <PERSON><PERSON><PERSON>,

  enable_ai_email_generation_for_warmuphero: <PERSON><PERSON><PERSON>,

)

object WarmupHeroAccountMetadata {

  implicit val format: Format[WarmupHeroAccountMetadata] = new Format[WarmupHeroAccountMetadata] {

    override def reads(json: JsValue): JsResult[WarmupHeroAccountMetadata] = Try {

      WarmupHeroAccountMetadata(

        enable_internal_email_accounts_api_for_warmuphero = (json \ "enable_internal_email_accounts_api_for_warmuphero").as[<PERSON><PERSON><PERSON>],

        enable_ai_email_generation_for_warmuphero = (json \ "enable_ai_email_generation_for_warmuphero").as[<PERSON><PERSON><PERSON>],

      )

    } match {

      case Failure(e) =>

        JsError(e.toString)

      case Success(value) =>

        JsSuccess(value)

    }

    override def writes(o: WarmupHeroAccountMetadata): JsValue = {

      Json.obj(

        "enable_internal_email_accounts_api_for_warmuphero" -> o.enable_internal_email_accounts_api_for_warmuphero,

        "enable_ai_email_generation_for_warmuphero" -> o.enable_ai_email_generation_for_warmuphero,

      )

    }

  }

}
