package api


object CONSTANTS {

  val APP_NAME = "WarmupHero"


  object API_MSGS {

    val ERROR_INVALID_REQUEST = "Invalid Request"

    val ERROR_INTERNAL_SERVER = "Sorry, there was an error. Please try again or contact support."

    val ERROR_NOT_FOUND_ACCOUNT = "We could not find an account with the given email"

    val SUCCESS_GENERIC_MSG = "Success"

    val SUCCESS_FORGOT_PASSWORD = "We have sent you an email to reset your password"

    val SUCCESS_EMAIL_VERIFIED = "Successfully verified your email"

    val SUCCESS_UPDATED_PASSWORD = "Password updated successfully"

    val SUCCESS_UPDATED_API_KEY = "API Key updated successfully"


    val CNAME_DNS_NOT_CORRECT = "Your CNAME DNS does not point to our servers. If you have updated your DNS recently, it may take 15 minutes to 24 hours for the changes to reflect across the internet."

    val INVALID_SYNTAX_IN_EMAIL = "Please verify your email and subject for missing / invalid merge-tags or conditional syntax. Only valid merge-tags can be used inside double-braces (e.g. {{first_name}} ). If you can't figure it out, please contact us."

  }


  object EMAIL_API_ERROR_KEYS {
    val GATEWAY_TIMEOUT = "GATEWAY_TIMEOUT"

    val OUTLOOK_SERVICE_UNAVAILABLE = "OUTLOOK_SERVICE_UNAVAILABLE"

    val INTERNAL_FAILURE = "INTERNAL_FAILURE"

  }

}
