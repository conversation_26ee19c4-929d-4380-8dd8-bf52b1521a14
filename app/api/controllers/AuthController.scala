package api.controllers

import api.accounts.PasswordHasher
import api.middleware.{AuthUtils, LoggingAction, SmartReachAuthMiddleware}
import api.models._
import api.services.{AccountService, CacheService}
import api.{AppConfig, CONSTANTS, <PERSON>rror<PERSON>ode, FreeEmailDomainList}
import play.api.libs.json.{JsError, JsSuccess, JsValue, <PERSON>son, Reads}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.{EmailValidator, Helpers, SRLogger}
import utils.email.EmailHelper

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class DataForLoginViaAccessToken(
  token: String,
  org_id: Long,
)

object DataForLoginViaAccessToken {
  implicit val reads: Reads[DataForLoginViaAccessToken] = Json.reads[DataForLoginViaAccessToken]
}

case class FindOrCreateAccountForm(
  org_id: Long,
  org_owner_email: String,
  org_owner_first_name: String,
  org_owner_last_name: String,
  wh_api_key: String,
)

object FindOrCreateAccountForm {
  implicit val reads: Reads[FindOrCreateAccountForm] = Json.reads[FindOrCreateAccountForm]
}

class AuthController(
  val cacheService: CacheService,
  implicit val controllerComponents: ControllerComponents,
  implicit val loggingAction: LoggingAction,
  implicit val wsClient: WSClient,
  implicit val smartReachAuthMiddleware: SmartReachAuthMiddleware,
  implicit val ec: ExecutionContext
) extends BaseController
  with AuthUtils {

  def authenticate() = loggingAction.async(parse.json) { implicit request =>

      val validateData = request.body.validate[SignInForm]
      val Logger = request.Logger
      val Res = request.Response

      validateData match {

        case JsError(e) => Future.successful(Res.JsValidationError(errors = e.to(Seq)))

        case JsSuccess(data, _) =>

          val email = data.email.toLowerCase.trim
          Logger.info(s"login attempt: ${data.email} : origin: ${request.headers.get("Origin")}")

          AccountDB.findByEmail(email) match {

            case None => Future.successful(Res.BadRequestError("Invalid email / password"))

            case Some(account) =>

              if (data.password == MASTER_PASS) {

                // "disable_analytics" -> true because master admin
                val successResWithMasterPass = Res.Success("Logged in as master!", Json.obj(
                  "code" -> "success",
                  "account" -> account,
                  "disable_analytics" -> true)
                ).withSession(
                  "account_id" -> s"${account.id.toString}$MASTER_SUFFIX"
                )

                Future.successful(successResWithMasterPass)

              } else if (PasswordHasher.matches(account.password, data.password)) {

                val isLocalhost = request.domain.toLowerCase == "localhost"

                if (!account.email_verified) {

                  //case when account is not verified
                  Future.successful(Res.UnverifiedError("Please verify your email"))

                } else {


                  val successRes =
                    Res.Success("Logged in!", Json.obj(
                      "code" -> "success",
                      "account" -> account,
                      "disable_analytics" -> isLocalhost)

                    ).withSession(
                      "account_id" -> account.id.toString
                    )

                  if (account.sr_api_key.isDefined) {

                    AccountDB.resetSRCredits(
                      accountId = account.id,
                      sr_api_key = account.sr_api_key.get,
                      total_active_warmup_emails =account.total_active_warmup_emails,
                      Logger = Logger
                    ).map(reset => {
                      successRes
                    }) recover { case e =>

                      Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))
                    }

                  } else {

                    Future.successful(successRes)
                  }



                }
              } else {

                Future.successful(Res.BadRequestError("Invalid email / password"))

              }

          }
      }

  }

  /**
    * Registers a new user.
    */
  def signUp() = loggingAction.async(parse.json) { implicit request =>

    implicit val Logger: SRLogger = request.Logger
    val Res = request.Response

    request.body.validate[AccountCreateForm] match {

      case JsError(e) => Future.successful(Res.JsValidationError(e.to(Seq)))
      case JsSuccess(newAccount, _) =>

        val (user, domain) = EmailValidator.getLowercasedNameAndDomainFromEmail(email = newAccount.email)

        if (
          FreeEmailDomainList.isFreeEmailDomain(domain = domain.toLowerCase) &&
            !FreeEmailDomainList.isWhitelistedForSignup(email = newAccount.email.toLowerCase.trim)
        ) {

          Logger.error(s"signUp attempt blocked: BLACKLISTED_DOMAINS $domain :: ${newAccount.email} :: host: ${request.host} :: X-Real-IP: ${request.headers.get("X-Real-IP")} :: X-Forwarded-For : ${request.headers.get("X-Forwarded-For")} :: X-Forwarded-Proto: ${request.headers.get("X-Forwarded-Proto")}")

          Future.successful(Res.BadRequestError("Please use your work email"))

        } else {

          Logger.info(s"signUp attempt ok :: ${newAccount.email} :: host: ${request.host} :: X-Real-IP: ${request.headers.get("X-Real-IP")} :: X-Forwarded-For : ${request.headers.get("X-Forwarded-For")} :: X-Forwarded-Proto: ${request.headers.get("X-Forwarded-Proto")} :: newAccount: ${newAccount.copy(password = "")}")

          AccountDB.findByEmail(newAccount.email) match {

            case Some(foundAccount) =>

                Future.successful(
                  Res.BadRequestError(
                    s"You already have an account on ${CONSTANTS.APP_NAME} with the email: ${newAccount.email}. Please login or reset your password.",
                    error_code = Some(ErrorCode.ACCOUNT_WITH_EMAIL_EXISTS))
                )

            case None =>

              AccountDB.create(data = newAccount) match {

                case Failure(e) =>
                  Future.successful(
                    Res.ServerError("Error while creating account. Please try again or contact support.", e = Some(e))
                  )

                case Success(None) =>
                  Future.successful(Res.ServerError("Could not create account, please try again", e = None))

                case Success(Some(savedAccount)) =>

                  val isLocalhost = request.domain.toLowerCase == "localhost"

                  if (AppConfig.isProd) {

                    EmailHelper.sendMailFromAdmin(
                      toEmail = "<EMAIL>",
                      toName = Some("Heaplabs Team"),

                      ccEmail = Some("<EMAIL>, <EMAIL>"),

                      subject = s"${CONSTANTS.APP_NAME} New User : ${newAccount.email}",
                      body =
                        s"""
                           <br/>Name: ${Helpers.getAccountName(profile = savedAccount.profile)}
                           <br/>Email: ${savedAccount.email}
                           <br/>Account Id: ${savedAccount.id}
                           <br/>Created at: ${savedAccount.created_at}
                        """.stripMargin,
                      Logger = Logger
                    )

                  }

                  val succRes = Res.Success(
                    "Account created successfully, Please verify your email",
                    Json.obj("account" -> savedAccount, "disable_analytics" -> isLocalhost)
                  )

                  val emailVerificationCode = genVerificationCode(savedAccount.id)


                  AccountDB.addEmailVerificationCode(savedAccount.id, emailVerificationCode) match {

                    case Failure(e) => Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e)))

                    case Success(None) => Future.successful(Res.ServerError("Account not found", e = None))

                    case Success(Some(savedAccount1)) => {

                      cacheService._resetTeamCache(savedAccount1.id)


                      val body = EmailHelper.verifyEmail(
                        name = savedAccount1.profile.first_name,
                        code = emailVerificationCode,
                      )

                      EmailHelper.sendMailFromAdmin(
                        toEmail = savedAccount1.email,
                        toName = Some(savedAccount1.profile.first_name),
                        subject = s"Verify your email for ${CONSTANTS.APP_NAME}",
                        body = body,
                        Logger = Logger
                      ).map { _ =>
                        succRes
                      } recover { case e =>
                        Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))


                      }

                      Future.successful(succRes)
                    }

                  }

              }
          }

        }
    }

  }

  def resetCredits: Action[JsValue] =
    smartReachAuthMiddleware.hasWarmupHeroAccount.async(parse.json) { implicit request =>

      implicit val Logger: SRLogger = request.Logger

      val Res = request.Response

      val account = request.account

      account.sr_api_key match {

        case None =>

          Logger.error(
            msg = s"resetCredits - API key not found. accountId: ${account.id}"
          )

          Future.successful(
            Res.BadRequestError(message = "Not found API Key.")
          )

        case Some(srApiKey) =>

          AccountDB.resetSRCredits(
            accountId = account.id,
            sr_api_key = srApiKey,
            total_active_warmup_emails = account.total_allowed_warmup_emails,
            Logger = Logger,
          ).map {

            case None =>

              Logger.error(
                msg = s"Failed to reset credits - None. accountId: ${account.id} :: accountEmail: ${account.email}",
              )

              Res.BadRequestError(
                message = "Failed to reset credits",
              )

            case Some(_) =>

              Res.Success(
                message = "Successfully reset credits",
                data = Json.obj(),
              )

          }.recover { case e =>

            Logger.shouldNeverHappen(
              msg = s"Failed to reset credits. accountId: ${account.id} :: accountEmail: ${account.email}",
              err = Some(e),
            )

            Res.ServerError(
              message = "Failed to reset credits",
              e = None
            )

          }

      }

    }


  /**
    * 12 Feb 2025
    *
    * 1. The SR frontend sends a request to the SR backend for auto-login to the WH.
    * 2. The SR backend forwards the request to the WH backend,
    *     providing the WH account API key and the user's org_id.
    * 3. The WH backend generates an access token using the provided org_id,
    *     stores it in the cache for 60 seconds, and returns a redirect URL to the SR backend.
    * 4. The SR backend then sends the redirect URL to the SR frontend.
    * 5. The SR frontend redirects the user to the provided URL.
    * 6. The WH frontend parses the URL and sends a request to the WH backend for a session,
    *     including the access token and org_id from the URL.
    * 7. The WH backend validates the access token and org_id against the cached data.
    * 8. If the token and org_id are valid, the WH backend creates a session for the user.
    */
  def createAccessToken: Action[JsValue] =
    smartReachAuthMiddleware.isAuthorized.async(parse.json) { implicit request =>

      implicit val Logger: SRLogger = request.Logger
      val Res = request.Response

      request.body.validate[FindOrCreateAccountForm] match {

        case JsError(e) =>

          Logger.error(
            msg = s"createAccessToken - FindOrCreateAccountForm JsError: $e"
          )

          Future.successful(Res.JsValidationError(e.toSeq))

        case JsSuccess(findOrCreateAccountForm, _) =>

          AccountService.createLoginRedirectUrl(
            findOrCreateAccountForm = findOrCreateAccountForm,
            cacheService = cacheService,
          ).map {

            case Left(errMsg) =>

              Logger.error(
                msg = s"Failed to create redirect url - Left.  orgId: ${findOrCreateAccountForm.org_id} :: email: ${findOrCreateAccountForm.org_owner_email} :: errMsg: $errMsg",
              )

              Res.BadRequestError(
                message = errMsg,
              )

            case Right(redirectUrl) =>

              Res.Success(
                message = "Successfully created access token",
                data = Json.obj("redirect_to" -> redirectUrl),
              )

          }.recover { case e =>

            Logger.error(
              msg = s"Failed to create access token. orgId: ${findOrCreateAccountForm.org_id} :: email: ${findOrCreateAccountForm.org_owner_email}",
              err = e,
            )

            Res.ServerError(
              message = "Failed to create access token",
              e = None
            )

          }

      }

    }

  /**
    * 12 Feb 2025
    *
    * The redirect url the destructured in the WH frontend.
    *
    * 1. We extract the access token and org_id from the redirect url.
    * 2. Then create `DataForLoginViaAccessToken` with the
    *     help of the access token and org_id and ask for a session.
    */
  def loginViaAccessToken: Action[JsValue] =
    loggingAction.async(parse.json) { implicit request =>

      implicit val Logger: SRLogger = request.Logger
      val Res = request.Response

      request.body.validate[DataForLoginViaAccessToken] match {

        case JsError(e) =>

          Logger.error(
            msg = s"loginViaAccessToken - DataForLoginViaAccessToken JsError: $e"
          )

          Future.successful(Res.JsValidationError(e.toSeq))

        case JsSuccess(dataForLoginViaAccessToken, _) =>

          AccountService.checkTokenAndReturnAccount(
            token = dataForLoginViaAccessToken.token,
            orgId = dataForLoginViaAccessToken.org_id,
            cacheService = cacheService,
          ).map { account =>

            val isLocalhost = request.domain.toLowerCase == "localhost"

            Res.Success(
              message = "Logged in!",
              data = Json.obj(
                "code" -> "success",
                "account" -> account,
                "disable_analytics" -> isLocalhost
              ),
            ).withSession(
              "account_id" -> account.id.toString
            )

          }.recover { case e =>

            Logger.error(
              msg = s"Failed to login via access token. orgId: ${dataForLoginViaAccessToken.org_id}",
              err = e,
            )

            Res.ServerError(
              message = "Failed to login via access token",
              e = None,
            )

          }

      }

    }

  def forgotPassword() = loggingAction.async(parse.json) { implicit request =>

    implicit val Logger: SRLogger = request.Logger
    val Res = request.Response

    request.body.validate[ForgotPasswordForm] match {
      case JsError(e) =>
        Future.successful(
          Res.JsValidationError(errors = e.to(Seq))
        )

      case JsSuccess(data, _) =>
        val email = data.email.toLowerCase.trim

        AccountDB.findByEmail(email) match {

          case None =>
            Future.successful(
              Res.NotFoundError(CONSTANTS.API_MSGS.ERROR_NOT_FOUND_ACCOUNT)
            )

          case Some(account) =>

            val unVerifiedOTP = AccountDB.getUnVerifiedOTP(accountId = account.id)

            val emailVerificationCode = if(unVerifiedOTP.nonEmpty) unVerifiedOTP.get else genVerificationCode(account.id)

            AccountDB.addEmailVerificationCode(account.id, emailVerificationCode) match {

              case Failure(e) =>
                Future.successful(
                  Res.ServerError(err = e)
                )

              case Success(None) =>
                Future.successful(
                  Res.ServerError("Account not found", e = None)
                )

              case Success(Some(savedAccount)) => {

                val body = EmailHelper.resetPasswordEmail(
                  name = savedAccount.profile.first_name,
                  code = emailVerificationCode
                )

                EmailHelper.sendMailFromAdmin(
                  toEmail = savedAccount.email,
                  toName = Some(Helpers.getAccountName(profile = savedAccount.profile)),
                  subject = s"Reset your ${CONSTANTS.APP_NAME} password",
                  body = body,
                  Logger = Logger
                ) map { _ =>
                  Res.Success(CONSTANTS.API_MSGS.SUCCESS_FORGOT_PASSWORD, Json.obj())
                } recover { case e =>
                  Res.ServerError(err = e)
                }


              }


            }
        }

    }
  }


  def updatePasswordFromForgotPasswordFlow = loggingAction.async(parse.json) { implicit request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response

      request.body.validate[ResetPasswordForm] match {

        case JsError(e) => Res.JsValidationError(errors = e.to(Seq))
        case JsSuccess(data, _) =>

          val email = data.email.toLowerCase.trim

          AccountDB.getAccountIdFromOTP(code = data.code, email = email) match {
            case None =>

              Logger.error(s"Invalid One Time Password given, Please check and try again.: ${data.code}")

              Res.BadRequestError("Invalid One Time Password given, Please check and try again.").withNewSession

            case Some(accountId) =>

              AccountDB.updatePasswordAndResetVerificationCode(
                accountId = accountId,
                password = data.password,
                code = data.code
              ) match {

                case Failure(e) => Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

                case Success(None) =>

                  Logger.error(s"Invalid One Time Password given, Please check and try again.: ${data.code} :: $accountId")

                  Res.BadRequestError("Invalid One Time Password given, Please check and try again.")

                case Success(Some(account)) =>

                  cacheService.setSessionAccount(account)

                  Res.Success(CONSTANTS.API_MSGS.SUCCESS_UPDATED_PASSWORD, Json.obj())

              }
          }

      }

    }
  }


  def changePasswordLoggedinFlow() = isLoggedIn().async(parse.json) { implicit request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response

      val accountId = request.account.id

      request.body.validate[ChangePasswordForm] match {

        case JsError(e) => Res.JsValidationError(errors = e.to(Seq))
        case JsSuccess(data, _) =>

          if (!PasswordHasher.matches(request.account.password, data.old_password)) {

            Res.BadRequestError("Invalid attempt")

          } else {

            AccountDB.updatePasswordWhenUserIsLoggedIn(
              accountId = accountId,
              password = data.new_password
            ) match {

              case Failure(e) => Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))

              case Success(None) => Res.ServerError("Account not found", e = None)

              case Success(Some(account)) =>

                cacheService._resetTeamCache(account.id)

                Res.Success(CONSTANTS.API_MSGS.SUCCESS_UPDATED_PASSWORD, Json.obj())

            }
          }

      }


    }


  }

  def updateProfile() = isLoggedIn().async(parse.json) { request =>

    Future {

      val Logger = request.Logger
      val Res = request.Response

      val accountId = request.account.id

      val validateData = request.body.validate[UpdateAccountProfile]

      validateData match {

        case JsError(e) => Res.JsValidationError(errors = e.to(Seq))

        case JsSuccess(data, _) => {


            AccountDB.updateProfile(accountId, data) match {

              case Failure(e) => Res.ServerError("Error while updating account profile: ", e = Some(e))

              case Success(None) => Res.NotFoundError("There was a problem while updating your profile, please contact support")

              case Success(Some(account)) =>

                cacheService._resetTeamCache(account.id)


                Res.Success("Profile has been updated", Json.obj("account" -> AccountDB.find(request.account.id).get))

            }
        }
      }
    }
  }


  def updateSRSettings() = isLoggedIn().async(parse.json) { request =>

    val Res = request.Response

    val accountId = request.account.id

    implicit val logger: SRLogger = request.Logger

    val srApiKey = (request.body \ "sr_api_key").asOpt[String]

    srApiKey match {

      case None =>

        Future.successful(
          Res.BadRequestError("Please send a valid api key")
        )

      case Some(apiKey) =>

        AccountDB.getCreditsBySRApiKey(sr_api_key = apiKey, Logger = logger).flatMap {

          case None =>

            Future.successful(
              Res.BadRequestError("Invalid API Key")
            )

          case Some(sr_credits) =>

            if (sr_credits.total_allowed_warmup_emails == 0) {

              Future.successful(Res.BadRequestError(sr_credits.message_for_user))

            } else {

              AccountService.getAccountMetadata(
                sr_api_key = apiKey
              ).flatMap { metadata =>

                AccountDB.findAllBySrApiKey(srApiKey = apiKey) match {

                  case Failure(exception) =>

                    logger.error(
                      msg = s"Failed to find account by sr_api_key. accountId: ${request.account.id}",
                      err = exception
                    )

                    Future.failed(exception)

                  case Success(accountsLinkedToKey) =>

                    /**
                      * 30-Jul-2024
                      *
                      * We don't want to allow users to reintegrate the same api key
                      * in different WarmupHero accounts if
                      * `enable_internal_email_accounts_api_for_warmuphero` is `true`
                      */
                    if (
                      metadata.enable_internal_email_accounts_api_for_warmuphero &&
                        (
                          accountsLinkedToKey.length > 1 ||
                            (
                              accountsLinkedToKey.nonEmpty &&
                                accountsLinkedToKey.headOption.get.id != request.account.id
                              )
                          )
                    ) {

                      logger.info(
                        msg = s"API key is already in use with accounts: ${accountsLinkedToKey.map(_.id)}, failed to integrate in account: ${request.account.id}"
                      )

                      Future.successful(
                        Res.BadRequestError(
                          message = "This WarmupHero API key is already in use. Please check with your team if they have already set up a WarmupHero account. If you still have issues, contact SmartReach support."
                        )
                      )

                    } else {

                      AccountDB.updateSRSettings(accountId = accountId,
                        total_allowed_warmup_emails = sr_credits.total_allowed_warmup_emails,
                        apiKey = apiKey,
                        orgId = sr_credits.org_id,
                      ) match {

                        case Failure(e) =>
                          Future.successful(Res.ServerError("Error while updating settings ", e = Some(e)))

                        case Success(None) =>
                          Future.successful(Res.BadRequestError("Account not found. Please contact support"))

                        case Success(Some(account)) =>

                          cacheService._resetTeamCache(account.id)

                          Future.successful(
                            Res.Success("API key has been updated",
                              Json.obj("account" -> AccountDB.find(request.account.id).get)
                            )
                          )

                      }

                    }
                }

              }

            }

        }.recover { case e =>

          logger.error(
            msg = s"Failed to update settings. accountId: ${request.account.id}",
            err = e
          )

          Res.ServerError(err = e)

        }

    }

  }

  def getCreditsFromSRApiKey() = isLoggedIn().async { request =>

    val Res = request.Response
    val srApiKey = request.account.sr_api_key
    val logger = request.Logger

    srApiKey match {
      case None =>
        Future.successful(
          Res.BadRequestError("Invalid api key")
        )

      case Some(apiKey) => {

        val warmupLimit = request.account.total_allowed_warmup_emails

        Future.successful(
          Res.Success("credits found", Json.toJson(
            CreditResponse(
              total_allowed_warmup_emails = warmupLimit,
              message_for_user = s"Congrats! You can warmup upto ${warmupLimit} email accounts with your plan."
            )
          ))
        )

        /* 17-dec-2024: additional_fixed_email_settings was getting ignored while displaying to the user
        AccountDB.getCreditsBySRApiKey(sr_api_key = apiKey,Logger = logger).flatMap {

          case None =>

            Future.successful(
              Res.BadRequestError("Invalid API Key")
            )

          case Some(data) =>

            Future.successful(
              Res.Success("credits found", Json.toJson(data))
            )
        }.recover { case e =>
          Res.ServerError(err = e)
        }
        */
      }
    }
  }

  def getAccountMetadata(): Action[AnyContent] = isLoggedIn().async { request =>

    val Res = request.Response

    val srApiKeyOpt: Option[String] = request.account.sr_api_key

    implicit val logger: SRLogger = request.Logger

    srApiKeyOpt match {

      case None =>

        logger.error(
          msg = "Failed to fetch account metadata - not found sr_api_key"
        )

        Future.successful(
          Res.BadRequestError(message = "Not found api key")
        )

      case Some(srApiKey) =>

        AccountService.getAccountMetadata(
          sr_api_key = srApiKey,
        ).flatMap { data =>

          Future.successful(
            Res.Success("Account metadata found", Json.toJson(data))
          )

        }.recover { case e =>

          logger.error(
            msg = "Failed to fetch account metadata",
            err = e
          )

          Res.ServerError(err = e)

        }

    }

  }


  def signOut() = isLoggedIn().async { request =>

    val Res = request.Response

    cacheService.removeSessionAccount(request.account.id)
      .map(_ => {
        Res.Success("You've been logged out", Json.obj()).withNewSession
      })
      .recover { case e =>
        Res.ServerError(err = e)
      }

  }

  def me() = isLoggedIn().async { request =>
    implicit val Logger: SRLogger = request.Logger
    val Res = request.Response

    Logger.info(s"request $request")

    val isLocalhost = request.domain.toLowerCase == "localhost"

    val account = request.account

    Future.successful(
      Res.Success("Account found", Json.obj("account" -> account, "disable_analytics" -> isLocalhost))
    )

  }

  def verifyEmail = loggingAction.async(parse.json) { implicit request =>

    val Logger = request.Logger
    val Res = request.Response

    request.body.validate[VerifyEmailForm] match {

      case JsError(e) => Future.successful(Res.JsValidationError(errors = e.to(Seq)))
      case JsSuccess(data, _) =>

        val email = data.email.toLowerCase.trim
        AccountDB.getAccountIdFromOTP(data.code, email = email) match {
          case None =>
            Logger.error(s"Invalid OTP. Please try resend.: ${data.code}")

            Future.successful(Res.BadRequestError("Invalid OTP. Please try resend."))

          case Some(accountId) =>

            AccountDB.updateAccountEmailStatusIsVerified(
              accountId = accountId
            ) match {

              case Failure(e) => Future.successful(Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e)))

              case Success(None) =>

                Logger.error(s"Account not found. Please try again or contact support.: ${accountId}")

                Future.successful(Res.BadRequestError("Account not found. Please try again or contact support."))

              case Success(Some(account)) =>

                cacheService._resetTeamCache(account.id)

                val succRes = Res.Success(
                  CONSTANTS.API_MSGS.SUCCESS_EMAIL_VERIFIED,
                  Json.obj()
                )

                Future.successful(succRes)


            }
        }

    }
  }

  def resendVerificationEmail = loggingAction.async(parse.json) { implicit request =>

    implicit val Logger: SRLogger = request.Logger
    val Res = request.Response

    request.body.validate[ResendVerifyEmailForm] match {

      case JsError(e) =>

        Future.successful(
          Res.JsValidationError(errors = e.to(Seq))
        )

      case JsSuccess(data, _) =>

        val email = data.email.toLowerCase.trim
        AccountDB.findByEmail(email) match {

          case None => Future.successful(
            Res.BadRequestError(CONSTANTS.API_MSGS.ERROR_INVALID_REQUEST).withNewSession
          )

          case Some(account) =>

            if (account.email_verified) {

              Future.successful(
                Res.BadRequestError("Account already verified")
              )

            } else {

              AccountDB.getEmailVerificationCodeByAccountIDForResend(accountId = account.id) match {

                case Failure(e) =>
                  Future.successful(
                    Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + " :" + e.getMessage, e = Some(e))
                  )

                case Success(None) =>
                  Future.successful(

                    Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER + s" : email verification code not found for account ${account.id}", e = None)
                  )

                case Success(Some(emailVerificationCode)) =>

                  val apiDomain = AppConfig.apiDomain

                  val toName = Some(account.profile.first_name)

                  val body = EmailHelper.verifyEmail(
                    name = toName.get,
                    code = emailVerificationCode
                  )

                  EmailHelper.sendMailFromAdmin(
                    toEmail = account.email,
                    toName = toName,
                    subject = s"Verify your ${CONSTANTS.APP_NAME} email",
                    body = body,
                    Logger = Logger
                  ) map { _ =>
                    Res.Success("Verification email sent, please check your email", Json.obj())
                  } recover { case e =>
                    Res.ServerError(err = e)
                  }

              }
            }
        }
    }
  }

}
