package api.controllers

import akka.actor.ActorSystem
import api.{CONSTANTS, FreeEmailDomainList}
import api.services.{CacheService, EmailAccountService, EmailHealthCheckService}
import api.middleware.{AuthUtils, EmailAccountUtils, LoggingAction, SmartReachAuthMiddleware}
import api.models._
import org.apache.http.client.utils.URIBuilder
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsSuccess, JsValue, <PERSON>son, Reads}
import play.api.libs.ws.WSClient
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}
import utils.{EmailValidator, SRLogger}
import utils.email._

import javax.mail.Message
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class SrEmailData(
  emails: List[String]
)

object SrEmailData {
  implicit val reads: Reads[SrEmailData] = Json.reads[SrEmailData]
}

class EmailAccountController(
                              val cacheService: CacheService,
                              implicit val loggingAction: LoggingAction,
                              implicit val controllerComponents: ControllerComponents,
                              implicit val executionContext: ExecutionContext,
                              implicit val wsClient: WSClient,
                              implicit val system: ActorSystem,
                              implicit val smartReachAuthMiddleware: SmartReachAuthMiddleware,
                            ) extends BaseController with AuthUtils with EmailAccountUtils {


  def findAll() =  isLoggedIn().async  { implicit request =>
    Future {
    val Logger = request.Logger
    val Res = request.Response

      val accountId = request.account.id
      EmailAccount.findAll(accountId) match {

        case Failure(e) =>

          Logger.fatal(s"findAll: $accountId", err = e)

          Res.ServerError("There was an error. Please try again.", e = Some(e))

        case Success(emailAccounts) =>

          Res.Success("Email accounts found", Json.obj("email_accounts" -> emailAccounts))
      }


    }

  }


  def findAllFromSR(
    older_than: Option[Long],
    newer_than: Option[Long]
  ): Action[AnyContent] = isLoggedIn().async { implicit request =>

    implicit val logger: SRLogger = request.Logger

    val Res = request.Response

    val accountId = request.account.id

    if (older_than.isDefined && newer_than.isDefined) {

      Future.successful(
        Res.BadRequestError(message = "Both newer_than and older_than cannot be defined at once.")
      )

    } else {

      request.account.sr_api_key match {

        case None =>

          Future.successful(
            Res.BadRequestError(message = "Not found API Key.")
          )

        case Some(srApiKey) =>

          EmailAccountService.getEmailAccountsFromSR(
            accountId = accountId,
            sr_api_key = srApiKey,
            older_than = older_than,
            newer_than = newer_than,
          ).map { emailSettings =>

            val nextLinkQueryParamsMapOpt: Option[Map[String, String]] =
              emailSettings.links.next.map { nextLinkStr =>

                EmailAccountController.extractQueryParamsFromLink(
                  link = nextLinkStr
                )

              }

            val prevLinkQueryParamsMapOpt: Option[Map[String, String]] =
              emailSettings.links.prev.map { prevLinkStr =>

                EmailAccountController.extractQueryParamsFromLink(
                  link = prevLinkStr
                )

              }

            val emailSettingsWithUpdatedLinks: SrEmailSettingsRes = emailSettings.copy(
              links = NavigationLinksPrevNextResponse(
                prev = prevLinkQueryParamsMapOpt.flatMap(_.get("newer_than")),
                next = nextLinkQueryParamsMapOpt.flatMap(_.get("older_than"))
              )
            )

            Res.Success(
              message = "Email accounts found",
              data = Json.toJson(emailSettingsWithUpdatedLinks)
            )

          }.recover { e =>

            logger.fatal(
              msg = s"Failed to fetch email accounts from SmartReach: $accountId",
              err = e
            )

            Res.ServerError(
              message = "There was an error. Please try again.",
              e = Some(e)
            )

          }

      }

    }

  }

  def find(id: Long) =  (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async  { implicit request =>
    Future {
      val Logger = request.Logger
      val Res = request.Response

      Res.Success("Email account found", Json.obj("email_account" -> request.emailAccount))

    }

  }

  def startWarmupFromSr(
    email_setting_uuid: String,
  ): Action[AnyContent] = smartReachAuthMiddleware.hasWarmupHeroAccount.async { implicit request =>

    implicit val Logger: SRLogger = request.Logger

    val Res = request.Response

    request.account.sr_api_key match {

      case None =>

        Logger.shouldNeverHappen(
          msg = s"Not found API key. accountId: ${request.account.id}",
          err = None,
        )

        Future.successful {

          Res.BadRequestError(
            message = "Not found API Key.",
          )

        }

      case Some(apiKey) =>

        EmailAccountService.activateOrConnectEmailAccount(
          email_setting_uuid = email_setting_uuid,
          sr_api_key = apiKey,
          account = request.account,
        ).map {

          case Left(errMsg) =>

            Logger.error(
              msg = s"activateOrConnectEmailAccount failed - Left. accountId: ${request.account.id} :: accountEmail: ${request.account.email} :: email_setting_uuid: $email_setting_uuid :: errMsg: $errMsg",
            )

            Res.BadRequestError(
              message = errMsg
            )

          case Right(emailAccountStatus) =>

            Res.Success(
              message = "Email account connected to WarmupHero",
              data = Json.toJson(emailAccountStatus),
            )

        }.recoverWith { case e =>

          Logger.error(
            msg = s"activateOrConnectEmailAccount failed. accountId: ${request.account.id} :: accountEmail: ${request.account.email} :: email_setting_uuid: $email_setting_uuid",
            err = e,
          )

          Future.successful {

            Res.ServerError(
              message = "Failed to connect email account to WarmupHero",
              e = None,
            )

          }

        }

    }

  }

  def findEmailAccountWarmupStatus: Action[JsValue] =
    smartReachAuthMiddleware.hasWarmupHeroAccount.async(parse.json) { implicit request =>

      implicit val Logger: SRLogger = request.Logger

      val Res = request.Response

      request.account.sr_api_key match {

        case None =>

          Logger.shouldNeverHappen(
            msg = s"Not found API key. accountId: ${request.account.id}",
            err = None,
          )

          Future.successful {

            Res.BadRequestError(
              message = "Not found API Key.",
            )

          }

        case Some(apiKey) =>

          request.body.validate[SrEmailData] match {

            case JsError(errors) =>

              Logger.error(
                msg = s"findEmailAccountWarmupStatus failed. accountId: ${request.account.id} :: accountEmail: ${request.account.email} :: errors: $errors",
              )

              Future.successful(
                Res.JsValidationError(errors = errors.to(Seq))
              )

            case JsSuccess(emailData, _) =>

              EmailAccountService.findEmailAccountWarmupStatus(
                whAccountApiKey = apiKey,
                accountId = request.account.id,
                emails = emailData.emails,
              ) match {

                case Failure(exception) =>

                  Logger.error(
                    msg = s"findEmailAccountWarmupStatus failed. accountId: ${request.account.id} :: accountEmail: ${request.account.email}",
                    err = exception,
                  )

                  Future.successful(
                    Res.ServerError(
                      message = "Failed to fetch email account warmup status",
                      e = None,
                    )
                  )

                case Success(emailAccountWarmupStatusList) =>

                  Future.successful(
                    Res.Success(
                      message = "Email account warmup status found",
                      data = Json.toJson(emailAccountWarmupStatusList)
                    )
                  )

              }
          }
      }

    }

  def connectSrEmailAccount(
    email_setting_uuid: String,
  ): Action[AnyContent] = isLoggedIn().async { implicit request =>

    implicit val Logger: SRLogger = request.Logger

    val Res = request.Response

    request.account.sr_api_key match {

      case None =>

        Logger.error(
          msg = s"connectSrEmailAccount - API key not found. email_setting_uuid: $email_setting_uuid"
        )

        Future.successful(
          Res.BadRequestError(message = "Not found API Key.")
        )

      case Some(apiKey) =>

        EmailAccountService.connectSrEmailAccount(
          email_setting_uuid = email_setting_uuid,
          account = request.account,
          sr_api_key = apiKey,
        ).map {

          case Left(errMsg) =>

            Logger.error(
              msg = s"connectSrEmailAccount failed. accountId: ${request.account.id} :: accountEmail: ${request.account.email} :: email_setting_uuid: $email_setting_uuid :: errMsg: $errMsg"
            )

            Res.BadRequestError(
              message = errMsg,
            )

          case Right(value) =>

            Res.Success(
              message = "Email account connected to WarmupHero",
              data = Json.toJson(value)
            )

        }.recover { case e =>

          Logger.error(
            msg = s"connectSrEmailAccount failed. accountId: ${request.account.id} :: accountEmail: ${request.account.email} :: email_setting_uuid: $email_setting_uuid",
            err = e,
          )

          Res.ServerError(
            message = "There was an error. Please try again.",
            e = None,
          )

        }

    }

  }


  def create() = isLoggedIn().async(parse.json) { implicit request =>


    Future {
      implicit val Logger = request.Logger
      val Res = request.Response

      val account = request.account
      if (account.total_allowed_warmup_emails <= account.total_active_warmup_emails) {
        Logger.error(s"[CreateAccount Error] : total_allowed_warmup_emails is less than or equal to total_active_warmup_emails , ${account.id}")
        Res.BadRequestError("You have reached the email account limit on your plan. Please increase the number of seats to add more email accounts for warmup")
      }
      else {


        val validateData = request.body.validate[EmailAccountForm]

        validateData match {

          case JsError(e) => Res.JsValidationError(errors = e.to(Seq))

          case JsSuccess(emailAccountForm, _) =>

            Logger.info(s"emailAccountForm encrypt: $emailAccountForm")

            val (_, domain) = EmailValidator.getLowercasedNameAndDomainFromEmail(email = emailAccountForm.email)

            val email = emailAccountForm.email.trim.toLowerCase

            val smtpUsername = emailAccountForm.smtp_username.trim.toLowerCase

            val imapUsername = emailAccountForm.imap_username.trim.toLowerCase

            /** Validations
              * according to logic
              * email should be work email and white listed
              * email and smtp_username should be same
              * smtp_username and imap_username should be email address in WarmupHero not allowing usernames
              * */
            if (
              FreeEmailDomainList.isFreeEmailDomain(domain = domain.toLowerCase) &&
                !FreeEmailDomainList.isWhitelistedForSignup(email = emailAccountForm.email.toLowerCase.trim)
            ) {

              Logger.error(s"email account integration attempt blocked: BLACKLISTED_DOMAINS $domain :: ${emailAccountForm.email} :: host: ${request.host} :: X-Real-IP: ${request.headers.get("X-Real-IP")} :: X-Forwarded-For : ${request.headers.get("X-Forwarded-For")} :: X-Forwarded-Proto: ${request.headers.get("X-Forwarded-Proto")}")

              Res.BadRequestError("Please use your work email to warm up")

            } else if (email != smtpUsername) {

              Logger.error(s"email account integration attempt blocked: BLACKLISTED_DOMAINS $domain :: ${emailAccountForm.email} :: host: ${request.host} :: X-Real-IP: ${request.headers.get("X-Real-IP")} :: X-Forwarded-For : ${request.headers.get("X-Forwarded-For")} :: X-Forwarded-Proto: ${request.headers.get("X-Forwarded-Proto")}")

              Res.BadRequestError("Please ensure email and SMTP email address must be same")

            } else if (!EmailValidator.validateEmail(email)) {

              Res.BadRequestError("Please send valid sending email")

            } else if (!EmailValidator.validateEmail(smtpUsername)) {

              Res.BadRequestError("Please send valid SMTP email address")

            } else if (!EmailValidator.validateEmail(imapUsername)) {

              Res.BadRequestError("Please send valid IMAP email address ")

            } else {

              EmailAccountService.createEmailAccount(
                account = account,
                emailAccountForm = emailAccountForm,
                isPurchasedFromSr = false, // user is manually connecting the email account.
              ) match {

                case Failure(e) =>

                  Logger.error(
                    msg = s"Failed to create email account. accountId: ${account.id} :: accountEmail: ${account.email} :: failedToConnectEmail: ${emailAccountForm.email}",
                    err = e,
                  )

                  Res.ServerError(
                    message = "Error while adding email account. Try again, or contact support",
                    e = None,
                  )

                case Success(Left(errMsg)) =>

                  Logger.error(
                    msg = s"Failed to create email account. accountId: ${account.id} :: accountEmail: ${account.email} :: failedToConnectEmail: ${emailAccountForm.email} - errMsg: $errMsg",
                  )

                  Res.BadRequestError(
                    message = errMsg,
                  )

                case Success(Right(row)) => Res.Success(s"Email account (${row.email}) has been added", Json.obj("email_account" -> row))

              }

            }

        }

      }

    }

  }

  def updateBasicInfo(id: Int) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async(parse.json) { implicit request =>


    Future {

      val Logger = request.Logger
      val Res = request.Response

      request.body.validate[BasicAccountForm] match {

        case JsError(e) => Res.JsValidationError(errors = e.to(Seq))

        case JsSuccess(data, _) =>

          Logger.info(s"EmailAccountController.update attempt: ea_$id : reqdata: $data")

          EmailAccount.updateBasicDetails(id=id, data=data) match {

            case Failure(e) =>

              Res.ServerError(s"Error while updating email account: ${e.getMessage}", e = Some(e))


            case Success(None) => Res.NotFoundError("Email account not found. Could you check and try again ?")

            case Success(Some(row)) => Res.Success(s"Email account (${row.email}) has been updated", Json.obj("email" -> row))

          }
      }
    }

  }

  def update(id: Int) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async(parse.json) { implicit request =>


    Future {

      val Logger = request.Logger
      val Res = request.Response

      request.body.validate[EmailAccountForm] match {

        case JsError(e) => Res.JsValidationError(errors = e.to(Seq))

        case JsSuccess(data, _) =>

            Logger.info(s"EmailAccountController.update attempt: ea_$id : reqdata: $data")

            EmailAccount.update(id, data) match {

              case Failure(e) => e.getMessage match {

                case msg if msg.contains("email_accounts_account_id_email_key") => Res.BadRequestError(s"You have already added ${data.email} to your WarmupHero account")

                case msg => Res.ServerError("Error while updating email account: " + msg, e = Some(e))

              }


              case Success(None) => Res.NotFoundError("Email account not found. Could you check and try again ?")

              case Success(Some(row)) => Res.Success(s"Email account (${row.email}) has been updated", Json.obj("email" -> row))

          }
      }
    }

  }


  def updateSignature(id: Int) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async(parse.json) { implicit request =>


    Future {
      val Logger = request.Logger
      val Res = request.Response

      request.body.validate[EmailAccountUpdateSignatureForm] match {

        case JsError(e) => Res.JsValidationError(errors = e.to(Seq))

        case JsSuccess(data, _) =>

          Logger.info(s"EmailAccountController.updateSignature attempt: eset_$id : reqdata: $data")

          EmailAccount.updateSignature(id, data) match {

            case Failure(e) => Res.ServerError(s"Error while updating signature", e = Some(e))

            case Success(None) => Res.NotFoundError("Email account not found")

            case Success(Some(row)) => Res.Success("Signature updated", Json.obj("email_account" -> row))

          }

      }
    }
  }

  def updateWarmupFrequencySettings() =
    isLoggedIn().async(parse.json) { implicit request =>

      Future {

        implicit val Logger: SRLogger = request.Logger

        val Res = request.Response

        request.body.validate[EmailAccountUpdateFrequencySettingsForm] match {

          case JsError(e) =>

            Logger.error(
              msg = s"Failed to parse EmailAccountUpdateFrequencySettingsForm: $e"
            )

            Res.JsValidationError(errors = e.to(Seq))

          case JsSuccess(data, _) =>

            if (data.email_account_ids.isEmpty) {

              Logger.warn(
                msg = s"Failed to update warmup frequency settings - email_account_ids is empty. accountId: ${request.account.id} :: emailAccountIds: ${data.email_account_ids}",
              )

              Res.BadRequestError("Please send at least one email account id")

            } else {

              Logger.info(s"EmailAccountController.updateSignature attempt: eset_${data.email_account_ids} : reqdata: $data")

              EmailAccountService.hasEmailAccounts(
                accountId = request.account.id,
                emailAccountIds = data.email_account_ids,
              ) match {

                case Failure(e) =>

                  Logger.shouldNeverHappen(
                    msg = s"Failed to check if email accounts belong to account. accountId: ${request.account.id} :: emailAccountIds: ${data.email_account_ids}",
                    err = Some(e),
                  )

                  Res.ServerError(
                    message = s"Error while updating warmup frequency settings",
                    e = None,
                  )

                case Success(false) =>

                  Logger.error(
                    msg = s"Email accounts do not belong to account. accountId: ${request.account.id} :: emailAccountIds: ${data.email_account_ids}",
                  )

                  Res.BadRequestError(message = "One or more email accounts do not belong to your account")

                case Success(true) =>

                  if (data.emails_per_day > 30) {

                    Res.BadRequestError("Maximum sends per day should not be more than 30")

                  } else if (data.emails_increase_per_day > 4) {

                    Res.BadRequestError("Ramp-up increase per day should not be more than 4")

                  } else {

                    EmailAccount.updateFrequencySettings(
                      accountId = request.account.id,
                      emailAccountIds = data.email_account_ids,
                      date = data,
                    ) match {

                      case Failure(e) =>

                        Logger.error(
                          msg = s"Failed to update warmup frequency settings. accountId: ${request.account.id} :: emailAccountIds: ${data.email_account_ids}",
                          err = e,
                        )

                        Res.ServerError(s"Error while updating warmup frequency settings", e = None)

                      case Success(row) => Res.Success("Warmup frequency settings updated", Json.obj("email_accounts" -> row))

                    }

                  }

              }

            }
        }

      }

    }


  def testSettings() = isLoggedIn().async(parse.json) { request =>
    implicit val Logger = request.Logger
    val Res = request.Response


    request.body.validate[EmailAccountForm] match {

      case JsError(e) => Future.successful(Res.JsValidationError(errors = e.to(Seq)))

      case JsSuccess(data, _) =>

        val (_, domain) = EmailValidator.getLowercasedNameAndDomainFromEmail(email = data.email)

        val email  = data.email.trim.toLowerCase

        val smtpUsername = data.smtp_username.trim.toLowerCase

        val imapUsername = data.imap_username.trim.toLowerCase

        if (
          FreeEmailDomainList.isFreeEmailDomain(domain = domain.toLowerCase) &&
            !FreeEmailDomainList.isWhitelistedForSignup(email = data.email.toLowerCase.trim)
        ) {

          Logger.error(s"email account integration attempt blocked: BLACKLISTED_DOMAINS $domain :: ${data.email} :: host: ${request.host} :: X-Real-IP: ${request.headers.get("X-Real-IP")} :: X-Forwarded-For : ${request.headers.get("X-Forwarded-For")} :: X-Forwarded-Proto: ${request.headers.get("X-Forwarded-Proto")}")

          Future.successful(Res.BadRequestError("Please use your work email to warm up"))

        } else if(email != smtpUsername) {

          Future.successful(Res.BadRequestError("Please ensure email and SMTP email address must be same"))

        } else if(!EmailValidator.validateEmail(email)) {

          Future.successful(Res.BadRequestError("Please send valid sending email"))

        } else if(!EmailValidator.validateEmail(smtpUsername)) {

          Future.successful(Res.BadRequestError("Please send valid SMTP email address"))

        } else if(!EmailValidator.validateEmail(imapUsername)) {

          Future.successful(Res.BadRequestError("Please send valid IMAP email address "))

        } else {

          val account = request.account

          val testFut = EmailAccountService.testEmailSetting(
            account = account,
            data = data,
          )

          testFut.map(e => {

            Logger.info(s"Your email account account have been successfully tested :: data: $data")

            Res.Success("Your email account account have been successfully tested", Json.obj())
          })
            .recover { case e => {

              Logger.error(
                msg = s"There was an error while testing your email account :: data: $data",
                err = e
              )

              Res.BadRequestError("There was an error while testing your email account: " + e.getMessage)
            }
            }
        }

    }
  }


  def checkEmailHealth(id: Long) = (
    isLoggedIn() andThen hasEmailAccount(emailAccountId = id)
    ).async(parse.json) { implicit request =>

    implicit val Logger: SRLogger = request.Logger

    val Res = request.Response

    EmailHealthCheckService.createdPendingEmailHealthCheckRecord(
      emailAccountId = EmailAccountId(id = id),
    ).map { emailHealthCheckRecord =>

      Res.Success(
        message = "Email health check record created",
        data = Json.toJson(emailHealthCheckRecord),
      )

    }.recover { err =>

      Logger.error(
        msg = s"Failed to create email health check record. emailAccountId: $id",
        err = err,
      )

      Res.ServerError(
        message = "Failed to create email health check record",
        e = None,
      )

    }

  }



  def delete(id: Int)= (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async(parse.json) { implicit request =>

    Future {
      val Logger = request.Logger
      val Res = request.Response

      Logger.info(s"DELETE deleteEmailAccount called by ${request.account.id} :: $id")

          EmailAccount.delete(emailAccountId = id) match {

            case Failure(e) =>
                Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = Some(e))

            case Success(n) =>

              if (n == 1) Res.Success(s"Deleted email account successfully", data = Json.obj())
              else Res.NotFoundError(s"Email account not found. Could you please check and retry ?")

          }


    }

  }


  def activate() = isLoggedIn().async(parse.json) { implicit request =>

    Future {

      val Res = request.Response
      val account_id = request.account.id

      implicit val Logger: SRLogger = request.Logger

      request.body.validate[ActivateOrDeactivateEmailAccounts] match {

        case JsError(e) =>

          Logger.error(s"EmailAccountController.activate failed. accountId: $account_id :: errors: $e")

          Res.JsValidationError(errors = e.to(Seq))

        case JsSuccess(data, _) =>

          val emailAccountIds = data.email_account_ids

          if (emailAccountIds.isEmpty) {

            Logger.error(s"EmailAccountController.activate failed. accountId: $account_id :: emailAccountIds: $emailAccountIds")

            Res.BadRequestError("Please provide at least one email account id")

          } else {

            EmailAccountService.hasEmailAccounts(
              accountId = account_id,
              emailAccountIds = emailAccountIds,
            ) match {

              case Failure(e) =>

                Logger.shouldNeverHappen(
                  msg = s"Failed to check if email accounts belong to account. accountId: $account_id :: emailAccountIds: $emailAccountIds",
                  err = Some(e),
                )

                Res.ServerError(
                  message = "Failed to activate email accounts",
                  e = None,
                )

              case Success(false) =>

                Logger.error(
                  msg = s"Email accounts do not belong to account. accountId: $account_id :: emailAccountIds: $emailAccountIds",
                )

                Res.BadRequestError(message = "One or more email accounts do not belong to your account")

              case Success(true) =>

                EmailAccount.activateOrDeactivateEmailAccount(
                  accountId = account_id,
                  emailAccountIds = emailAccountIds,
                  active = true,
                ) match {

                  case Failure(e) =>

                    Logger.error(
                      msg = s"Failed to activate email accounts. accountId: $account_id :: emailAccountIds: $emailAccountIds",
                      err = e,
                    )

                    Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None)

                  case Success(row) =>

                    EmailAccount.clearError(emailAccountIds = row.map(_.id), accountId = account_id) match {

                      case Failure(e) =>

                        Logger.error(
                          msg = s"Failed to clear error for email accounts. accountId: $account_id :: emailAccountIds: $emailAccountIds",
                          err = e,
                        )

                        Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None)

                      case Success(_) =>

                        Res.Success(s"Warm up started successfully", Json.obj("email_accounts" -> row))

                    }

                }

            }

          }

      }

    }

  }

  def deactivate() = isLoggedIn().async(parse.json) { implicit request =>

    Future {

      val Res = request.Response

      implicit val Logger: SRLogger = request.Logger

      val account_id = request.account.id

      request.body.validate[ActivateOrDeactivateEmailAccounts] match {

        case JsError(e) =>

          Logger.error(s"EmailAccountController.deactivate failed. accountId: $account_id :: errors: $e")

          Res.JsValidationError(errors = e.to(Seq))

        case JsSuccess(data, _) =>

          val emailAccountIds = data.email_account_ids

          if (emailAccountIds.isEmpty) {

            Logger.error(s"EmailAccountController.deactivate failed. accountId: $account_id :: emailAccountIds: $emailAccountIds")

            Res.BadRequestError("Please provide at least one email account id")

          } else {

            EmailAccountService.hasEmailAccounts(
              accountId = account_id,
              emailAccountIds = emailAccountIds,
            ) match {

              case Failure(e) =>

                Logger.shouldNeverHappen(
                  msg = s"Failed to check if email accounts belong to account. accountId: $account_id :: emailAccountIds: $emailAccountIds",
                  err = Some(e),
                )

                Res.ServerError(
                  message = "Failed to deactivate email accounts",
                  e = None,
                )

              case Success(false) =>

                Logger.error(
                  msg = s"Email accounts do not belong to account. accountId: $account_id :: emailAccountIds: $emailAccountIds",
                )

                Res.BadRequestError(message = "One or more email accounts do not belong to your account")

              case Success(true) =>

                EmailAccount.activateOrDeactivateEmailAccount(emailAccountIds = emailAccountIds, accountId = account_id, active = false) match {

                  case Failure(e) =>

                    Logger.error(
                      msg = s"Failed to deactivate email accounts. accountId: $account_id :: emailAccountIds: $emailAccountIds",
                      err = e,
                    )

                    Res.ServerError(CONSTANTS.API_MSGS.ERROR_INTERNAL_SERVER, e = None)

                  case Success(row) =>

                    Res.Success(s"Warm up started successfully", Json.obj("email_accounts" -> row))

                }
            }

          }

      }

    }

  }

}


object EmailAccountController {

  /**
    * 05-Aug-2024
    *
    * Example input:
    * /api/v3/settings/warmuphero/email_settings?older_than=*************
    *
    * Example output:
    * Map(older_than -> *************)
    */
  private def extractQueryParamsFromLink(link: String): Map[String, String] = {

    val tryOfQueryParams: Try[Map[String, String]] = Try {

      val queryParamsMap = scala.collection.mutable.Map.empty[String, String]

      new URIBuilder(link).getQueryParams.iterator.forEachRemaining { param =>

        queryParamsMap(param.getName) = param.getValue

      }

      queryParamsMap.toMap

    }

    tryOfQueryParams
      .getOrElse(Map.empty[String, String])

  }

}
