package api.controllers

import akka.actor.ActorSystem
import api.{AppConfig, FreeEmailDomainList}
import api.accounts.MicrosoftOAuth
import api.emails.EmailToBeSent
import api.middleware.{AuthUtils, LoggingAction, SmartReachAuthMiddleware}
import api.models.{Account, AccountDB, EmailAccount, EmailOAuthAccountForm, EmailServiceProvider, EmailSettings, EmailTestForm, MsClientIdVersion}
import api.services.{CacheService, EmailAccountService}
import play.api.libs.json.Json
import play.api.mvc.{BaseController, ControllerComponents}
import play.api.libs.ws.WSClient
import utils.{EmailValidator, ParseUtils, SRLogger}
import org.joda.time.DateTime
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import io.smartreach.esp.utils.email.models.EmailReply
import utils.email.{OutlookApi, SendEmailResponse}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class OAuthState(
                       id: Long,
                       serviceProvider: String
                     )

class OAuthController(
                       val cacheService: CacheService,
                       implicit val controllerComponents: ControllerComponents,
                       implicit val loggingAction: LoggingAction,
                       implicit val wsClient: WSClient,
                       implicit val ec: ExecutionContext,
                       implicit val system: ActorSystem,
                       implicit val smartReachAuthMiddleware: SmartReachAuthMiddleware,
                     ) extends BaseController
  with AuthUtils  {

  val gauthStateDelimiter = "___"

  def _createAuthState(stateData: OAuthState) = {

    val s = stateData

    val gap = gauthStateDelimiter

    val state = s.id.toString +
      gap + s.serviceProvider

    state
  }


  def _deconstructGAuthState(state: String): Option[OAuthState] = {

    // REF: https://stackoverflow.com/a/********
    val splits = state.split(gauthStateDelimiter, -1).toVector

    ParseUtils.parseLong(splits(0)).map { aid =>
      val serviceProvider = splits(1)

      OAuthState(
        id = aid,
        serviceProvider = serviceProvider,
      )
    }
  }

  def getOAuthUrl(service_provider: String, email_address: Option[String]) = isLoggedIn().async {

    implicit request =>

      val Logger = request.Logger
      val account = request.account

      val Res = request.Response

        if (service_provider == EmailServiceProvider.MICROSOFT_365_API.toString) {
        val state = _createAuthState(stateData = OAuthState(
          id = account.id,
          serviceProvider = service_provider
        ))

        val authorizationUrl = MicrosoftOAuth.authorizationUrl(
          state = state,
          s = AppConfig.getMicrosoftOAuthSettings(
            isZapmailFlow = false,
            version = MsClientIdVersion.V2, // use new client ID for new accounts
          ),
          emailAddress = email_address,
          promptLogin = true
        )


        Future {
          Res.Success("Redirect user to Microsoft auth page", Json.obj("redirect_to" -> authorizationUrl))
        }
      }
      else {
        Future {
          Res.BadRequestError("Invalid Service Provider")
        }
      }
  }

  def getOAuthUrlZapmail(service_provider: String, email_address: String) = smartReachAuthMiddleware.hasWarmupHeroAccount.async {

    implicit request =>

      val Logger = request.Logger
      val account = request.account

      val Res = request.Response

      if (service_provider == EmailServiceProvider.MICROSOFT_365_API.toString) {
        val state = _createAuthState(stateData = OAuthState(
          id = account.id,
          serviceProvider = service_provider
        ))

        val authorizationUrl = MicrosoftOAuth.authorizationUrl(
          state = state,
          s = AppConfig.getMicrosoftOAuthSettings(
            isZapmailFlow = true,
            version = MsClientIdVersion.V2, // use new client ID for new accounts
          ),
          emailAddress = None,
          promptLogin = true
        )

        Future {
          Res.Success("Redirect user to Microsoft auth page", Json.obj("redirect_to" -> authorizationUrl))
        }
      }
      else {
        Future {
          Res.BadRequestError("Invalid Service Provider")
        }
      }
  }

  def __outlookAuthFetchTokens(
                                code: String,
                                account: Account,
                                failedLogErrorPartial: String,
                                failedAuthorizationErrorMsg: String,
                                isZapmailFlow: Boolean,
                                Logger: SRLogger
                              ): Future[(EmailOAuthAccountForm, Seq[EmailOAuthAccountForm])] = {

    val FAILED_AUTH_EXCEPTION = new Exception(failedAuthorizationErrorMsg)


    // if code is there as a query param in the url, then this a successful redirect from google oauth flow
    MicrosoftOAuth.getAccessToken(
      code,
      Logger = Logger,
      s = AppConfig.getMicrosoftOAuthSettings(
        isZapmailFlow = isZapmailFlow,
        version = MsClientIdVersion.V2, // use new client ID for new accounts
      ),
    ).flatMap { access_token =>

      if (access_token.isEmpty) {

        Logger.fatal(s"1: $failedLogErrorPartial")
        Future.failed(FAILED_AUTH_EXCEPTION)

      } else {

        val accessTokenData = access_token.get


        val userProfileDataFuture = MicrosoftOAuth.getProfile(
          accessTokenData.access_token,
          Logger = Logger
        )

        userProfileDataFuture.flatMap { userProfileData => {


          val failedGetProfileErrorMsg = "We could not find a valid outlook mailbox in your account. Please contact support if you have any questions."
          val FAILED_GET_PROFILE_EXCEPTION = new Exception(failedGetProfileErrorMsg)

          if (accessTokenData.refresh_token.isEmpty ||
            accessTokenData.expires_in.isEmpty ||
            accessTokenData.token_type.isEmpty) {


            Logger.fatal(s"2: $failedLogErrorPartial")

            Future.failed(FAILED_AUTH_EXCEPTION)

          } else if (userProfileData.mail.isEmpty) {

            Logger.fatal(s"3: $failedGetProfileErrorMsg :: Outlook UserDetails: ${userProfileData} :: warmuphero Account Id: ${account.id}")

            Future.failed(FAILED_GET_PROFILE_EXCEPTION)

          }
          else {

            val email = userProfileData.mail.get


            val parentEmailSettingForSaving = EmailOAuthAccountForm(
              email = email,
              service_provider = EmailServiceProvider.MICROSOFT_365_API,
              oauth2_access_token = accessTokenData.access_token,
              oauth2_refresh_token = accessTokenData.refresh_token.get,
              oauth2_token_type = accessTokenData.token_type.get,
              oauth2_token_expires_in = accessTokenData.expires_in.get,
              oauth2_access_token_expires_at = DateTime.now().plusSeconds(accessTokenData.expires_in.get),
              first_name = userProfileData.givenName.getOrElse(""),
              last_name = userProfileData.surname.getOrElse(""),
            )

            val aliasEmailSettingForSaving = Seq() // outlook aliases not supported

            Future.successful((parentEmailSettingForSaving, aliasEmailSettingForSaving))

          }


        }

        }

      }


    }
  }

  def getOAuthCode(code: String) = isLoggedIn().async {

    implicit request =>

      implicit val Logger = request.Logger
      val account = request.account

      val Res = request.Response

      val codeOpt = request.getQueryString("code")
      val state = request.getQueryString("state")

      if (state.isEmpty) {

        Logger.fatal(s"OAuthFetchTokens error: state param missing, ${account.id}")

        Future.successful(Res.BadRequestError("We need the state param from service provider. That is missing. Sorry about this. Please contact us"))
      } else {

        val s = _deconstructGAuthState(state.get)
        if (s.isEmpty) {
          Future.successful(Res.BadRequestError("Auth state not defined"))
        }
        else {

          val authState = s.get
          val serviceProvider = authState.serviceProvider

          if (authState.id != account.id) {
            Future.successful(Res.BadRequestError("Pass valid aid"))
          }
          else {
            codeOpt match {

              case None => Future.successful(Res.BadRequestError("Invalid authorization code"))
              case Some(code) =>

                val failedAuthorizationErrorMsg = s"Failed $serviceProvider Authorization. Please retry or contact support."
                val failedLogErrorPartial = s"${serviceProvider}AuthFetchTokens [FATAL]: Failed: ${account.id} :: $state :: $code"

                val emailSettingsForSaving = if (serviceProvider == EmailServiceProvider.MICROSOFT_365_API.toString) {
                  __outlookAuthFetchTokens(
                    code = code,
                    account = account,
                    failedLogErrorPartial = failedLogErrorPartial,
                    failedAuthorizationErrorMsg = failedAuthorizationErrorMsg,
                    isZapmailFlow = false,
                    Logger = Logger
                  )
                }
                else {
                  Future.failed(new Exception(s"Invalid service provider: $serviceProvider"))
                }


                emailSettingsForSaving
                  .flatMap {
                    case (parentEmailSettingForSaving, aliasEmailSettingsForSaving) => {

                      EmailAccountService.validateAndConnectMicrosoftOAuthSettings(
                        parentEmailSettingForSaving = parentEmailSettingForSaving,
                        account = account,
                      ).map {

                        case Left(errMsg) =>

                          Logger.error(
                            msg = s"validateAndConnectMicrosoftOAuthSettings failed. accountId: ${account.id} :: accountEmail: ${account.email} :: failedToConnectEmail: ${parentEmailSettingForSaving.email} - errMsg: $errMsg",
                          )

                          Res.BadRequestError(
                            message = errMsg,
                          )

                        case Right(emailSettingId) =>

                          Res.Success(
                            message = "Email account connected to WarmupHero",
                            data = Json.obj("email_setting_id" -> emailSettingId.emailSettingId),
                          )

                      }

                    }
                  }
            }

          }.recover { case e =>

            Logger.fatal(s"${serviceProvider}AuthFetchTokens error: emailSettingsForSaving, ${account.id}", err = e)

            Res.BadRequestError(e.getMessage)
          }
        }
      }
  }

  def getOAuthCodeZapmail(code: String) = loggingAction.async { implicit request =>

    implicit val Logger: SRLogger = request.Logger

    Logger.info(msg = s"getOAuthCodeZapmail called - start")

    val Res = request.Response

    val codeOpt = request.getQueryString("code")
    val state = request.getQueryString("state")

    // TODO: extract the logic into a service

    Logger.info(msg = s"getOAuthCodeZapmail called - code: $code :: state: $state")

    val sOpt = state.flatMap(s => _deconstructGAuthState(state = s))

    sOpt match {

      case None =>

        Logger.fatal(
          msg = s"OAuthFetchTokens error: state param missing. codeOpt: $codeOpt :: sOpt: $sOpt"
        )

        Future.successful(
          Res.BadRequestError("We need the state param from service provider. That is missing. Sorry about this. Please contact us")
        )

      case Some(authState) =>

        val serviceProvider = authState.serviceProvider

        val accountId = authState.id

        if (serviceProvider != EmailServiceProvider.MICROSOFT_365_API.toString) {

          Logger.fatal(
            msg = s"OAuthFetchTokens error: invalid service provider. serviceProvider: $serviceProvider :: accountId: $accountId :: codeOpt: $codeOpt"
          )

          Future.successful(Res.BadRequestError("Invalid service provider"))

        } else {

          codeOpt match {

            case None =>

              Logger.fatal(s"OAuthFetchTokens error: code is empty. state: $state :: code: $codeOpt :: accountId: $accountId")

              Future.successful(Res.BadRequestError("Invalid authorization code"))

            case Some(code) =>

              AccountDB.find(
                id = accountId
              ) match {

                case None =>

                  Logger.fatal(s"OAuthFetchTokens error: account not found. state: $state :: code: $code")

                  Future.successful(Res.BadRequestError("Account not found"))

                case Some(account) =>

                  val failedAuthorizationErrorMsg = s"Failed $serviceProvider Authorization. Please retry or contact support."
                  val failedLogErrorPartial = s"${serviceProvider}AuthFetchTokens [FATAL]: Failed: ${account.id} :: $state :: $code"

                  __outlookAuthFetchTokens(
                    code = code,
                    account = account,
                    failedLogErrorPartial = failedLogErrorPartial,
                    failedAuthorizationErrorMsg = failedAuthorizationErrorMsg,
                    isZapmailFlow = true,
                    Logger = Logger
                  ).flatMap { case (parentEmailSettingForSaving, _) =>

                    EmailAccountService.validateAndConnectMicrosoftOAuthSettings(
                      parentEmailSettingForSaving = parentEmailSettingForSaving,
                      account = account,
                    ).map {

                      case Left(errMsg) =>

                        Logger.error(
                          msg = s"getOAuthCodeZapmail - validateAndConnectMicrosoftOAuthSettings failed. accountId: ${account.id} :: accountEmail: ${account.email} :: failedToConnectEmail: ${parentEmailSettingForSaving.email} - errMsg: $errMsg",
                        )

                        Res.BadRequestError(
                          message = errMsg,
                        )

                      case Right(emailSettingId) =>

                        Res.Success(
                          message = "Email account connected to WarmupHero",
                          data = Json.obj("email_setting_id" -> emailSettingId.emailSettingId),
                        )

                    }

                  }.recover { case e =>

                    Logger.fatal(s"${serviceProvider}AuthFetchTokens error: emailSettingsForSaving, ${account.id}", err = e)

                    Res.ServerError(message = "Failed to connect email account to WarmupHero", e = None)

                  }

              }

          }

        }
    }

  }

}
