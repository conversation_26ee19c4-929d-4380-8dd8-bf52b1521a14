package api.controllers

import java.time.{LocalDate, LocalDateTime, LocalTime, ZoneOffset}
import api.middleware.{AuthUtils, EmailAccountUtils, LoggingAction}
import api.models.EmailAccountStats
import api.services.{CacheService, EmailAccountStatsService}
import play.api.libs.ws.WSClient
import play.api.mvc.{BaseController, ControllerComponents}

import java.time.temporal.ChronoUnit
import api.models.EmailAccountStats.getEmailAccountStats
import play.api.libs.json.Json

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class ReportController(
  val cacheService: CacheService,
  implicit val loggingAction: LoggingAction,
  implicit val controllerComponents: ControllerComponents,
  implicit val executionContext: ExecutionContext,
  implicit val wsClient: WSClient
) extends BaseController with AuthUtils with EmailAccountUtils {


  def getEmailAccountOverallStats(id: Long) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async { implicit request =>

    Future {

      val Res = request.Response

      val accountId = request.account.id

      val overallStats = getEmailAccountStats(emailAccountId = id, accountId = accountId, from = None, till = None)

      Res.Success(
        "Email Account stats found",
        Json.obj(
          "overall_stats" -> overallStats
        )
      )

    }
  }


  def getEmailAccountTimewiseStats(id: Long) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async { implicit request =>

    Future {

      val Res = request.Response

      val accountId = request.account.id

      val from = LocalDateTime.now().minusDays(14).toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
      val till = LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli() / 1000

      val timewiseStats  = EmailAccountStats.getEmailAccountStats(emailAccountId = id,  accountId = accountId, from = Some(from), till = Some(till))

      Res.Success(
        "Email Account stats found",
        Json.obj(
          "timewise_stats" -> timewiseStats
        )
      )

    }
  }


  /*
  def getEmailAccountDailyStatsForPrimary(id: Long) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async { implicit request =>

    Future {

      val Res = request.Response
      val accountId = request.account.id
      val emailAccount = request.emailAccount

      /*
      * NOTE: if email account created_at is greater than 14 days than fetching last 14 days stats
      * or else fetching 14 days stats from created_at
      * */
      val daysBetween = ChronoUnit.DAYS.between(emailAccount.created_at.toLocalDate, LocalDate.now())
      var from = LocalDate.now().minusDays(13).atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
      var till = LocalDate.now().atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
      if(daysBetween < 14) {
        from = emailAccount.created_at.toLocalDate.atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
        till = emailAccount.created_at.plusDays(13).toLocalDate.atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
      }

      val dailyStats = EmailAccountStats.getEmailAccountStatsDaily(
        emailAccountId = id,
        accountId = accountId,
        from = from,
        till = till,
        isReply = false
      )

      Res.Success(
        "Email Account stats found",
        Json.obj(
          "daily_stats" -> dailyStats
        )
      )

    }
  }

  def getEmailAccountDailyStatsForReplies(id: Long) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async { implicit request =>

    Future {

      val Res = request.Response
      val accountId = request.account.id
      val emailAccount = request.emailAccount

      /*
      * NOTE: if email account created_at is greater than 14 days than fetching last 14 days stats
      * or else fetching 14 days stats from created_at
      * */
      val daysBetween = ChronoUnit.DAYS.between(emailAccount.created_at.toLocalDate, LocalDate.now())
      var from = LocalDateTime.now().minusDays(14).toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
      var till = LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
      if(daysBetween < 14) {
        from = emailAccount.created_at.toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
        till = emailAccount.created_at.plusDays(14).toInstant(ZoneOffset.UTC).toEpochMilli() / 1000
      }

      val dailyStats = EmailAccountStats.getEmailAccountStatsDaily(
        emailAccountId = id,
        accountId = accountId,
        from = from,
        till = till,
        isReply = true
      )

      Res.Success(
        "Email Account stats found",
        Json.obj(
          "daily_stats" -> dailyStats
        )
      )

    }

  }
   */

  def getEmailAccountDailyStats(id: Long) = (isLoggedIn() andThen hasEmailAccount(emailAccountId = id)).async { implicit request =>

    Future {

      val Res = request.Response
      val accountId = request.account.id
      val emailAccount = request.emailAccount

      val Logger = request.Logger

      EmailAccountStatsService.getEmailAccountDailyStats(
        emailAccount = emailAccount,
        accountId = accountId,
      ) match {

        case Failure(exception) =>

          Logger.error(
            msg = s"Failed to fetch email account stats. emailAccountId: $id :: accountId: $accountId",
            err = exception,
          )

          Res.ServerError(message = "Failed to fetch email account stats", e = None)

        case Success(dailyStats) =>

          Res.Success(
            message = "Email Account stats found",
            data = Json.obj(
              "daily_stats" -> dailyStats
            )
          )

      }

    }

  }

}
