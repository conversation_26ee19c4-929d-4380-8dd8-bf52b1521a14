package api.controllers
import play.api.mvc.{Action, AnyContent, BaseController, ControllerComponents}

class IndexController(
                       implicit val controllerComponents: ControllerComponents,
                     ) extends BaseController {

  def returnIndexHtml: Action[AnyContent] = Action { request =>


    val redirectToHtmlPage =
      s"""<!DOCTYPE html>
        <html lang="en">

          <head>
            <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
                <meta http-equiv="x-ua-compatible" content="ie=edge">

                </head>

                <body>
                  <div>
                    Hello world!
                  </div>
                </body>

              </html>"""

    Ok(redirectToHtmlPage).as(HTML)

  }
}
