package api.services

import api.AppConfig
import api.controllers.FindOrCreateAccountForm
import api.dao.AccessTokenCacheDAO
import api.models.{Account, AccountDB, WarmupHeroAccountMetadata}
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

object AccountService {

  // 4 Feb 2025 - for internal usage only
  def fetchAndUpdateOrgIdForAllAccounts(
    implicit Logger: SRLogger,
    ec: ExecutionContext,
    ws: WSClient,
  ): Future[List[Boolean]] = {

    for {

      // Check the total number of accounts with API key
      // Problem - we get those customer who have left WH.
      allAccounts: List[Account] <- Future.fromTry {
        AccountDB.findAllWithApiKey
      }

      _ = Logger.debug(msg = s"fetchAndUpdateOrgIdForAllAccounts - size: ${allAccounts.size}")

      accountsUpdateWithOrgIdFut: List[Future[Boolean]] = allAccounts.map { acc =>

        // findAllWithApiKey - sr_api_key is not null in where clause
        val apiKey = acc.sr_api_key.get

        AccountDB.getCreditsBySRApiKey(
          sr_api_key = apiKey,
          Logger = Logger
        ).flatMap { c =>

          Future.fromTry {

            // Only the org_id is from SmartReach.
            // All other field are from the existing Account obj, so they don't change.
            AccountDB.updateSRSettings(
              accountId = acc.id,
              total_allowed_warmup_emails = acc.total_allowed_warmup_emails,
              apiKey = apiKey,
              orgId = c.get.org_id,
            ).map { _ =>

              Logger.info(
                msg = s"Successfully to update data - accountId: ${acc.id} :: accountEmail: ${acc.email} :: orgId: ${c.get.org_id}",
              )

              true

            }

          }

        }.recoverWith { case e =>

          Logger.error(
            msg = s"Failed to update data - accountId: ${acc.id} :: accountEmail: ${acc.email}",
            err = e,
          )

          Future.successful(false)

        }

      }

      accountsUpdateWithOrgId: List[Boolean] <- Future.sequence(accountsUpdateWithOrgIdFut)

    } yield {

      accountsUpdateWithOrgId

    }

  }


  def getAccountMetadata(
    sr_api_key: String,
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    logger: SRLogger,
  ): Future[WarmupHeroAccountMetadata] = {

    AccountDB.getAccountMetadata(
      sr_api_key = sr_api_key,
    )

  }

  def getAccountMetadataByAccountId(
    accountId: Long,
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    logger: SRLogger,
  ): Future[WarmupHeroAccountMetadata] = {

    AccountDB.find(
      id = accountId
    ).flatMap(_.sr_api_key) match {

      case None =>

        logger.error(
          msg = s"getAccountMetadataByAccountId - failed to find API key. accountId: $accountId"
        )

        Future.failed(new Exception("Not found account api key"))

      case Some(srApiKey) =>

        AccountDB.getAccountMetadata(
          sr_api_key = srApiKey,
        )

    }

  }

  private def findOrCreateAccount(
    findOrCreateAccountForm: FindOrCreateAccountForm,
  )(
    implicit Logger: SRLogger,
  ): Try[Either[String, Account]] = {

    for {

      accountByOrgIdOpt: Either[String, Option[Account]] <- AccountDB.findByOrgId(
        orgId = findOrCreateAccountForm.org_id,
      ) match {

        case Failure(exception) =>

          Logger.shouldNeverHappen(
            msg = s"findOrCreateAccount - findByOrgId failed. orgId: ${findOrCreateAccountForm.org_id}",
            err = Some(exception),
          )

          Failure(exception)

        case Success(accounts) =>

          if (accounts.length < 1) {

            // No account found with provided `org_id`

            Success(Right(None))

          } else if (accounts.length > 1) {

            // Multiple accounts linked with the same `org_id`
            // This happens when 1 API key is used in multiple WH accounts.
            // Cannot auto login such users.

            Logger.error(
              msg = s"Multiple accounts found with the same orgId - orgId: ${findOrCreateAccountForm.org_id} :: accountIds: ${accounts.map(_.id)} :: accountEmails: ${accounts.map(_.email)}"
            )

            Success(Left("Cannot auto login, multiple accounts found with the same org_id"))

          } else {

            val account = accounts.head

            if (account.sr_api_key.contains(findOrCreateAccountForm.wh_api_key)) {

              // API key is same - no need to update

              Success(Right(Some(account)))

            } else {

              Logger.debug(
                msg = s"WarmupHero auto login - API keys don't match - updating. accountId: ${account.id} :: accountEmail: ${account.email} :: orgId: ${findOrCreateAccountForm.org_id}",
              )

              AccountDB.updateSRSettings(
                accountId = account.id,
                total_allowed_warmup_emails = account.total_allowed_warmup_emails,
                apiKey = findOrCreateAccountForm.wh_api_key,
                orgId = findOrCreateAccountForm.org_id,
              ).flatMap {

                case None =>

                  Logger.shouldNeverHappen(
                    msg = s"Failed to update account with api key. accountId: ${account.id} :: accountEmail: ${account.email} :: orgId: ${findOrCreateAccountForm.org_id}",
                    err = None,
                  )

                  Failure(new Exception("Failed to update account with api key"))

                case Some(account) =>

                  Success(Right(Some(account)))

              }

            }

          }

      }

      accountByApiKeyOpt: Either[String, Option[Account]] <- accountByOrgIdOpt match {

        case Left(errMsg) =>

          Success(Left(errMsg))

        case Right(Some(accountFromOrgId)) =>

          Success(Right(Some(accountFromOrgId)))

        case Right(None) =>

          AccountDB.findAllBySrApiKey(
            srApiKey = findOrCreateAccountForm.wh_api_key,
          ) match {

            case Failure(exception) =>

              Failure(exception)

            case Success(accountsFromApiKey) =>

              if (accountsFromApiKey.length < 1) {

                // No account found with provided API key

                Success(Right(None))

              } else if (accountsFromApiKey.length > 1) {

                // Multiple accounts linked with the same API key
                // Cannot auto login such users.

                Logger.error(
                  msg = s"Multiple accounts found with the same API key - orgId: ${findOrCreateAccountForm.org_id} :: accountIds: ${accountsFromApiKey.map(_.id)} :: accountEmails: ${accountsFromApiKey.map(_.email)}"
                )

                Success(Left("Cannot auto login, multiple accounts found with same apikey"))

              } else {

                // If only 1 account is associated with the provided API key then use it.
                val acc = accountsFromApiKey.head

                // If the account was found using the API key,
                // update the org_id for that account.
                AccountDB.updateAccountOrgId(
                  accountId = acc.id,
                  orgId = findOrCreateAccountForm.org_id,
                ) match {

                  case Failure(exception) =>

                    Logger.shouldNeverHappen(
                      msg = s"findOrCreateAccount - updateAccountOrgId failed. accountId: ${acc.id}, orgId: ${findOrCreateAccountForm.org_id}",
                      err = Some(exception),
                    )

                    Failure(exception)

                  case Success(_) =>

                    Success(Right(Some(acc)))

                }

              }

          }

      }

      accountByOwnerEmailOpt: Either[String, Option[Account]] <- accountByApiKeyOpt match {

        case Left(errMsg) =>

          Success(Left(errMsg))

        case Right(Some(existingAccount)) =>

          // If we find an existing account by querying against API key or orgId, then return it.
          // No need to try and find it using the owner email.

          Success(Right(Some(existingAccount)))

        case Right(None) =>

          // We didn't find an existing account by querying API key or orgId in the previous steps,
          // try and find the account using the SmartReach owner email.

          // This issue occurs when a user first manually signs up on WarmupHero
          // using the same email as their SmartReach account,
          // and then tries to auto login into WarmupHero from SmartReach.
          //
          // The auto-login fails because we can't find the account using the API key and org id.
          // However, when we attempt to create a new account,
          // the process fails due to a unique constraint on the email field in the accounts table.
          //
          // To resolve this, we will now also search for the account using the SmartReach owner email
          // before attempting to create a new one.

          Try {
            AccountDB.findByEmail(
              email = findOrCreateAccountForm.org_owner_email.trim.toLowerCase,
            )
          } match {

            case Failure(exception) =>

              Logger.shouldNeverHappen(
                msg = s"findOrCreateAccount - findByEmail failed. email: ${findOrCreateAccountForm.org_owner_email}",
                err = Some(exception),
              )

              Failure(exception)

            case Success(None) =>

              // No account found with provided owner email

              Success(Right(None))

            case Success(Some(accountFromEmail)) =>

              // If the account was found using the owner email,
              // update the org_id and API key for that account.
              AccountDB.updateSRSettings(
                accountId = accountFromEmail.id,
                total_allowed_warmup_emails = accountFromEmail.total_allowed_warmup_emails,
                apiKey = findOrCreateAccountForm.wh_api_key,
                orgId = findOrCreateAccountForm.org_id,
              ) match {

                case Failure(exception) =>

                  Logger.shouldNeverHappen(
                    msg = s"findOrCreateAccount - updateAccountOrgIdAndAPIKey failed. accountId: ${accountFromEmail.id}, orgId: ${findOrCreateAccountForm.org_id} :: ownerEmail: ${findOrCreateAccountForm.org_owner_email}",
                    err = Some(exception),
                  )

                  Failure(exception)

                case Success(_) =>

                  Success(Right(Some(accountFromEmail)))

              }

          }

      }

      account: Either[String, Account] <- accountByOwnerEmailOpt match {

        case Left(errMsg) =>

          Success(Left(errMsg))

        case Right(Some(existingAccount)) =>

          Success(Right(existingAccount))

        case Right(None) =>

          Logger.info(
            msg = s"No account found with org id, owner email and API key, creating new account. orgId: ${findOrCreateAccountForm.org_id} :: ownerEmail: ${findOrCreateAccountForm.org_owner_email}",
          )

          AccountDB.autoCreateAccount(
            first_name = findOrCreateAccountForm.org_owner_first_name,
            last_name = findOrCreateAccountForm.org_owner_last_name,
            email = findOrCreateAccountForm.org_owner_email,
            api_key = findOrCreateAccountForm.wh_api_key,
            org_id = findOrCreateAccountForm.org_id,
          ) match {

            case Failure(exception) =>

              Logger.shouldNeverHappen(
                msg = s"findOrCreateAccount - failed create account. orgId: ${findOrCreateAccountForm.org_id} :: email: ${findOrCreateAccountForm.org_owner_email}",
                err = Some(exception),
              )

              Failure(exception)

            case Success(None) =>

              Logger.shouldNeverHappen(
                msg = s"findOrCreateAccount - failed to create account - None. orgId: ${findOrCreateAccountForm.org_id} :: email: ${findOrCreateAccountForm.org_owner_email}",
              )

              Failure(new Exception("Failed to create account"))

            case Success(Some(account)) =>

              Success(Right(account))

          }

      }

    } yield {

      account

    }

  }

  def createLoginRedirectUrl(
    findOrCreateAccountForm: FindOrCreateAccountForm,
    cacheService: CacheService,
  )(
    implicit Logger: SRLogger,
    ec: ExecutionContext,
  ): Future[Either[String, String]] = {

    val accessToken = AccessTokenCacheDAO.generateNewToken(
      orgId = findOrCreateAccountForm.org_id,
    )

    for {

      eitherErrMsgOrAcc: Either[String, Account] <- Future.fromTry {

        findOrCreateAccount(
          findOrCreateAccountForm = findOrCreateAccountForm,
        )

      }

      eitherErrMsgOrRedirectUrl: Either[String, String] <- eitherErrMsgOrAcc match {

        case Left(errMsg) =>

          Future.successful(Left(errMsg))

        case Right(_) =>

          AccessTokenCacheDAO.saveNewToken(
            token = accessToken,
            orgId = findOrCreateAccountForm.org_id,
            cacheService = cacheService,
          ).map { _ =>

            Right(
              s"${AppConfig.productionDashboardDomain}/auth/access_check?token=$accessToken&org_id=${findOrCreateAccountForm.org_id}"
            )

          }.recoverWith { case e =>

            Logger.shouldNeverHappen(
              msg = s"createLoginRedirectUrl - failed to save new token in cache. orgId: ${findOrCreateAccountForm.org_id}",
              err = Some(e),
            )

            Future.failed(e)

          }

      }

    } yield {

      eitherErrMsgOrRedirectUrl

    }

  }

  def checkTokenAndReturnAccount(
    orgId: Long,
    token: String,
    cacheService: CacheService,
  )(
    implicit Logger: SRLogger,
    ec: ExecutionContext,
    ws: WSClient,
  ): Future[Account] = {

    for {

      orgIdFromCache: Long <- AccessTokenCacheDAO.getOrgIdFromToken(
        token = token,
        cacheService = cacheService,
      )

      _: Boolean <- if (orgId != orgIdFromCache) {

        Future.failed(new Exception("Invalid token [1]"))

      } else {

        Future.successful(true)

      }

      _: Boolean <- AccessTokenCacheDAO.deleteToken(
        token = token,
        cacheService = cacheService,
      )


      account: Account <- Future.fromTry {

        AccountDB.findByOrgId(
          orgId = orgId,
        ) match {

          case Failure(exception) =>

            Logger.shouldNeverHappen(
              msg = s"checkTokenAndReturnAccount - findByOrgId failed. orgId: $orgId",
              err = Some(exception),
            )

            Failure(exception)

          case Success(accounts) =>

            if (accounts.length != 1) {

              Logger.shouldNeverHappen(
                msg = s"checkTokenAndReturnAccount - accounts.length != 1. orgId: $orgId :: accountIds: ${accounts.map(_.id)} :: accountEmails: ${accounts.map(_.email)}",
              )

              Failure(new Exception("Account not found"))

            } else {

              Success(accounts.head)

            }

        }

      }

      // reset credits on auto login
      // we also do this for normal login
      accountResetCredits: Account <- AccountDB.resetSRCredits(
        accountId = account.id,
        sr_api_key = account.sr_api_key.get,
        total_active_warmup_emails = account.total_active_warmup_emails,
        Logger = Logger
      ).map { _ =>

        account

      }

      // Reset team cache after successfully validating the access token
      _ = cacheService._resetTeamCache(aid = accountResetCredits.id)

    } yield {

      accountResetCredits

    }

  }

}
