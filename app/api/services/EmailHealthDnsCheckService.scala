package api.services

import play.api.libs.json._

import java.io.{ByteArrayOutputStream, PrintWriter}
import scala.concurrent.{ExecutionContext, Future}
import scala.sys.process._
import scala.util.Try
import scala.util.matching.Regex


// TODO:
//  All the models and objects in the file should come from the common email service providers package.

case class EmailHealthCheckRecordId(id: Long) extends AnyVal {

  override def toString: String = id.toString

}

object EmailHealthCheckRecordId {

  implicit val reads: Reads[EmailHealthCheckRecordId] = new Reads[EmailHealthCheckRecordId] {

    override def reads(ev: JsValue): JsResult[EmailHealthCheckRecordId] = {

      ev match {

        case JsNumber(id) => JsSuccess(EmailHealthCheckRecordId(id = id.toLong))

        case randomValue => JsError(s"expected number, got some random value - $randomValue")

      }

    }

  }

  implicit val writes: Writes[EmailHealthCheckRecordId] = new Writes[EmailHealthCheckRecordId] {

    override def writes(o: EmailHealthCheckRecordId): JsValue = JsNumber(o.id)

  }

}

sealed trait EmailAuthStatus

object EmailAuthStatus {

  private val pass = "pass"
  private val none = "none"
  private val fail = "fail"


  case object Pass extends EmailAuthStatus {

    override def toString: String = pass

  }

  case object None extends EmailAuthStatus {

    override def toString: String = none

  }

  case class Fail(status: String) extends EmailAuthStatus {

    override def toString: String = status

  }

  def fromKey(key: String): EmailAuthStatus = {

    key match {

      case `pass` => Pass

      case `none` => None

      case `fail` => Fail(status = fail)

      case s: String => Fail(status = s)

    }

  }

  implicit def writes: Writes[EmailAuthStatus] = (s: EmailAuthStatus) => s match {

    case Pass
         | None =>

      JsString(s.toString)

    case Fail(_) =>

      // As far as the frontend is concerned we can just say fail ??
      JsString(fail)

  }

}

case class EmailAuthStatusDetails(
  spf_auth_status: EmailAuthStatus,
  dkim_auth_status: EmailAuthStatus,
  dmarc_auth_status: EmailAuthStatus,
)

case class EmailHealthCheckDetails(
  spf_record: Option[String],
  dkim_record: Option[String],
  dmarc_record: Option[String],
)


case class CommandExecutionResult(exitValue: Int, output: String, error: String)

object CommandExecutor {

  def runCommandInBash(cmd: String)(implicit ec: ExecutionContext): Future[CommandExecutionResult] = Future {
    val stdoutStream = new ByteArrayOutputStream
    val stderrStream = new ByteArrayOutputStream
    val stdoutWriter = new PrintWriter(stdoutStream)
    val stderrWriter = new PrintWriter(stderrStream)

    val exitValue = Seq("bash", "-c", cmd).!(ProcessLogger(stdoutWriter.println, stderrWriter.println))

    stdoutWriter.close()
    stderrWriter.close()

    CommandExecutionResult(exitValue, stdoutStream.toString, stderrStream.toString)
  }

}

object EmailHealthDnsCheckService {


  def getDkimRecord(
    domain: String,
    selector: String
  )(
    implicit ec: ExecutionContext
  ): Future[Option[String]] = {

    val command =
      s"""
         |dig +short $selector._domainkey.$domain TXT
         |""".stripMargin

    CommandExecutor.runCommandInBash(
      cmd = command
    ).flatMap { res =>

      if (res.error == "" && res.exitValue == 0) {

        // For now just check if the value exists, will add validation for DKIM record.
        val dkimRecordOpt = if (res.output == "") None else Some(res.output)

        Future.successful(dkimRecordOpt)

      } else {

        Future.failed(new Exception(s"${res.error}. ${res.exitValue}"))

      }

    }

  }

  def getDmarcRecord(
    domain: String
  )(
    implicit ec: ExecutionContext
  ): Future[Option[String]] = {

    val command =
      s"""
         |dig +short _dmarc.$domain TXT
         |""".stripMargin

    CommandExecutor.runCommandInBash(
      cmd = command
    ).flatMap { res =>

      if (res.error == "" && res.exitValue == 0) {

        // For now just check if the value exists, will add validation for Dmarc record.
        val dmarcRecordOpt = if (res.output == "") None else Some(res.output)

        Future.successful(dmarcRecordOpt)

      } else {

        Future.failed(new Exception(s"${res.error}. ${res.exitValue}"))

      }

    }

  }

  def getSpfRecord(
    domain: String
  )(
    implicit ec: ExecutionContext
  ): Future[Option[String]] = {

    val command =
      s"""
         |dig +short $domain TXT
         |""".stripMargin

    CommandExecutor.runCommandInBash(
      cmd = command
    ).flatMap { res =>

      if (res.error == "" && res.exitValue == 0) {

        // For now just check if the value exists, will add validation for SPF record.
        val spfRecordOpt = if (res.output == "") {
          None
        } else {

          // `dig +short $domain TXT` was giving multiple TXT records,
          // getting the one which has `v=spf`.
          res.output.split("\n").find(_.contains("v=spf"))

        }

        Future.successful(spfRecordOpt)

      } else {

        Future.failed(new Exception(s"${res.error}. ${res.exitValue}"))

      }

    }

  }

  private def extractSpfAuthStatus(authResultsOpt: Option[String]) = {

    val spfStatusPattern: Regex = "\\bspf=([a-zA-Z]+)".r

    val spfRes: Option[Regex.Match] = authResultsOpt.flatMap { s =>

      spfStatusPattern.findFirstMatchIn(
        source = s
      )

    }

    spfRes
      .map(s => EmailAuthStatus.fromKey(s.toString.drop(4))) // remove `spf=` from s
      .getOrElse(EmailAuthStatus.None)

  }

  private def extractDkimAuthStatus(authResultsOpt: Option[String]) = {

    val dkimStatusPattern: Regex = "\\bdkim=([a-zA-Z]+)".r

    val dkimRes: Option[Regex.Match] = authResultsOpt.flatMap { s =>

      dkimStatusPattern.findFirstMatchIn(
        source = s
      )

    }

    dkimRes
      .map(s => EmailAuthStatus.fromKey(s.toString.drop(5))) // remove `dkim=` from s
      .getOrElse(EmailAuthStatus.None)

  }

  private def extractDmarcAuthStatus(authResultsOpt: Option[String]) = {

    val dmarcStatusPattern: Regex = "\\bdmarc=([a-zA-Z]+)".r

    val dmarcRes: Option[Regex.Match] = authResultsOpt.flatMap { s =>

      dmarcStatusPattern.findFirstMatchIn(
        source = s
      )

    }

    dmarcRes
      .map(s => EmailAuthStatus.fromKey(s.toString.drop(6))) // remove `dmarc=` from s
      .getOrElse(EmailAuthStatus.None)

  }

  def extractEmailAuthStatusDetails(
    emailMessageFullHeaders: JsValue,
  ): EmailAuthStatusDetails = {

    val authResultsOpt: Option[String] = Try {
      (emailMessageFullHeaders \ "Authentication-Results").as[String]
    }.toOption


    val spfStatus: EmailAuthStatus = extractSpfAuthStatus(
      authResultsOpt = authResultsOpt,
    )

    val dkimStatus: EmailAuthStatus = extractDkimAuthStatus(
      authResultsOpt = authResultsOpt,
    )


    val dmarcStatus: EmailAuthStatus = extractDmarcAuthStatus(
      authResultsOpt = authResultsOpt,
    )

    EmailAuthStatusDetails(
      spf_auth_status = spfStatus,
      dkim_auth_status = dkimStatus,
      dmarc_auth_status = dmarcStatus,
    )

  }

  def extractDkimSelectorFromSignature(
    emailMessageFullHeaders: JsValue,
  ): Option[String] = {

    // Note there can be space after s=
    // e.g. s=  default

    val dkimSelectorPattern: Regex = """\bs=\s*([a-zA-Z0-9_-]+)""".r

    val dkimSignatureOpt: Option[String] = Try {
      (emailMessageFullHeaders \ "DKIM-Signature").as[String]
    }.toOption

    val dkimSignatureSelectorMatchOpt: Option[Regex.Match] = dkimSignatureOpt.flatMap { s =>

      dkimSelectorPattern.findFirstMatchIn(
        source = s
      )

    }

    // ex. "s=  google".drop(2) => "  google" [remove the `s=` part]
    //                 .trim    => "google"   [remove the whitespace around the selector value]
    dkimSignatureSelectorMatchOpt.map(_.toString.drop(2).trim)

  }

}
