package api.services

import api.models.{EmailAccount, EmailAccountStats, EmailAccountStatsDaily}

import java.time.temporal.ChronoUnit
import java.time.{LocalDate, LocalDateTime, LocalTime, ZoneOffset}

import scala.util.Try

case class FromTill(
  from: Long,
  till: Long,
)

object EmailAccountStatsService {

  def getStatsTimeRangeFromCreatedAt(
    emailAccountCreatedAt: LocalDateTime
  ): FromTill = {

    /*
    * NOTE: if email account created_at is greater than 14 days than fetching last 14 days stats
    * or else fetching 14 days stats from created_at
    * */
    val daysBetween = ChronoUnit.DAYS.between(emailAccountCreatedAt.toLocalDate, LocalDate.now())

    if (daysBetween < 14) {

      FromTill(
        from = emailAccountCreatedAt.toLocalDate.atTime(LocalTime.MIDNIGHT).toInstant(ZoneOffset.UTC).toEpochMilli / 1000,
        till = emailAccountCreatedAt.toLocalDate.atTime(LocalTime.MAX).plusDays(14).toInstant(ZoneOffset.UTC).toEpochMilli / 1000,
      )

    } else {

      FromTill(
        from = LocalDate.now().atTime(LocalTime.MIDNIGHT).minusDays(14).toInstant(ZoneOffset.UTC).toEpochMilli / 1000,
        till = LocalDate.now().atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli / 1000,
      )

    }

  }

  def getEmailAccountDailyStats(
    accountId: Long,
    emailAccount: EmailAccount,
  ): Try[List[EmailAccountStatsDaily]] = {

    val fromTill = EmailAccountStatsService.getStatsTimeRangeFromCreatedAt(
      emailAccountCreatedAt = emailAccount.created_at,
    )

    EmailAccountStats.getEmailAccountStatsDaily(
      emailAccountId = emailAccount.id,
      accountId = accountId,
      from = fromTill.from,
      till = fromTill.till,
    )

  }

}
