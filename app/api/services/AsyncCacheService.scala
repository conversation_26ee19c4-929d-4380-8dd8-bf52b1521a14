package api.services

import api.AppConfig
import api.models.Account
import play.api.Logging
import play.api.cache.AsyncCacheApi
import play.api.cache.redis.CacheAsyncApi
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json, OFormat}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import utils.Helpers

import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.duration._

class CacheService(cacheApi: CacheAsyncApi, implicit val ec: ExecutionContext) extends Logging{

  val keyPrefix = "prext"

  implicit val accountFormat: OFormat[Account] = Json.format[Account]

  def sessionAccountKey(accountId: Long) = s"$keyPrefix::accountId::$accountId"

  def apiAccountKey(apiKey: String) = s"$keyPrefix::apiKey::$apiKey"


  // browser logged in session account

  def setSessionAccount(account: Account): Future[Any] = {

    Future {
      Json.toJson(account)
    }.flatMap(js => {
      cacheApi.set(sessionAccountKey(account.id), js, 3600.seconds)
    })
      .recover { case e =>
        logger.error(s"CacheService.setSessionAccount FATAL: ${Helpers.getStackTraceAsString(e)} :: $account")
      }
  }


  def getSessionAccount(accountId: Long): Future[Option[Account]] = {

    Future {
      cacheApi.get[JsValue](sessionAccountKey(accountId))
    }
      .flatten
      .flatMap {
        case None => Future.successful(None)

        case Some(a) =>

          a.validate[Account] match {

            case JsError(e) =>

              logger.error(s"CacheService.getSessionAccount FATAL ($accountId) error while fetching from redis, will drop this value: $e")

              removeSessionAccount(accountId = accountId)
                .map(_ => None)

            case JsSuccess(data, _) =>

              Future.successful(Some(data))

          }
      }
      .recover { case e =>

        logger.error(s"CacheService.getSessionAccount FATAL $accountId error while fetching from redis: ${Helpers.getStackTraceAsString(e)}")

        None
      }
  }

  def removeSessionAccount(accountId: Long): Future[Any] = {
    cacheApi.remove(sessionAccountKey(accountId))
      .recover { case e =>
        logger.error(s"CacheService.removeSessionAccount FATAL $accountId: ${Helpers.getStackTraceAsString(e)}")
      }
  }

  final def _resetTeamCache(aid: Long) = {
    removeSessionAccount(accountId = aid)

    logger.info(s"[AuthUtils] _resetTeamCache run for $aid")

  }


  def setJsValue(key: String, data: JsValue, expirySeconds: Long = 3600): Future[Any] = {
    Future {
      Json.toJson(data)
    }.flatMap(json =>
      cacheApi.set(key, json, expirySeconds.seconds)
    )
      .recover { case e =>
        logger.error(s"CacheService.setJsValue FATAL key: $key :: ${Helpers.getStackTraceAsString(e)} :: $data")
      }
  }


  def getJsValue(key: String): Future[Option[JsValue]] = {

    cacheApi.get[JsValue](key)
      .recover { case e =>
        logger.error(s"CacheService.getJsValue FATAL $key: ${Helpers.getStackTraceAsString(e)}")
        None
      }

  }

  def removeJsValue(key: String): Future[Any] = {
    cacheApi.remove(key)
      .recover { case e =>
        logger.error(s"CacheService.removeJsValue FATAL $key: ${Helpers.getStackTraceAsString(e)}")
      }
  }


  /// START: get / set normal string values ///


  def setStr(key: String, value: String, expirySeconds: Long = 3600): Future[Any] = {
    cacheApi.set(key, value, expirySeconds.seconds)
      .recover { case e =>
        logger.error(s"CacheService.setStr FATAL key: $key :: $value :: ${Helpers.getStackTraceAsString(e)}")
      }
  }


  def getStr(key: String): Future[Option[String]] = {

    cacheApi.get[String](key)
      .recover { case e =>
        logger.error(s"CacheService.getStr FATAL $key: ${Helpers.getStackTraceAsString(e)}")
        None
      }
  }

  def removeStr(key: String) = {
    cacheApi.remove(key)
      .recover { case e =>
        logger.error(s"CacheService.removeStr FATAL $key: ${Helpers.getStackTraceAsString(e)}")
      }

  }

  /// END: get / set normal string values ///


}



