package api.services

import akka.actor.ActorSystem
import api.AppConfig
import api.dao.EmailHealthCheckDAO
import api.models._
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import utils.email._
import utils.{EmailValidator, SRLogger}

import javax.mail.Message
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.EnumerationHasAsScala
import scala.util.{Failure, Success}

object EmailHealthCheckService {

  private def fetchHealthCheckMessages(
    emailAccountForHealthCheck: EmailAccount,
  )(
    implicit executionContext: ExecutionContext,
    wSClient: WSClient,
    Logger: SRLogger,

  ): Future[Option[Message]] = {

    val receiverEmailAccountId = AppConfig.EmailHealthCheck.getReceiverEmailSettingIdForEmailHealthCheck

    for {

      receiverEmailAccount: EmailAccount <- Future.fromTry {

        EmailAccount.find(id = receiverEmailAccountId)

      }

      emailService: TEmailService <- receiverEmailAccount.service_provider match {

        case EmailServiceProvider.MICROSOFT_365_API =>

          // Outlook API does not give the full headers

          Logger.shouldNeverHappen(
            msg = s"Cannot get full headers with outlook api. receiverEmailAccountId: $receiverEmailAccountId",
            err = None,
          )

          Future.failed(new Exception("Cannot get full headers with outlook api."))

        case EmailServiceProvider.GOOGLE_WORKSPACE
             | EmailServiceProvider.MICROSOFT_365
             | EmailServiceProvider.OTHER =>

          val emailService: TEmailService =

            if (receiverEmailAccount.service_provider == EmailServiceProvider.GOOGLE_WORKSPACE) GmailService
            else if (receiverEmailAccount.service_provider == EmailServiceProvider.MICROSOFT_365) OutlookService
            else GenericEmailService

          Future.successful(emailService)

      }

      imapSettings: EmailSettings.ImapEmailAccount <- receiverEmailAccount.settings match {

        case _: EmailSettings.OAuthTokens =>

          Logger.shouldNeverHappen(
            msg = s"Cannot get full headers with outlook api - OAuthTokens. receiverEmailAccountId: $receiverEmailAccountId",
            err = None,
          )

          Future.failed(new Exception("OAuthTokens - Cannot get full headers with outlook api."))

        case es: EmailSettings.SmtpImapSettings =>

          Future.successful(es.imap_settings)

      }

      messages: Vector[Message] <- emailService.fetchRecentMessages(
        emailAfterDate = DateTime.now.minusMinutes(7),
        receiveCredentials = imapSettings,
        Logger = Logger,
        senderEmail = Some(emailAccountForHealthCheck.email)
      )

      messageOpt: Option[Message] = {

        val healthCheckMessageOpt = messages
          .filter(_.getSubject == AppConfig.EmailHealthCheck.emailHealthCheckSubject)
          .distinctBy { message =>

            val allHeaders: Map[String, String] = message
              .getAllHeaders
              .asScala
              .map(header => header.getName -> header.getValue)
              .toMap

            allHeaders.get("Message-ID")

          }
          .headOption

        Logger.info(s"Found healthCheckMessageOpt: ${healthCheckMessageOpt.map(_.getSubject)}")

        healthCheckMessageOpt

      }

    } yield {

      messageOpt

    }

  }

  private def emailSettingHealthCheckTrackInternal(
    emailAccountForHealthCheck: EmailAccount,
    emailHealthCheckRecordId: EmailHealthCheckRecordId,
  )(
    implicit executionContext: ExecutionContext,
    wSClient: WSClient,
    Logger: SRLogger,

  ): Future[EmailHealthCheckRecordId] = {

    for {

      emailHealthCheckMessage: Option[Message] <- fetchHealthCheckMessages(
        emailAccountForHealthCheck = emailAccountForHealthCheck,
      )

      message: Message <- emailHealthCheckMessage match {

        case None =>

          Logger.error(
            s"emailHealthCheckMessage not found - None. emailAccountId: ${emailAccountForHealthCheck.id} :: email: ${emailAccountForHealthCheck.email}",
          )

          Future.failed(new Exception("No email health check message found."))


        case Some(message) =>

          Future.successful(message)

      }

      allHeaders: Map[String, String] = message
        .getAllHeaders
        .asScala
        .map(header => header.getName -> header.getValue)
        .toMap


      emailAuthStatusDetails = EmailHealthDnsCheckService.extractEmailAuthStatusDetails(
        emailMessageFullHeaders = Json.toJson(allHeaders),
      )

      dkimSelectorOpt = EmailHealthDnsCheckService.extractDkimSelectorFromSignature(
        emailMessageFullHeaders = Json.toJson(allHeaders),
      )

      (_, domain: String) = EmailValidator.getLowercasedNameAndDomainFromEmail(
        email = emailAccountForHealthCheck.email,
      )

      spfRecordOpt <- EmailHealthDnsCheckService.getSpfRecord(
        domain = domain,
      )

      dkimRecordOpt <- dkimSelectorOpt match {

        case None =>

          Logger.error(
            s"dkimSelector not found. emailAccountId: ${emailAccountForHealthCheck.id} :: email: ${emailAccountForHealthCheck.email}",
          )

          Future.successful(None)

        case Some(selector) =>

          EmailHealthDnsCheckService.getDkimRecord(
            domain = domain,
            selector = selector,
          )

      }

      dmarcRecordOpt <- EmailHealthDnsCheckService.getDmarcRecord(
        domain = domain,
      )

      emailHealthCheckRecord: EmailHealthCheckRecordId <- Future.fromTry {

        EmailHealthCheckDAO.updateEmailHealthCheckResultOnSuccess(
          emailAuthStatusDetails = emailAuthStatusDetails,
          dkimSelectorOpt = dkimSelectorOpt,
          emailHealthCheckDetails = EmailHealthCheckDetails(
            spf_record = spfRecordOpt,
            dkim_record = dkimRecordOpt,
            dmarc_record = dmarcRecordOpt,
          ),
          accountId = emailAccountForHealthCheck.account_id,
          emailHealthCheckRecordId = emailHealthCheckRecordId,
        )

      }

    } yield {

      emailHealthCheckRecord

    }

  }

  def emailSettingHealthCheckTrack(
    emailHealthCheckRecordId: EmailHealthCheckRecordId,
  )(
    implicit executionContext: ExecutionContext,
    wSClient: WSClient,
    Logger: SRLogger,
  ): Future[EmailHealthCheckRecordId] = {

    for {

      emailHealthCheckRecord: EmailHealthCheckRecord <- EmailHealthCheckDAO.getEmailHealthCheckRecord(
        id = emailHealthCheckRecordId,
      ) match {

        case Failure(exception) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email health check record. emailHealthCheckRecordId: $emailHealthCheckRecordId",
            err = Some(exception),
          )

          Future.failed(exception)

        case Success(None) =>

          Logger.error(
            s"emailHealthCheckRecord not found - None. emailHealthCheckRecordId: $emailHealthCheckRecordId",
          )

          Future.failed(new Exception("Email health check record not found"))

        case Success(Some(emailHealthCheckRecord)) =>

          Future.successful(emailHealthCheckRecord)

      }

      emailAccountForHealthCheck: EmailAccount <- Future.fromTry {
        EmailAccount.find(id = emailHealthCheckRecord.email_account_id)
      }

      updatedEmailHealthCheckRecordId: EmailHealthCheckRecordId <- emailSettingHealthCheckTrackInternal(
        emailAccountForHealthCheck = emailAccountForHealthCheck,
        emailHealthCheckRecordId = emailHealthCheckRecordId,
      )
        .recoverWith { e =>

          Logger.error(
            s"emailSettingHealthCheckTrack failed. emailAccountId: ${emailAccountForHealthCheck.id} :: email: ${emailAccountForHealthCheck.email}",
            err = e,
          )

          EmailHealthCheckDAO.updateEmailHealthCheckResultOnFailure(
            accountId = emailAccountForHealthCheck.account_id,
            emailHealthCheckRecordId = emailHealthCheckRecordId,
          )

          Future.failed(e)

        }

    } yield {

      updatedEmailHealthCheckRecordId

    }

  }

  def createEmailSettingHealthCheck(
    emailAccountForHealthCheck: EmailAccount,
  )(
    implicit ec: ExecutionContext,
    wSClient: WSClient,
    system: ActorSystem,
    Logger: SRLogger,
  ): Future[EmailHealthCheckRecordId] = {

    val receiverEmailAccountId = AppConfig.EmailHealthCheck.getReceiverEmailSettingIdForEmailHealthCheck

    for {

      receiverEmailAccount: EmailAccount <- EmailAccount.find(id = receiverEmailAccountId) match {

        case Failure(exception) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to find receiverEmailAccount for email health check. receiverEmailAccountId: $receiverEmailAccountId :: emailAccountIdForHealthCheck: ${emailAccountForHealthCheck.id} :: email: ${emailAccountForHealthCheck.email} :: accountId: ${emailAccountForHealthCheck.account_id}",
            err = Some(exception),
          )

          Future.failed(exception)

        case Success(receiverEmailAccount) =>

          Future.successful(receiverEmailAccount)

      }

      sendSettings: EmailSettings.EmailSendSettings = emailAccountForHealthCheck.settings match {

        case es: EmailSettings.SmtpImapSettings =>

          es.smtp_settings

        case es: EmailSettings.OAuthTokens =>

          es

      }

      emailSendDetails: EmailSendDetail = EmailSendDetail(
        id = 0, // We are not creating an entry in emails_scheduled

        subject = AppConfig.EmailHealthCheck.emailHealthCheckSubject,
        body = EmailHelper.makeHTMLBody(
          baseBody = AppConfig.EmailHealthCheck.emailHealthCheckTextBody,
          salutation = "",
          signature = "",
        ),
        text_body = AppConfig.EmailHealthCheck.emailHealthCheckTextBody,

        is_reply = false,

        account_id = emailAccountForHealthCheck.account_id,
        sender_email_account_id = emailAccountForHealthCheck.id,
        service_provider = emailAccountForHealthCheck.service_provider,

        send_settings = sendSettings,

        from_email = emailAccountForHealthCheck.email,
        from_name = emailAccountForHealthCheck.sender_name,

        receiver_email_account_id = receiverEmailAccount.id,

        to_emails = Seq(
          IEmailAddress(
            email = receiverEmailAccount.email,
            name = Some(receiverEmailAccount.sender_name)
          )
        ),

        message_id_suffix = emailAccountForHealthCheck.message_id_suffix,

        in_reply_to_header = None,
        references_header = None,

        sender_email_setting_paused_till = emailAccountForHealthCheck.paused_till,

        reply_to_email = None,
        reply_to_name = None,

      )

      _: SendEmailResponse <- EmailSendingService.sendEmailToProspect(
        data = emailSendDetails,
      )

      emailHealthCheckRecordId: EmailHealthCheckRecordId <- Future.fromTry {

        EmailHealthCheckDAO.createPendingEmailHealthCheckRecord(
          accountId = emailAccountForHealthCheck.account_id,
          emailAccountIdForHealthCheck = emailAccountForHealthCheck.id,
        )

      }

    } yield {

      emailHealthCheckRecordId

    }

  }

  def createdPendingEmailHealthCheckRecord(
    emailAccountId: EmailAccountId,
  )(
    implicit ec: ExecutionContext,
    ws: WSClient,
    system: ActorSystem,
    Logger: SRLogger,
  ): Future[EmailHealthCheckRecord] = {

    for {

      ea: EmailAccount <- Future.fromTry {
        EmailAccount.find(id = emailAccountId.id) // TODO: FIXME - VALUE CLASS
      }

      createdEmailHealthCheckRecordId: EmailHealthCheckRecordId <-
        EmailHealthCheckService.createEmailSettingHealthCheck(
          emailAccountForHealthCheck = ea,
        )

      createdEmailHealthCheckRecord: EmailHealthCheckRecord <- EmailHealthCheckDAO.getEmailHealthCheckRecord(
        id = createdEmailHealthCheckRecordId,
      ) match {

        case Failure(exception) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email health check record. emailAccountId: $emailAccountId :: emailHealthCheckRecordId: $createdEmailHealthCheckRecordId",
            err = Some(exception),
          )

          Future.failed(exception)

        case Success(None) =>

          Logger.shouldNeverHappen(
            msg = s"Failed to get email health check record - None. emailAccountId: $emailAccountId :: emailHealthCheckRecordId: $createdEmailHealthCheckRecordId",
          )

          Future.failed(new Exception("Email health check record not found"))

        case Success(Some(emailHealthCheckRecord)) =>

          Future.successful(emailHealthCheckRecord)

      }

    } yield {

      createdEmailHealthCheckRecord

    }

  }

  def checkEmailHealthAndCanSendEmail(
    emailAccountId: EmailAccountId,
    accountId: AccountId,
  )(
    implicit ec: ExecutionContext,
    ws: WSClient,
    system: ActorSystem,
    Logger: SRLogger,
  ): Future[Boolean] = {

    for {

      emailHealthCheckRecordOpt: Option[EmailHealthCheckRecord] <- Future.fromTry {

        EmailHealthCheckDAO.getEmailHealthCheckRecord(
          emailAccountId = emailAccountId,
          accountId = accountId,
        )

      }

      emailHealthCheckRecord: EmailHealthCheckRecord <- emailHealthCheckRecordOpt match {

        case None =>

          // If the email health check record does not exist, we create a new one.

          createdPendingEmailHealthCheckRecord(
            emailAccountId = emailAccountId,
          )

        case Some(emailHealthCheckRecord) =>

          if (emailHealthCheckRecord.updated_at.isBefore(DateTime.now.minusDays(15))) {

            // If the email health check record is older than 15 days, we check again.

            createdPendingEmailHealthCheckRecord(
              emailAccountId = emailAccountId,
            )

          } else {

            Future.successful(emailHealthCheckRecord)

          }

      }

      canSend = emailHealthCheckRecord.status match {

        case EmailHealthCheckStatus.Pending
             | EmailHealthCheckStatus.Queued =>

          // Cannot send email if health check is not completed.

          false

        case EmailHealthCheckStatus.Failed =>

          // If health check has failed, then pause the email account with error.

          // TODO: Revert when significant number of emails in WH network have passed the health check.

          //          EmailAccountErrorHandlingService.handleEmailHealthCheckError(
          //            emailAccountId = emailAccountId,
          //          ) match {
          //
          //            case Failure(exception) =>
          //
          //              Logger.shouldNeverHappen(
          //                msg = s"Failed to handleEmailHealthCheckError. emailAccountId: $emailAccountId",
          //                err = Some(exception),
          //              )
          //
          //              false
          //
          //            case Success(_) =>
          //
          //              false
          //
          //          }

          true

        case EmailHealthCheckStatus.Completed =>

          val isEmailHealthy = emailHealthCheckRecord.dkim_auth_status == EmailAuthStatus.Pass &&
            emailHealthCheckRecord.dkim_auth_status == EmailAuthStatus.Pass &&
            emailHealthCheckRecord.dkim_auth_status == EmailAuthStatus.Pass

          if (isEmailHealthy) {

            true

          } else {

            // If email is not healthy, then pause the email account with error.

            // TODO: Revert when significant number of emails in WH network have passed the health check.

            //            EmailAccountErrorHandlingService.handleEmailHealthCheckError(
            //              emailAccountId = emailAccountId,
            //            ) match {
            //
            //              case Failure(exception) =>
            //
            //                Logger.shouldNeverHappen(
            //                  msg = s"Failed to handleEmailHealthCheckError. emailAccountId: $emailAccountId",
            //                  err = Some(exception),
            //                )
            //
            //                false
            //
            //              case Success(_) =>
            //
            //                false
            //
            //            }

            true

          }

      }

    } yield {

      canSend

    }

  }

}
