package api.services

import api.AppConfig
import api.models.{SrEmailSettingsRes, UnauthorizedSrApiKeyException, WarmupHeroEmailSettingWithPasswordResponse}
import play.api.libs.json.{JsError, JsSuc<PERSON>}
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.concurrent.{ExecutionContext, Future}

object SREmailAccountsApi {

  def getSrEmailAccountForIntegration(
    email_setting_uuid: String,
    sr_api_key: String,
  )(
    implicit logger: SRLogger,
    ec: ExecutionContext,
    ws: WSClient,
  ): Future[WarmupHeroEmailSettingWithPasswordResponse] = {

    val srBaseDomain = AppConfig.srBaseDomain

    // add id
    val url = s"$srBaseDomain/api/v3/settings/warmuphero/email_settings/$email_setting_uuid"

    ws.url(url)
      .withHttpHeaders("X-API-KEY" -> sr_api_key)
      .get()
      .flatMap { response =>

        if (response.status == 401) {

          logger.error(
            msg = s"FATAL: Failed while getting email accounts 401 Unauthorized: ${response.body}"
          )

          Future.failed(UnauthorizedSrApiKeyException())

        } else if (response.status != 200) {

          logger.error(s"FATAL: Failed while getting SmartReach email accounts XYZ ${response.body}")

          Future.failed(new Exception(s"Failed while fetching SmartReach email account for integration"))

        } else {

          (response.json \ "data").validate[WarmupHeroEmailSettingWithPasswordResponse] match {

            case JsError(errors) =>

              logger.error(msg = s"Failed to parse SrEmailSettingsRes: $errors")

              Future.failed(new Exception(s"Failed to parse SrEmailSettingsRes"))

            case JsSuccess(srEmailSettingsRes, _) =>

              Future.successful(srEmailSettingsRes)

          }

        }

      }

  }

}
