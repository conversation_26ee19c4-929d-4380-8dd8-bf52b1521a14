package api.services

import akka.actor.ActorSystem
import api.{AppConfig, FreeEmailDomainList}
import api.models.{Account, AccountDB, BasicEmailAccountDetails, EmailAccount, EmailAccountForm, EmailAccountId, EmailAccountStatus, EmailAccountWarmupStatus, EmailOAuthAccountForm, EmailServiceProvider, EmailSettings, EmailTestForm, MsClientIdVersion, SrEmailSettingsRes, WarmupHeroEmailSettingResponse, WarmupHeroEmailSettingWithPasswordResponse}
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.api.microsoftOAuth.{EmailSettingUpdateAccessToken, MicrosoftOAuthSettings}
import io.smartreach.esp.utils.email.models.EmailReply
import org.joda.time.DateTime
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import utils.{<PERSON>ailValida<PERSON>, SRLogger}
import utils.email.{GenericEmailService, GmailService, OutlookApi, OutlookService, SendEmailResponse, TEmailService}

import javax.mail.Message
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

object EmailAccountService {

  def getMicrosoftOAuthSettingsForAccount(
    emailAccountId: EmailAccountId,
    isZapmailFlow: Boolean,
  ): MicrosoftOAuthSettings = {

    val emailAccountTry = EmailAccount.find(id = emailAccountId.id)

    val version = emailAccountTry
      .map(_.ms_client_id_version)
      .getOrElse(MsClientIdVersion.V1)

    AppConfig.getMicrosoftOAuthSettings(
      isZapmailFlow = isZapmailFlow,
      version = version,
    )

  }

  def testMicrosoftOAuthSettings(
    oauthForm: EmailOAuthAccountForm,
  )(
    implicit Logger: SRLogger,
    ec: ExecutionContext,
    ws: WSClient,
    system: ActorSystem,
  ): Future[true] = {

    val oauthTokens = EmailSettings.OAuthTokens(
      oauth2_refresh_token = oauthForm.oauth2_refresh_token,
      oauth2_access_token = oauthForm.oauth2_access_token,
      oauth2_access_token_expires_at = oauthForm.oauth2_access_token_expires_at
    )

    for {
      receiveMessage: Vector[EmailReply.OutlookReplyTrackedViaAPI] <- OutlookApi.fetchRecentMessagesForTestingImap(
        emailAfterDate = DateTime.now().minusMinutes(5),
        receiveCredentials = oauthTokens,
        Logger = Logger
      )

      sendEmail: SendEmailResponse <- OutlookApi.sendEmail(
        sendCredentials = oauthTokens,
        email = EmailAccount.getTestEmail(
          data = EmailTestForm(
            email = oauthForm.email, first_name = oauthForm.first_name, last_name = oauthForm.last_name
          ),
          accountEmail = oauthForm.email
        )
      )
    } yield true

  }

  def validateAndConnectMicrosoftOAuthSettings(
    parentEmailSettingForSaving: EmailOAuthAccountForm,
    account: Account,
  )(
    implicit Logger: SRLogger,
    ec: ExecutionContext,
    ws: WSClient,
    system: ActorSystem,
  ): Future[Either[String, EmailSettingId]] = {

    val (_, domain) = EmailValidator.getLowercasedNameAndDomainFromEmail(email = parentEmailSettingForSaving.email)
    val email = parentEmailSettingForSaving.email.trim.toLowerCase

    if (
      FreeEmailDomainList.isFreeEmailDomain(domain = domain.toLowerCase) &&
        !FreeEmailDomainList.isWhitelistedForSignup(email = email)
    ) {

      // TODO: append to logger in controller
      //  :: host: ${request.host} :: X-Real-IP: ${request.headers.get("X-Real-IP")} :: X-Forwarded-For : ${request.headers.get("X-Forwarded-For")} :: X-Forwarded-Proto: ${request.headers.get("X-Forwarded-Proto")}

      Logger.error(s"email account integration attempt blocked: BLACKLISTED_DOMAINS $domain :: ${parentEmailSettingForSaving.email}")

      Future.successful(Left("Please use your work email to warm up"))

    }
    else if (!EmailValidator.validateEmail(email)) {

      Future.successful(Left("Please send valid sending email"))
    }
    else {

      EmailAccountService.testMicrosoftOAuthSettings(
        oauthForm = parentEmailSettingForSaving
      ).map { _: true =>

        val updatedEmailAccessToken = EmailSettingUpdateAccessToken(
          oauth2_refresh_token = parentEmailSettingForSaving.oauth2_refresh_token,
          oauth2_access_token = parentEmailSettingForSaving.oauth2_access_token,
          oauth2_token_expires_in = parentEmailSettingForSaving.oauth2_token_expires_in,
          oauth2_access_token_expires_at = parentEmailSettingForSaving.oauth2_access_token_expires_at
        )

        EmailAccount.isEmailAccountInDB(
          email = email,
          serviceProvider = EmailServiceProvider.MICROSOFT_365_API,
        ) match {

          case Failure(exception) =>

            Logger.error(
              msg = s"Failed to find email account. email: $email",
              err = exception
            )

            Left("Failed to integrate email account.")

          case Success(Some(basicEmailAccountDetails)) =>

            basicEmailAccountDetails.accountIdOpt match {

              case None =>

                Logger.fatal(
                  msg = s"Email account does not belong to any user account. email: $email basicEmailAccountDetails: $basicEmailAccountDetails :: accountId: ${account.id}"
                )

                Left(
                  "Failed to connect your email, please retry or contact support."
                )

              case Some(accountId) =>

                if (accountId != account.id) {

                  Logger.info(
                    msg = s"Email account connected in this or another WarmupHero account. email: $email basicEmailAccountDetails: $basicEmailAccountDetails :: accountId: ${account.id}"
                  )

                  Left(
                    "This email is already connected in this or another WarmupHero account. An email can be connected only once in the WarmupHero network. Please contact our support if you have any questions."
                  )

                } else {

                  EmailAccount.updateAccessTokenAndRefreshToken(
                    emailAccountId = basicEmailAccountDetails.emailAccountId,
                    data = updatedEmailAccessToken
                  ) match {

                    case Success(Some(eid)) =>

                      // updating an email account needs init state to be false so that add signature popup will not appear

                      Right(eid)

                    case Success(None) =>

                      Logger.error(s"Failed to reconnect account for account_id : ${account.id}")

                      Left("Failed to reconnect account")

                    case Failure(e) =>

                      Logger.error(s"Failed to reconnect account for account_id :  ${account.id}", err = e)

                      Left(e.getMessage)

                  }

                }

            }

          case Success(None) =>

            if (account.total_allowed_warmup_emails <= account.total_active_warmup_emails) {

              Logger.error(s"[CreateAccount Error] : total_allowed_warmup_emails is less than or equal to total_active_warmup_emails , ${account.id}")

              Left("You have reached the email account limit on your plan. Please increase the number of seats to add more email accounts for warmup")

            } else {

              EmailAccount.createEmailOAuthAccount(
                accountId = account.id,
                data = parentEmailSettingForSaving,
                emailDomain = domain
              ) match {

                case Success(Some(eid)) =>

                  // creating an new email account needs init state to be true so that add signature popup will appear
                  Right(EmailSettingId(eid.id))

                case Success(None) =>

                  Logger.error(s"Failed to create account in db for account_id :  ${account.id}")

                  Left("Failed to create account")

                case Failure(e) =>

                  Logger.error(s"Failed to create account in db for account_id :  ${account.id}", err = e)

                  Left(e.getMessage)

              }

            }

        }

      }.recover {
        case e =>
          Logger.error(s"Unable to test send and receive messages for ${account.id}", err = e)
          Left(s"Unable to test send and receive messages")
      }

    }

  }

  def testEmailSetting(
    account: Account,
    data: EmailAccountForm,
  )(
    implicit Logger: SRLogger,
    ec: ExecutionContext,
    ws: WSClient,
    system: ActorSystem,
  ): Future[Boolean] = {

    val emailAfterDate = DateTime.now().minusMinutes(5)

    /* doing trim and toLower all login and host details */
    val updatedData = data.copy(
      email = data.email.trim.toLowerCase,

      smtp_username = data.smtp_username.trim.toLowerCase,
      smtp_host = data.smtp_host.trim.toLowerCase,


      imap_username = data.imap_username.trim.toLowerCase,
      imap_host = data.imap_host.trim.toLowerCase,
    )

    val emailTestForm = EmailTestForm(
      email = data.email.trim.toLowerCase,
      first_name = data.first_name,
      last_name = data.last_name
    )

    val emailToBeSent = EmailAccount.getTestEmail(
      data = emailTestForm,
      accountEmail = account.email
    )

    val emailService: TEmailService =
      if (updatedData.service_provider == EmailServiceProvider.GOOGLE_WORKSPACE) GmailService
      else if (updatedData.service_provider == EmailServiceProvider.MICROSOFT_365) OutlookService
      else GenericEmailService

    for {

      imapEmailSetting: EmailSettings.ImapEmailAccount <- Future.fromTry(EmailAccount.getImapEmailAccount(updatedData))

      messages: Vector[Message] <- emailService.fetchRecentMessagesForTestingImap(
        emailAfterDate = emailAfterDate,
        receiveCredentials = imapEmailSetting,
        Logger = Logger
      )

      smtpEmailSettings: EmailSettings.SmtpEmailAccount <- Future.fromTry(EmailAccount.getSMTPEmailAccount(emailAccount = updatedData))

      // return type is Unit. So, not adding explicit return type.
      sendEmail <- emailService.sendEmail(sendCredentials = smtpEmailSettings, email = emailToBeSent)

    } yield true


  }

  def activateOrConnectEmailAccount(
    email_setting_uuid: String,
    sr_api_key: String,
    account: Account,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
    actorSystem: ActorSystem,
  ): Future[Either[String, EmailAccountWarmupStatus]] = {

    for {

      emailAccount: WarmupHeroEmailSettingWithPasswordResponse <-
        SREmailAccountsApi.getSrEmailAccountForIntegration(
          email_setting_uuid = email_setting_uuid,
          sr_api_key = sr_api_key
        )

      findExistingEmailAccount: Option[BasicEmailAccountDetails] <- Future.fromTry {

        EmailAccount.isEmailAccountInDB(
          email = emailAccount.email,
          serviceProvider = emailAccount.service_provider,
        )

      }

      activatedEmailAccount: Either[String, EmailAccount] <- findExistingEmailAccount match {

        case None =>

          // Does not exist - connect

          connectSrEmailAccount(
            email_setting_uuid = email_setting_uuid,
            sr_api_key = sr_api_key,
            account = account,
          )

        case Some(existingEmailAccount) =>

          // Already exists - activate

          Future.fromTry {

            EmailAccount.activateOrDeactivateEmailAccount(
              accountId = account.id,
              emailAccountIds = List(existingEmailAccount.emailAccountId),
              active = true,
            ).map(_.find(_.id == existingEmailAccount.emailAccountId)).flatMap {

              case None =>

                Logger.shouldNeverHappen(
                  msg = s"Email account activation failed. emailAccountId: ${existingEmailAccount.emailAccountId} :: email: ${emailAccount.email} :: serviceProvider: ${emailAccount.service_provider}",
                  err = None
                )

                Failure(new Exception("Failed to activate email account"))

              case Some(ea) =>

                EmailAccount.clearError(
                  emailAccountIds = List(ea.id),
                  accountId = account.id,
                ).map(_.find(eaId => eaId == ea.id)).flatMap {

                  case None =>

                    Logger.shouldNeverHappen(
                      msg = s"Email account clear error failed. emailAccountId: ${ea.id} :: email: ${emailAccount.email} :: serviceProvider: ${emailAccount.service_provider}",
                      err = None,
                    )

                    Failure(new Exception("Failed to clear error"))

                  case Some(_) =>

                    Success(Right(ea))

                }

            }

          }

      }

      activatedEmailAccountStatus: Either[String, EmailAccountWarmupStatus] = activatedEmailAccount.map { ea =>

        EmailAccountWarmupStatus(
          status = EmailAccountStatus.RUNNING, // The email account was connected or activated.
          email = ea.email,
          service_provider = ea.service_provider
        )

      }

    } yield {

      activatedEmailAccountStatus

    }

  }

  def connectSrEmailAccount(
    email_setting_uuid: String,
    sr_api_key: String,
    account: Account,
  )(
    implicit Logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext,
    actorSystem: ActorSystem,
  ): Future[Either[String, EmailAccount]] = {

    for {

      emailAccount: WarmupHeroEmailSettingWithPasswordResponse <-
        SREmailAccountsApi.getSrEmailAccountForIntegration(
          email_setting_uuid = email_setting_uuid,
          sr_api_key = sr_api_key
        )

      eaf: EmailAccountForm <- if (!emailAccount.can_auto_connect) {

        Logger.shouldNeverHappen(
          msg = s"Attempt to auto connect email which cannot be auto connected. emailToBeConnected: ${emailAccount.email} :: emailSettingUuid: ${emailAccount.id}:: accountId: ${account.id} :: accountEmail: ${account.email}",
          err = None,
        )

        Future.failed(new Exception("Cannot auto connect email"))

      } else if (
        emailAccount.smtp_host.isEmpty ||
          emailAccount.smtp_port.isEmpty ||
          emailAccount.smtp_username.isEmpty ||
          emailAccount.smtp_password.isEmpty ||

          emailAccount.imap_host.isEmpty ||
          emailAccount.imap_port.isEmpty ||
          emailAccount.imap_username.isEmpty ||
          emailAccount.imap_password.isEmpty
      ) {

        Logger.error(
          msg = s"Attempt to auto connect email with empty credentials. emailToBeConnected: ${emailAccount.email} :: emailSettingUuid: ${emailAccount.id}:: accountId: ${account.id} :: accountEmail: ${account.email}",
        )

        Future.failed(new Exception("Cannot auto connect email with empty credentials"))

      } else {

        Future.successful {

          EmailAccountForm(
            email = emailAccount.email,

            first_name = emailAccount.first_name,
            last_name = emailAccount.last_name,

            service_provider = emailAccount.service_provider,

            // Note: .get is only safe because we are checking above.
            smtp_host = emailAccount.smtp_host.get,
            smtp_port = emailAccount.smtp_port.get,
            smtp_username = emailAccount.smtp_username.get,
            smtp_password = emailAccount.smtp_password.get,

            imap_host = emailAccount.imap_host.get,
            imap_port = emailAccount.imap_port.get,
            imap_username = emailAccount.imap_username.get,
            imap_password = emailAccount.imap_password.get,
          )

        }

      }

      // Convert this to either - if failed ask the user to reconnect their email in SR and try again.
      testRes: Either[String, Boolean] <- EmailAccountService.testEmailSetting(
        account = account,
        data = eaf,
      ).map { res =>

        Right(res)

      }.recover { e =>

        // If testEmailSetting fails, ask the user to reconnect their email account in SR
        // and try again.

        Logger.error(
          msg = s"Email setting test failed. accountId: ${account.id} :: accountEmail: ${account.email} :: emailToBeConnected: ${eaf.email}",
          err = e,
        )

        Left(
          "Failed to connect email account, please reconnect the email account in SmartReach and try again."
        )

      }

      createdAccount: Either[String, EmailAccount] <- Future.fromTry {

        testRes match {

          case Left(errMsg) =>

            Success(Left(errMsg))

          case Right(_) =>

            createEmailAccount(
              account = account,
              emailAccountForm = eaf,
              isPurchasedFromSr = emailAccount.is_purchased_from_sr,
            )

        }


      }

    } yield {

      createdAccount

    }

  }

  def getEmailAccountsFromSR(
    accountId: Long,
    sr_api_key: String,
    older_than: Option[Long],
    newer_than: Option[Long],
  )(
    implicit logger: SRLogger,
    ws: WSClient,
    ec: ExecutionContext
  ): Future[SrEmailSettingsRes] = {

    for {

      // Email accounts from SmartReach organization
      srEmailSettingsRes: SrEmailSettingsRes <- AccountDB.getEmailAccountsFromSR(
        sr_api_key = sr_api_key,
        older_than = older_than,
        newer_than = newer_than
      )


      // Integrated email accounts in WarmupHero account with same email address
      exisistingEmailsInWarmupHeroWithSameAddress: List[EmailAccount] <- Future.fromTry {

        EmailAccount.findAllByEmail(
          accountId = accountId,
          emails = srEmailSettingsRes.email_settings.map(_.email)
        )

      }

      emailSettingsNotIntegratedInWH: List[WarmupHeroEmailSettingResponse] =
        srEmailSettingsRes.email_settings
          .filter { emailSettingFromSR =>

            // Check if email account fetched from SmartReach
            // exists in users WarmupHero account.
            val emailSettingFromSRExistsInWH =
              exisistingEmailsInWarmupHeroWithSameAddress.exists { existingEmailAccount =>

                existingEmailAccount.settings match {

                  case settings: EmailSettings.SmtpImapSettings =>

                    emailSettingFromSR.email == existingEmailAccount.email &&
                      emailSettingFromSR.smtp_host.contains(settings.smtp_settings.smtp_host) &&
                      emailSettingFromSR.imap_host.contains(settings.imap_settings.imap_host)

                  case _: EmailSettings.OAuthTokens =>

                    emailSettingFromSR.email == existingEmailAccount.email &&
                      emailSettingFromSR.service_provider == existingEmailAccount.service_provider

                }

              }

            // Keep the email account from Smartreach
            // if it does not exist in WarmupHero.
            !emailSettingFromSRExistsInWH

          }

    } yield {

      srEmailSettingsRes.copy(
        email_settings = emailSettingsNotIntegratedInWH
      )

    }

  }

  def createEmailAccount(
    account: Account,
    emailAccountForm: EmailAccountForm,
    isPurchasedFromSr: Boolean,
  )(
    implicit Logger: SRLogger,
  ): Try[Either[String, EmailAccount]] = {

    if (
      !isPurchasedFromSr &&
        account.total_allowed_warmup_emails <= account.total_active_warmup_emails
    ) {

      Logger.error(
        msg = s"createEmailAccount: total_allowed_warmup_emails is less than or equal to total_active_warmup_emails , ${account.id}"
      )

      Success(
        Left(
          "You have reached the email account limit on your plan. Please increase the number of seats to add more email accounts for warmup"
        )
      )

    } else {

      val accountId = account.id

      val (_, domainNotTrimmed) = EmailValidator.getLowercasedNameAndDomainFromEmail(email = emailAccountForm.email)

      val domain = domainNotTrimmed.toLowerCase.trim

      val email = emailAccountForm.email.trim

      EmailAccount.isEmailAccountInDB(
        email = email,
        serviceProvider = emailAccountForm.service_provider
      ) match {

        case Failure(exception) =>

          Logger.error(
            msg = s"isEmailAccountInDB - failed. email: $email :: serviceProvider: ${emailAccountForm.service_provider}",
            err = exception,
          )

          Failure(exception)

        case Success(Some(basicEmailAccountDetails)) =>

          basicEmailAccountDetails.accountIdOpt match {

            case None =>

              Logger.fatal(
                msg = s"Email account does not belong to any user account. email: $email basicEmailAccountDetails: $basicEmailAccountDetails :: accountId: ${account.id}"
              )

              Success(
                Left("Failed to connect your email, please retry or contact support.")
              )

            case Some(accountId) =>

              if (accountId != account.id) {

                Logger.info(
                  msg = s"Email account connected in this WarmupHero account. email: $email basicEmailAccountDetails: $basicEmailAccountDetails :: accountId: ${account.id}"
                )

                Success(
                  Left(
                    "This email is already connected in this or another WarmupHero account. An email can be connected only once in the WarmupHero network. Please contact our support if you have any questions."
                  )
                )

              } else {

                Success(
                  Left(
                    "This email is already connected in this WarmupHero account. An email can be connected only once in the WarmupHero network. Please contact our support if you have any questions."
                  )
                )

              }

          }

        case Success(None) =>

          EmailAccount.createEmailAccount(
            accountId = accountId,
            data = emailAccountForm,
            emailDomain = domain,
            isPurchasedFromSr = isPurchasedFromSr,
          ) match {

            case Failure(e) => e.getMessage match {

              case msg if msg.contains("email_accounts_account_id_email_key") =>

                Success(Left(s"You have already added ${emailAccountForm.email} to your account"))

              case _ =>

                Logger.error(
                  msg = s"createEmailAccount failed. accountId: $accountId :: emailToBeConnected: ${emailAccountForm.email} :: domain: $domain",
                  err = e,
                )

                Failure(e)

            }

            case Success(None) =>

              Logger.error(
                msg = s"createEmailAccount failed - None. accountId: $accountId :: emailToBeConnected: ${emailAccountForm.email} :: domain: $domain",
              )

              Failure(new Exception("Failed to create email account"))

            case Success(Some(row)) =>

              Success(Right(row))

          }

      }

    }

  }


  def findEmailAccountWarmupStatus(
    whAccountApiKey: String,
    accountId: Long,
    emails: List[String],
  ): Try[List[EmailAccountWarmupStatus]] = {

    EmailAccount.findEmailAccountWarmupStatus(
      apiKey = whAccountApiKey,
      accountId = accountId,
      emails = emails,
    )

  }

  def hasEmailAccounts(
    accountId: Long,
    emailAccountIds: List[Long],
  )(
    implicit Logger: SRLogger,
  ): Try[Boolean] = {

    EmailAccount.findEmailAccounts(
      accountId = accountId,
      emailAccountIds = emailAccountIds,
    ).map { emailAccounts =>

      val hasAllEmailAccounts = emailAccounts.length == emailAccountIds.length &&
        emailAccounts.forall(_.account_id == accountId)

      if (!hasAllEmailAccounts) {

        Logger.error(
          msg = s"hasEmailAccounts: false :: accountId: $accountId :: emailAccountIds: $emailAccountIds :: emailAccounts: ${emailAccounts.map(_.id)}",
        )

      }

      hasAllEmailAccounts

    }

  }

}
