
import play.api.http.HttpErrorHandler
import play.api.mvc._
import play.api.mvc.Results._

import scala.concurrent._
import javax.inject.Singleton
import api.{ErrorBody, ErrorResponse, ErrorType, SRAPIResponse}
import play.api.libs.json.Json
import utils.{SRLogger, StringUtils}

@Singleton
class AppErrorHandler extends HttpErrorHandler {

  def onClientError(request: RequestHeader, statusCode: Int, message: String) = {
    Future.successful {

      val msg = Json.toJson(
        ErrorResponse(message = "A client error occurred: " + message, data = ErrorBody(error_type = ErrorType.API_ERROR, param = None, error_code = None))
      )

      Status(statusCode)(msg)

    }
  }

  def onServerError(request: RequestHeader, exception: Throwable) = {

    val logRequestId = s" ${StringUtils.genLogTraceId} onServerError: request: ${request.method} ${request.path} ${request.rawQueryString}"
    val Logger = new SRLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)

    Future.successful {


      Res.ServerError(message = "A server error occurred: " + exception.getMessage, e = Some(exception))

      //      InternalServerError("A server error occurred: " + exception.getMessage)
    }
  }
}
