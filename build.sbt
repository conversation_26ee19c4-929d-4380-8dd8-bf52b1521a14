val sr_logger_resolver = "sr-logger" at "artifactregistry://us-central1-maven.pkg.dev/smrtcloud/sr-logger/"
val sr_email_service_providers_resolver = "sr-email-service-providers" at "artifactregistry://us-central1-maven.pkg.dev/smrtcloud/sr-email-service-providers"
val sr_llm_service_resolver = "sr-llm-service" at "artifactregistry://us-central1-maven.pkg.dev/smrtcloud/sr-llm-service"

val sr_logger_dependency = "com.smartreach" %% "sr_logger" % "1.0.1"
val sr_email_service_providers_dependency = "com.smartreach" %% "sr_email_service_providers" % "1.0.1"
val sr_llm_service_dependency = "com.smartreach" %% "sr_llm_service" % "3.2.3"

name := "SRWarmup"

version := "1.0"

lazy val `srwarmup` = (project in file(".")).enablePlugins(PlayScala)

scalaVersion := "2.13.12"

routesGenerator := InjectedRoutesGenerator


resolvers ++= Seq(
//  "Scalaz Bintray Repo" at "https://dl.bintray.com/scalaz/releases",
//  "Atlassian Releases" at "https://maven.atlassian.com/public/"



  // REF: https://github.com/playframework/play-plugins/tree/master/redis#how-to-install
  "google-sedis-fix" at "https://pk11-scratch.googlecode.com/svn/trunk",
  sr_email_service_providers_resolver,
  sr_logger_resolver,
  sr_llm_service_resolver,
)


libraryDependencies ++= Seq(
  jdbc,
  cacheApi,
  ws,

  // https://github.com/xdotai/play-json-extensions
  // For more than 22 fields in json case class
  "ai.x" %% "play-json-extensions" % "0.40.2",

  "com.sun.mail" % "jakarta.mail" % "1.6.7",

  //  REF: http://scandilabs.com/technology/knowledge/How_to_search_gmail_accounts_via_JavaMail_and_IMAP
  ("com.sun.mail" % "gimap" % "1.6.7"),

  "com.typesafe.play" %% "play-json" % "2.10.0-RC7",
  "com.typesafe.play" %% "play-json-joda" % "2.10.0-RC7",

  "com.rabbitmq" % "amqp-client" % "4.0.0",

  "org.jsoup" % "jsoup" % "1.10.2",
  // "commons-io" % "commons-io" % "2.5",

  evolutions,

  filters,

  // scala test dependencies - copied from coldemail dependencies
  "org.scalatest" %% "scalatest" % "3.2.18" % "test",
  "org.scalatest" %% "scalatest-flatspec" % "3.2.18" % "test",
  "org.scalamock" %% "scalamock" % "6.0.0" % Test,

  "org.postgresql" % "postgresql" % "42.2.5",


  "ch.qos.logback" % "logback-classic" % "1.1.8",

  "org.scalikejdbc" %% "scalikejdbc" % "3.3.5",

  "org.scalikejdbc" %% "scalikejdbc-config" % "3.3.5",

  "org.scalikejdbc" %% "scalikejdbc-joda-time" % "3.3.5",

  "org.scalikejdbc" %% "scalikejdbc-play-initializer" % "2.8.0-scalikejdbc-3.5",

  "com.github.karelcemus" %% "play-redis" % "2.7.0",

  "com.softwaremill.macwire" %% "macros" % "2.5.8" % "provided",

//  "org.logback-extensions" % "logback-ext-loggly" % "0.1.5",

  // used for hashing
  "de.svenkubiak" % "jBCrypt" % "0.4.1",


  // https://mvnrepository.com/artifact/commons-validator/commons-validator
  // used for email format validation
  "commons-validator" % "commons-validator" % "1.6",

  "com.iheart" %% "ficus" % "1.4.7",

  // "com.lihaoyi" %% "pprint" % "0.5.9"

  // https://mvnrepository.com/artifact/commons-validator/commons-validator
  // "commons-validator" % "commons-validator" % "1.6"

  // dependencies for google cloud logging
  // REF: https://cloud.google.com/logging/docs/setup/java
  "com.google.cloud" % "google-cloud-logging-logback" % "0.123.4-alpha",
  "com.google.cloud" % "google-cloud-logging" % "3.6.4",
  sr_email_service_providers_dependency,
  sr_logger_dependency,
  sr_llm_service_dependency,

).map(_.exclude("commons-logging", "commons-logging"))

// REF (override configuration in test environment): http://stackoverflow.com/a/42637619
javaOptions in Test += "-Dconfig.file=conf/application.test.conf"

// ignore tests in assembly (CI)
test in assembly := {}

fork in run := false

// Merge strategy
//
// REF:
// 1. http://queirozf.com/entries/creating-scala-fat-jars-for-spark-on-sbt-with-sbt-assembly-plugin
// 2. http://stackoverflow.com/a/34639471
assemblyMergeStrategy in assembly := {

  //  case PathList("META-INF", xs@_*) => MergeStrategy.discard

  /**
    * Added on 24th Aug 2024, to fix the following error that was showing up:
    *
    * [error] 1 error was encountered during merge
    * [error] java.lang.RuntimeException: deduplicate: different file contents found in the following:
    * [error] /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.14.3/jackson-core-2.14.3.jar:META-INF/FastDoubleParser-LICENSE
    * [error] /Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/software/amazon/awssdk/third-party-jackson-core/2.26.22/third-party-jackson-core-2.26.22.jar:META-INF/FastDoubleParser-LICENSE
    */
  case x if x.endsWith("META-INF/FastDoubleParser-LICENSE") => MergeStrategy.last

  /**
    * 19th October 2024:
    * clashing during assembly:
    *
    * [error] 1 error(s) were encountered during the merge:
    * [error] java.lang.RuntimeException:
    * [error] Deduplicate found different file contents in the following:
    * [error]   Jar name = gimap-1.6.7.jar, jar org = com.sun.mail, entry target = META-INF/javamail.providers
    * [error]   Jar name = imap-1.6.7.jar, jar org = com.sun.mail, entry target = META-INF/javamail.providers
    */
  case x if x.endsWith("META-INF/javamail.providers") => MergeStrategy.last

  case x if x.endsWith(".properties") => MergeStrategy.last
  case x if x.endsWith("reference-overrides.conf") => MergeStrategy.concat
  case x if x.endsWith("module-info.class") => MergeStrategy.discard

  case x if (
    // x.startsWith("com/typesafe/play") && // was not working on this check
    x.contains("play") && // was not working on this check
      (
        x.endsWith("package$.class") ||
          x.endsWith("package.class") ||
          x.endsWith("reference-overrides.conf")
        )
    ) => MergeStrategy.last

  case x if x.endsWith("mimetypes.default") => MergeStrategy.last

  case x if x.contains("google/protobuf") => MergeStrategy.last

  case x if x.endsWith("/native-image/io.netty/transport/reflection-config.json") =>
    MergeStrategy.last

  case x if x.endsWith("mailcap.default") => MergeStrategy.last



  case PathList("javax", "activation", xs @ _*) => MergeStrategy.first
  case x =>
    val oldStrategy = (assemblyMergeStrategy in assembly).value
    oldStrategy(x)
}

// NOTE: https://gitlab.com/heaplabs/coldemail/issues/21
// unmanagedResourceDirectories in Compile += baseDirectory.value / "public"

// REF: https://github.com/sbt/sbt-assembly#assembly-task
// the Procfile runs the application from this jar
assemblyJarName in assembly := "srwarmup.jar"

assemblyOption in assembly := (assemblyOption in assembly).value.copy(cacheOutput = false)


//
scalacOptions ++= Seq(
  "-deprecation" // Emit warning and location for usages of deprecated APIs.
  , "-feature" // Emit warning and location for usages of features that should be imported explicitly.
  , "-unchecked" // Enable additional warnings where generated code depends on assumptions.
  , "-Xfatal-warnings" // Fail the compilation if there are any warnings.
  //  "-Xlint", // Enable recommended additional warnings.
  //  "-Ywarn-adapted-args", // Warn if an argument list is modified to match the receiver.
  //  "-Ywarn-dead-code", // Warn when dead code is identified.
  //  "-Ywarn-inaccessible", // Warn about inaccessible types in method signatures.
  //  "-Ywarn-nullary-override", // Warn when non-nullary overrides nullary, e.g. def foo() over def foo.
  //  "-Ywarn-numeric-widen" // Warn when numerics are widened.
)
//
libraryDependencies += "de.svenkubiak" % "jBCrypt" % "0.4.1"
