
logs
project/project
project/target
target
tmp
.history
dist
/.idea
/*.iml
/out
/.idea_modules
/.classpath
/.project
/RUNNING_PID
/.settings
/testing

build_scripts/
build_scripts.zip

profile-env.sh

# the create-package.sh script temporarily adds the files reqyured to create
# the zip for elastic beanstalk into this directory
package

# Logs
logs
*.log
.DS_Store

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/
# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directory
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git
node_modules

# Editor temporary files
*~
\#*#
.#*
*.orig
*.rej

# Typescript-related generated files
typings
client/**/*.js
client/**/*.js.map
test/**/*.js
test/**/*.js.map
server/**/*.js
server/**/*.js.map

# Project specific
server/cookies/*.json
serverBuild/cookies/*.json
wpstats.json
#local
app/testing
app/test_worksheets
app/Testfunctions.sc
todo.md
profile_env_*.sh
deploy_sched_*.sh

frontend/client/react-data-grid/*
!frontend/client/react-data-grid/packages/
!frontend/client/react-data-grid/packages/**/dist/
frontend/client/react-data-grid/packages/react-data-grid-examples/*
app/TestFeatures.sc
*.rg
*.class
sql/*.json



sr_logger/.bsp
sr_logger/.idea
sr_logger/project
